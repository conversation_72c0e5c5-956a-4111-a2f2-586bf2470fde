import {
  Activity,
  ActivityType,
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaDice, FaPlus, FaTrash, FaWandMagicSparkles } from "react-icons/fa6";
import { Dispatch, MouseEvent, SetStateAction, useState } from "react";
import { FaClock, FaEdit, FaFile } from "react-icons/fa";
import { getActivityTypeColor } from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/getActivityTypeColor.ts";
import titleize from "titleize";
import usePOST from "@/util/api/usePOST.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";
import GeneratingActivitiesOverlay from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/GeneratingActivitiesOverlay.tsx";

export default function ListView({
  entries,
  setEditID,
}: {
  entries: Activity[];
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    activity: { add },
    outline: { gradeLevel, keywords, title },
    goal: { entries: goals },
    term: { entries: terms },
  } = useEditorContext();
  const topRealm = useTopRealm();
  const POST = usePOST();
  const pushError = usePushError();

  const [generating, setGenerating] = useState<boolean>(false);

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    add();
  }

  async function handleGenerate(e: MouseEvent) {
    e.preventDefault();
    if (generating) return;

    const payload = {
      realmEID: topRealm.externalID,
      subjects: {
        topics: [
          ...goals.map((entry) => entry.description),
          ...terms.map((entry) => entry.word),
          ...keywords,
        ],
        target: {
          age: gradeLevel + 5,
          domain: title,
        },
      },
    };

    setGenerating(true);

    try {
      const { response, error, data } = await POST(
        "/v1/ai/curricula/activities/generate",
        {
          assertedRealmEidUrn: topRealm.eidURN,
          body: JSON.stringify(payload),
        }
      );

      if (error) {
        pushError(error.clientErrorDetail.userMessage);
        return;
      }

      if (!response.ok) {
        pushError(await response.text());
        return;
      }

      if (!data) {
        pushError("No data received from AI service");
        return;
      }

      // Parse response according to the API format used in ActivitiesSection
      const envelope: APIEnvelope<{
        concepts: [{ en_US: string; es_ES: string; fr_FR: string }];
      }> = JSON.parse(await data.text());

      const ids = entries.map((entry) => entry.id);

      // Create activities from the generated concepts
      for (const concept of envelope.data.concepts) {
        const newID = getNonCollidingID(ids);
        const tokens = concept.en_US.split(": ");

        add({
          id: newID,
          parentTopic: null, // Not associated with any specific topic in the activities view
          title: tokens[0] || concept.en_US,
          type: ActivityType.CREATION,
          description: tokens[1] || "AI-generated activity",
          duration: "Not Set",
          resources: [],
          deliverables: [],
        });

        ids.push(newID);
      }
    } catch (err) {
      pushError(
        "Failed to generate activities: " +
          (err instanceof Error ? err.message : String(err))
      );
    } finally {
      setGenerating(false);
    }
  }

  function handleCancelGeneration() {
    setGenerating(false);
  }

  return (
    <>
      <GeneratingActivitiesOverlay
        isVisible={generating}
        onCancel={handleCancelGeneration}
      />

      <div className="flex flex-col px-5 shrink-0 gap-3">
        {entries.map((activity, index) => (
          <Entry activity={activity} key={index} setEditID={setEditID} />
        ))}

        {/* Create New Activity Button */}
        <button
          onClick={handleClick}
          type="button"
          disabled={generating}
          className="h-16 btn btn-lg btn-primary btn-outline size-full rounded-lg flex items-center justify-center gap-3"
        >
          <FaPlus />
          <span>Create New Activity</span>
        </button>

        {/* AI Generate Activities Button */}
        <button
          onClick={handleGenerate}
          type="button"
          disabled={generating}
          className="h-16 btn btn-lg btn-secondary btn-outline size-full rounded-lg flex items-center justify-center gap-3"
        >
          {generating ? (
            <div className="loading loading-spinner loading-md" />
          ) : (
            <FaWandMagicSparkles />
          )}
          <span>
            {generating ? "Generating Activities..." : "AI Generate Activities"}
          </span>
        </button>
      </div>
    </>
  );
}

function Entry({
  activity,
  setEditID,
}: {
  activity: Activity;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    activity: { remove },
  } = useEditorContext();

  const [removeTimeout, setRemoveTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  function handleRemoveClick(e: MouseEvent) {
    e.preventDefault();
    if (removeTimeout !== null) {
      remove(activity.id);
      clearTimeout(removeTimeout);
      setRemoveTimeout(null);
    } else {
      setRemoveTimeout(
        setTimeout(() => {
          setRemoveTimeout(null);
        }, 1000)
      );
    }
  }

  function handleEditClick(e: MouseEvent) {
    e.preventDefault();
    setEditID(activity.id);
  }

  return (
    <div className="p-4 border border-neutral-300 rounded-lg flex flex-row gap-4">
      <FaDice className="size-6 shrink-0 text-neutral-600" />

      <div className="flex flex-col grow gap-3">
        <div className="flex flex-row gap-3 items-center">
          <span className="text-lg font-bold">{activity.title}</span>
          <div
            style={{ backgroundColor: getActivityTypeColor(activity.type) }}
            className="rounded-full px-3 py-1 text-sm font-medium text-white"
          >
            {titleize(activity.type)}
          </div>
          <div className="grow" />
          <div className="flex flex-row gap-1 items-center">
            <div className="tooltip" data-tip="Edit">
              <button
                type="button"
                onClick={handleEditClick}
                className="btn btn-square btn-sm text-base hover:btn-primary focus-visible:btn-primary hover:text-white focus-visible:text-white"
              >
                <FaEdit />
              </button>
            </div>

            <div
              className="tooltip"
              data-tip={removeTimeout ? "Click Again To Confirm" : "Remove"}
            >
              <button
                type="button"
                onClick={handleRemoveClick}
                className="btn btn-square btn-sm text-base hover:btn-error focus-visible:btn-error hover:text-white focus-visible:text-white"
              >
                <FaTrash />
              </button>
            </div>
          </div>
        </div>

        <div className="flex flex-row gap-4 items-center text-sm text-neutral-600">
          <div className="flex items-center gap-2">
            <FaClock />
            <span>{activity.duration}</span>
          </div>
          {activity.resources.length > 0 && (
            <div className="flex items-center gap-2">
              <FaFile />
              <span>
                {activity.resources.length} resource
                {activity.resources.length !== 1 ? "s" : ""}
              </span>
            </div>
          )}
        </div>

        <div className="text-neutral-700">{activity.description}</div>

        {activity.resources.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {activity.resources.map((resource, idx) => (
              <div
                key={idx}
                className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-sm"
              >
                {resource}
              </div>
            ))}
          </div>
        )}

        {activity.deliverables.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {activity.deliverables.map((deliverable, idx) => (
              <div
                key={idx}
                className="bg-green-50 text-green-700 px-2 py-1 rounded text-sm"
              >
                📋 {deliverable}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
