import {
  Dispatch,
  MouseEvent,
  ReactNode,
  SetStateAction,
  useEffect,
  useState,
} from "react";
import {
  Context,
  Realm,
  RealmDetails,
} from "@/root/narp/app/_components/Context";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import { useConfiguration } from "@/util/configuration/ConfigurationContext.tsx";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { useNavigate } from "react-router-dom";
import useSessionState from "@/util/hooks/useSessionState.tsx";

export default function Provider({ children }: { children: ReactNode }) {
  const configuration = useConfiguration();
  const { getSession, isAuthenticated, signOut } = useAuth();
  const pushError = usePushError();
  const navigate = useNavigate();
  const [topRealms, setTopRealms] = useState<Realm[] | null>(null);
  const [curTopRealm, setCurTopRealm] = useSessionState<Realm | null>(
    "/app/topRealm",
    null,
  );

  async function updateTopRealms() {
    if (!(await isAuthenticated())) return navigate("/narp/auth");

    const [session, error] = await getSession();
    if (error || !session) return pushError(error.message);

    const url = new URL("/v1/realms", configuration.apiBaseURL);
    const headers = new Headers();
    headers.set(
      "Authorization",
      `Bearer ${session.getAccessToken().getJwtToken()}`,
    );

    const response = await fetch(url, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) return pushError("failed to get top realms");

    const data: { realms: Realm[] } = await response.json();
    setTopRealms(data.realms ? data.realms : []);
  }

  function handleSignOut(e: MouseEvent) {
    e.preventDefault();
    signOut();
    navigate("/narp/auth");
  }

  useEffect(() => {
    updateTopRealms();
  }, []);

  if (!topRealms) {
    return (
      <div className="fixed w-screen h-screen flex items-center justify-center">
        <div className="loading loading-xl" />
      </div>
    );
  }

  if (topRealms.length <= 0) {
    return (
      <div className="fixed w-screen h-screen flex flex-col items-center justify-center p-5 text-center">
        <div className="text-xl font-bold text-center">No Realms Found.</div>
        <div className="flex flex-row">
          Contact your administrator for further instructions or contact
          support&nbsp;
          <a className="link link-info" href="mailto:<EMAIL>">
            here
          </a>
          .
        </div>
        <button
          onClick={handleSignOut}
          type="button"
          className="btn btn-error mt-10"
        >
          Sign Out
        </button>
      </div>
    );
  }

  if (!curTopRealm) {
    return (
      <div className="fixed w-screen h-screen flex flex-col items-center justify-center p-5">
        <div className="text-xl font-bold text-center">Select a Realm</div>
        <div className="flex flex-col gap-5 p-5 max-w-lg w-full">
          {topRealms.map((realm, i) => (
            <RealmCard realm={realm} setCurTopRealm={setCurTopRealm} key={i} />
          ))}
        </div>
      </div>
    );
  }

  function setTopRealm(index: number, redirect: boolean = true) {
    if (!topRealms) return pushError("missing top realms");

    const target = topRealms.at(index);
    if (!target) return pushError("realm index out of bounds");

    setCurTopRealm(target);
    if (redirect) navigate("/narp/app");
  }

  async function fetchRealmDetails(
    eid: string,
  ): Promise<[RealmDetails, null] | [null, Error]> {
    const [session, error] = await getSession();
    if (error || !session) return [null, error];

    const url = new URL(`/v1/realms/${eid}`, configuration.apiBaseURL);
    const headers = new Headers();
    headers.set(
      "Authorization",
      `Bearer ${session.getAccessToken().getJwtToken()}`,
    );

    const response = await fetch(url, {
      method: "GET",
      headers: headers,
    });

    if (!response.ok) {
      const error = await response.json();
      return [null, new Error(error.error.userMessage)];
    }

    return [await response.json(), null];
  }

  return (
    <Context.Provider
      value={{
        topRealms: topRealms,
        currentTopRealm: curTopRealm,
        setTopRealm: setTopRealm,
        fetchRealmDetails: fetchRealmDetails,
      }}
    >
      {children}
    </Context.Provider>
  );
}

function RealmCard({
  realm,
  setCurTopRealm,
}: {
  realm: Realm;
  setCurTopRealm: Dispatch<SetStateAction<Realm | null>>;
}) {
  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setCurTopRealm(realm);
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      className="p-5 bg-white border border-neutral-400 rounded-lg cursor-pointer transition-colors hover:bg-neutral-100"
    >
      <div className="text-xl font-bold">{realm.displayName}</div>
      <div className="text-sm text-neutral-400 flex items-center justify-center gap-1.5">
        <span>{realm.externalID}</span>
      </div>
    </button>
  );
}
