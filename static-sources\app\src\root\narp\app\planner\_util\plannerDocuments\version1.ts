import { ScopeAndSequenceDocument as Version0 } from "@/root/narp/app/planner/_util/plannerDocuments/version0.ts";
import { Content, TDocumentDefinitions } from "pdfmake/interfaces";

export enum GoalType {
  KnowledgeGoal,
  SkillGoal,
}

export enum GoalPriority {
  Optional,
  NiceToHave,
  MustHave,
}

export enum GradeLevel {
  PreKindergarten = -1,
  Kindergarten = 0,
  FirstGrade = 1,
  SecondGrade = 2,
  ThirdGrade = 3,
  FourthGrade = 4,
  FifthGrade = 5,
  SixthGrade = 6,
  SeventhGrade = 7,
  EightGrade = 8,
  NinthGrade = 9,
  TenthGrade = 10,
  EleventhGrade = 11,
  TwelfthGrade = 12,
}

export interface Goal {
  type: GoalType;
  description: string;
  priority: GoalPriority;
}

export enum LexicalCategories {
  Noun = "Noun",
  Verb = "Verb",
  Adjective = "Adjective",
  Adverb = "Adverb",
  Preposition = "Preposition",
  Pronoun = "Pronoun",
  Interjection = "Interjection",
  Conjunction = "Conjunction",
}
export type LC = keyof typeof LexicalCategories;

export interface Term {
  word: string;
  definition: string;
  lexicalCategory: LexicalCategories;
}

export interface Topic {
  name: string;

  goals: Goal[];
  terms: Term[];

  notes: string;
  essentialQuestions: string;
  resources: string;
  evidenceOfLearning: string;
}

export interface Outline {
  grade: GradeLevel;
  title: string;
  abstract: string;
  keywords: string[];
}

export interface ScopeAndSequenceDocument {
  manifest: "ver1";
  outline: Outline;
  topics: Topic[];
}

export function fromVersion0(doc: Version0): ScopeAndSequenceDocument {
  return {
    manifest: "ver1",
    outline: {
      title: doc.title,
      abstract: doc.abstract,
      keywords: doc.keywords,
      grade: doc.grade,
    },
    topics: doc.topics.map((entry) => {
      return {
        goals: entry.goals,
        name: entry.name,
        terms: [],
        notes: "",
        essentialQuestions: "",
        resources: "",
        evidenceOfLearning: "",
      };
    }),
  };
}

const gradeLevelLabels: { [grade: number]: string } = {
  [GradeLevel.Kindergarten]: "Kindergarten",
  [GradeLevel.FirstGrade]: "1st Grade",
  [GradeLevel.SecondGrade]: "2nd Grade",
  [GradeLevel.ThirdGrade]: "3rd Grade",
  [GradeLevel.FourthGrade]: "4th Grade",
  [GradeLevel.FifthGrade]: "5th Grade",
  [GradeLevel.SixthGrade]: "6th Grade",
  [GradeLevel.SeventhGrade]: "7th Grade",
  [GradeLevel.EightGrade]: "8th Grade",
  [GradeLevel.NinthGrade]: "9th Grade",
  [GradeLevel.TenthGrade]: "10th Grade",
  [GradeLevel.EleventhGrade]: "11th Grade",
  [GradeLevel.TwelfthGrade]: "12th Grade",
};

const goalPriorityLabels: { [priority: number]: string } = {
  [GoalPriority.Optional]: "Optional",
  [GoalPriority.NiceToHave]: "Nice-to-have",
  [GoalPriority.MustHave]: "Must Have",
};

const goalTypeLabels: { [type: number]: string } = {
  [GoalType.SkillGoal]: "Skill Goal",
  [GoalType.KnowledgeGoal]: "Knowledge Goal",
};

function generateGoalDefinition(goal: Goal): Content {
  return {
    stack: [
      { text: goal.description },
      {
        ul: [
          {
            text: goalPriorityLabels[goal.priority],
            italics: true,
            bold: true,
            color:
              goal.priority === GoalPriority.Optional
                ? "#00786f"
                : goal.priority === GoalPriority.NiceToHave
                  ? "#372aac"
                  : "#31156c",
            listType: "circle",
          },
          {
            text: goalTypeLabels[goal.type],
            italics: true,
            color: goal.type === GoalType.SkillGoal ? "#314158" : "#c6005c",
            listType: "circle",
          },
        ],
      },
    ],
    marginBottom: 5,
  };
}

function generateTermDefinition(term: Term): Content {
  return {
    text: [
      { text: term.word, italics: true },
      " : ",
      { text: term.definition },
    ],
  };
}

function generateTopicDefinition(topic: Topic): Content {
  return {
    stack: [
      { text: topic.name, style: "subheader" },
      topic.goals.length > 0
        ? {
            stack: [
              { text: "Goals", bold: true },
              {
                ul: [
                  ...topic.goals.map((goal) => generateGoalDefinition(goal)),
                ],
              },
            ],
            marginBottom: 5,
          }
        : "",
      topic.terms.length > 0
        ? {
            stack: [
              { text: "Vocabulary", bold: true },
              {
                ul: [
                  ...topic.terms.map((term) => generateTermDefinition(term)),
                ],
              },
            ],
            marginBottom: 5,
          }
        : "",
      topic.essentialQuestions
        ? {
            stack: [
              { text: "Essential Questions", bold: true },
              { text: topic.essentialQuestions, marginLeft: 10 },
            ],
            marginBottom: 5,
          }
        : "",
      topic.evidenceOfLearning
        ? {
            stack: [
              { text: "Evidence of Learning", bold: true },
              { text: topic.evidenceOfLearning, marginLeft: 10 },
            ],
            marginBottom: 5,
          }
        : "",
      topic.resources
        ? {
            stack: [
              { text: "Resources", bold: true },
              { text: topic.resources, marginLeft: 10 },
            ],
            marginBottom: 5,
          }
        : "",
      topic.notes
        ? {
            stack: [
              { text: "Notes", bold: true },
              { text: topic.notes, marginLeft: 10 },
            ],
            marginBottom: 5,
          }
        : "",
    ],
    marginBottom: 10,
  };
}

export function toPDFDefinition(
  doc: ScopeAndSequenceDocument,
): TDocumentDefinitions {
  return {
    content: [
      { text: doc.outline.title, style: "header" },
      { text: `manifest ${doc.manifest}`, italics: true, marginBottom: 10 },
      {
        text: [
          { text: "Grade Level: ", bold: true },
          `${gradeLevelLabels[doc.outline.grade]}`,
        ],
        marginBottom: 5,
      },
      { text: doc.outline.abstract, marginBottom: 10 },
      ...doc.topics.map((topic) => generateTopicDefinition(topic)),
    ],
    styles: {
      header: { fontSize: 14, bold: true },
      subheader: { fontSize: 12, bold: true },
    },
    defaultStyle: { fontSize: 10 },
    pageMargins: 72,
  };
}
