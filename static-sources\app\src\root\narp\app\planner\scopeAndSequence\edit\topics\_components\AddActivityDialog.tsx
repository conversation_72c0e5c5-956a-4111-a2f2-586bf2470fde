import {
  Dispatch,
  FormEvent,
  RefObject,
  SetStateAction,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  Activity,
  ActivityType,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaPlus, FaXmark } from "react-icons/fa6";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";

export interface AddActivityDialogRef {
  close: () => void;
  open: () => void;
  dialog: RefObject<HTMLDialogElement>;
}

export default function AddActivityDialog({
  ref,
  onClose,
  onOpen,
  onSubmit,
}: {
  ref: RefObject<AddActivityDialogRef>;
  onOpen?: () => void;
  onClose?: () => void;
  onSubmit?: (activity: Activity) => void;
}) {
  const {
    activity: { entries },
  } = useEditorContext();

  const dialogRef = useRef<HTMLDialogElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  const [value, setValue] = useState<Activity>({
    deliverables: [],
    description: "",
    duration: "",
    id: 0,
    parentTopic: null,
    resources: [],
    title: "",
    type: ActivityType.CREATION,
  });

  function open() {
    dialogRef.current?.showModal();
    formRef.current?.reset();
    onOpen && onOpen();
    setValue({
      deliverables: [],
      description: "",
      duration: "",
      id: getNonCollidingID(entries.map((entry) => entry.id)),
      parentTopic: null,
      resources: [],
      title: "",
      type: ActivityType.CREATION,
    });
  }

  function close() {
    dialogRef.current?.close();
    onClose && onClose();
  }

  function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    onSubmit && onSubmit(value);
    close();
  }

  useImperativeHandle(ref, () => {
    return {
      open: open,
      close: close,
      dialog: dialogRef,
    };
  });

  return (
    <dialog
      ref={dialogRef}
      className="not-open:hidden min-w-md m-auto bg-white border border-neutral-400 rounded-lg shadow overflow-visible"
      onClose={(e) => {
        e.preventDefault();
        close();
      }}
    >
      <form
        ref={formRef}
        onSubmit={handleSubmit}
        className="size-full flex flex-col gap-5 items-center p-5"
      >
        <div className="w-full flex flex-row items-center gap-3">
          <div className="grow basis-0">
            <div className="tooltip" data-tip={"Cancel"}>
              <button
                type="button"
                onClick={close}
                className="btn btn-error btn-square shrink-0"
              >
                <FaXmark />
              </button>
            </div>
          </div>
          <div className="text-xl font-bold">Add New Activity</div>
          <div className="grow basis-0"></div>
        </div>

        <label className="floating-label w-full">
          <span className="font-bold">Title</span>
          <input
            autoFocus
            value={value.title}
            name="type"
            type="text"
            autoComplete="off"
            onInput={(e) =>
              setValue({ ...value, title: e.currentTarget.value })
            }
            className="input input-lg w-full"
          />
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Type</span>
          <select
            onInput={(e) =>
              setValue({
                ...value,
                type: e.currentTarget.value as ActivityType,
              })
            }
            value={value.type}
            className="select select-lg w-full"
          >
            {Object.keys(ActivityType).map((type, index) => (
              <option value={type} key={index}>
                {type}
              </option>
            ))}
          </select>
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Description</span>
          <textarea
            onInput={(e) =>
              setValue({ ...value, description: e.currentTarget.value })
            }
            autoComplete="off"
            value={value.description}
            className="textarea textarea-lg w-full"
          />
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Duration</span>
          <input
            autoFocus
            value={value.duration}
            name="type"
            type="text"
            autoComplete="off"
            onInput={(e) =>
              setValue({ ...value, duration: e.currentTarget.value })
            }
            className="input input-lg w-full"
          />
        </label>

        <ResourcesField value={value} setValue={setValue} />
        <DeliverablesField value={value} setValue={setValue} />

        <button type="submit" className="btn btn-primary w-full">
          Create
        </button>
      </form>
    </dialog>
  );
}

function ResourcesField({
  value,
  setValue,
}: {
  value: Activity;
  setValue: Dispatch<SetStateAction<Activity>>;
}) {
  const [displayValue, setDisplayValue] = useState<string>("");

  function handleAdd() {
    if (!displayValue) return;
    value.resources.push(displayValue);
    setValue({ ...value });
    setDisplayValue("");
  }

  function handleRemove(index: number) {
    value.resources.splice(index, 1);
    setValue({ ...value });
    setDisplayValue("");
  }

  return (
    <div className="w-full flex flex-col">
      <label className="floating-label w-full">
        <span className="font-bold">Add Resource</span>
        <label className="input input-lg w-full flex flex-row gap-3 pe-0.5">
          <input
            autoFocus
            value={displayValue}
            name="type"
            type="text"
            autoComplete="off"
            className="grow"
            onInput={(e) => setDisplayValue(e.currentTarget.value)}
          />

          <button
            onClick={() => handleAdd()}
            type="button"
            className="btn btn-square btn-success"
          >
            <FaPlus />
          </button>
        </label>
      </label>

      <div className="flex flex-col pt-1 gap-1">
        {value.resources.map((resource, index) => (
          <div
            className="flex flex-row w-full gap-1 border border-neutral-200 rounded p-1 px-2"
            key={index}
          >
            <span className="grow truncate">{resource}</span>
            <button
              onClick={() => handleRemove(index)}
              type="button"
              className="btn btn-square btn-error btn-outline btn-xs"
            >
              <FaXmark />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}

function DeliverablesField({
  value,
  setValue,
}: {
  value: Activity;
  setValue: Dispatch<SetStateAction<Activity>>;
}) {
  const [displayValue, setDisplayValue] = useState<string>("");

  function handleAdd() {
    if (!displayValue) return;
    value.deliverables.push(displayValue);
    setValue({ ...value });
    setDisplayValue("");
  }

  function handleRemove(index: number) {
    value.deliverables.splice(index, 1);
    setValue({ ...value });
    setDisplayValue("");
  }

  return (
    <div className="w-full flex flex-col">
      <label className="floating-label w-full">
        <span className="font-bold">Add Deliverable</span>
        <label className="input input-lg w-full flex flex-row gap-3 pe-0.5">
          <input
            autoFocus
            value={displayValue}
            name="type"
            type="text"
            autoComplete="off"
            className="grow"
            onInput={(e) => setDisplayValue(e.currentTarget.value)}
          />

          <button
            onClick={() => handleAdd()}
            type="button"
            className="btn btn-square btn-success"
          >
            <FaPlus />
          </button>
        </label>
      </label>

      <div className="flex flex-col pt-1 gap-1">
        {value.deliverables.map((resource, index) => (
          <div
            className="flex flex-row w-full gap-1 border border-neutral-200 rounded p-1 px-2"
            key={index}
          >
            <span className="grow truncate">{resource}</span>
            <button
              onClick={() => handleRemove(index)}
              type="button"
              className="btn btn-square btn-error btn-outline btn-xs"
            >
              <FaXmark />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
