import { NavLink, useNavigate, useParams } from "react-router-dom";
import { FaLock } from "react-icons/fa6";
import { FormEvent, useState } from "react";
import { MFAStatus, useAuth } from "@/util/auth/AuthContext.tsx";

export function Component() {
  const { type } = useParams();
  const navigate = useNavigate();
  const { mfaTOTP, mfaSMS } = useAuth();

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const code = data.get("code")?.toString();

    if (code === undefined) return setError(new Error("Missing fields"));

    setLoading(true);
    const [status, error] =
      type?.toLowerCase() === "totp" ? await mfaTOTP(code) : await mfaSMS(code);
    setLoading(false);

    if (error) return setError(error);

    switch (status) {
      case MFAStatus.Success:
        return navigate("/narp/app");
      case MFAStatus.ConfirmRequired:
        return navigate("/narp/auth/verify");
      case MFAStatus.Error:
        return setError(new Error("Failed to submit mfa"));
      default:
        return setError(new Error("Invalid MFA status"));
    }
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5">
      <form
        onSubmit={handleSubmit}
        className="max-w-xs w-full p-5 flex flex-col gap-3"
      >
        <div className={"text-xl my-3 text-center font-bold"}>
          Multi Factor Authentication
        </div>

        <div className={"text-center"}>
          Check your authenticator app
          <br /> for MFA code
        </div>

        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>MFA Code</span>
            <FaLock className={"text-base-content/20"} />
            <input
              type={"text"}
              required
              name={"code"}
              maxLength={6}
              placeholder={"MFA Code"}
            />
          </label>
          <p className={"validator-hint hidden"}>Required</p>
        </fieldset>

        <button
          disabled={loading}
          type="submit"
          className={`btn ${error ? "btn-error" : "btn-primary"}`}
        >
          {loading ? <div className="loading" /> : <>Submit</>}
        </button>

        {error ? (
          <div className="text-error text-center">{error?.message}</div>
        ) : (
          <></>
        )}

        <div className="divider m-0">OR</div>

        <NavLink
          className="btn btn-secondary no-animation mb-1.5"
          to="/narp/auth"
        >
          Back to Sign In
        </NavLink>
      </form>
    </div>
  );
}
