import { useAuth } from "@/util/auth/AuthContext.tsx";
import { useEffect, useState } from "react";

// null before checking, true if authenticated, false if not authenticated
export function useAuthenticated() {
  const [authenticated, setAuthenticated] = useState<boolean | null>(null);

  const { isAuthenticated } = useAuth();

  useEffect(() => {
    isAuthenticated().then((authenticated) => {
      setAuthenticated(authenticated);
    });
  }, []);

  return authenticated;
}
