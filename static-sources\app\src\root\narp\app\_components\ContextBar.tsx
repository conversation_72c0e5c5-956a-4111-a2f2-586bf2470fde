import { useNavigate } from "react-router-dom";
import { FormEvent, MouseEvent } from "react";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import { useRealmContext } from "@/root/narp/app/_components/Context";
import { useLocalStorage } from "@/util/hooks/useLocalStorage";

export default function ContextBar({ open }: { open: boolean }) {
  return (
    <div
      className={`-z-10 transition-transform p-3 flex flex-col absolute right-0 top-0 h-full w-64 bg-white drop-shadow-lg ${open && "translate-x-full"}`}
    >
      <TopLevelRealmCard />
      <div className="divider divider-vertical my-1.5" />
      <div className="grow"></div>
      <div className="divider divider-vertical my-1.5" />
      <MiscellaneousInformation />
      <div className="divider divider-vertical my-1.5" />
      <SignOutButton />
    </div>
  );
}

function SignOutButton() {
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const [, setTermsAccepted] = useLocalStorage("terms_accepted", false);

  async function handleLogout(e: MouseEvent) {
    e.preventDefault();
    await signOut();
    setTermsAccepted(false);
    navigate("/narp/auth");
  }

  return (
    <button
      type="button"
      onClick={handleLogout}
      className="btn btn-error btn-sm"
    >
      Sign Out
    </button>
  );
}

function TopLevelRealmCard() {
  const { setTopRealm, currentTopRealm, topRealms } = useRealmContext();

  function handleInput(e: FormEvent<HTMLSelectElement>) {
    e.preventDefault();
    setTopRealm(parseInt(e.currentTarget.value), true);
  }

  const index = topRealms.findIndex(
    (value) => value.externalID === currentTopRealm.externalID,
  );

  return (
    <div className="flex flex-col gap-1">
      <div className="font-bold">Current Top Realm:</div>
      <select
        onInput={handleInput}
        autoComplete="off"
        className="select min-w-0 w-full"
        value={index}
      >
        {topRealms.map((realm, i) => (
          <option value={i} key={i}>
            {realm.displayName}
          </option>
        ))}
      </select>
    </div>
  );
}

function MiscellaneousInformation() {
  return (
    <div className="flex flex-col">
      <a
        href="https://discourse-apollo.www.codermerlin.academy/t/welcome-to-ayode-community-discussion-forums/7"
        className="link link-info"
      >
        Join Our Community
      </a>
      <a
        href="https://ayode-institute.github.io/ayode-design-documentation/legal"
        className="link link-info"
      >
        Legal
      </a>
      <a href="mailto:<EMAIL>" className="link link-info">
        <EMAIL>
      </a>

      <div className="text-base-content/25">
        {global.__APP_VERSION__ || "unknown version"}
      </div>
    </div>
  );
}
