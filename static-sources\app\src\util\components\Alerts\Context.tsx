import { createContext, useContext } from "react";

export enum AlertType {
  ERROR,
  INFO,
  SUCCESS,
  WARNING,
  NEUTRAL,
}

export interface Alert {
  type: AlertType;
  message: string;
  description: string | undefined;
  callToActionText: string | undefined;
  callToActionLink: string | undefined;
  variantColor: boolean; // Set to false automatically if not set
}

export interface Context {
  alerts: Alert[];
  activeAlert: Alert | null;
  pushAlert: (
    message: string,
    type?: AlertType,
    description?: string,
    callToActionLink?: string,
    callToActionText?: string,
    variantColor?: boolean
  ) => void;
  popAlert: () => Alert | null;
}

export const Context = createContext<Context>({} as Context);

export function useAlerts() {
  return useContext(Context);
}

export function usePushAlert() {
  return useContext(Context).pushAlert;
}
