import PageSkeleton from "@/util/components/PageSkeleton.tsx";
import { useAuthenticated } from "@/util/auth/useAuthenticated.tsx";
import { Navigate, Outlet } from "react-router-dom";
import { useLocalStorage } from "@/util/hooks/useLocalStorage";

export function Component() {
  const authenticated = useAuthenticated();
  const [termsAccepted] = useLocalStorage("terms_accepted", false);

  if (authenticated === null) return <PageSkeleton />;
  if (authenticated) {
    if (!termsAccepted) {
      return <Navigate to="/narp/app/terms" />;
    }
    return <Navigate to="/narp/app" />;
  }
  return (
    <>
      <Outlet />
      <div className="fixed bottom-5 left-5 text-base-content/20">
        {global.__APP_VERSION__ || "unknown version"}
      </div>
    </>
  );
}
