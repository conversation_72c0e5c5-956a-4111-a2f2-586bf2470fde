@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: "Lato", sans-serif;
}

@layer base {
  /* Font Sizes */
  :root {
    --text-font-size-50: 12px;
    --text-font-size-75: 14px;
    --text-font-size-100: 16px; /* This is the base size */
    --text-font-size-200: 18px;
    --text-font-size-300: 20px;
    --text-font-size-400: 22px;
    --text-font-size-500: 26px;
    --text-font-size-600: 28px;
    --text-font-size-700: 32px;
    --text-font-size-800: 36px;
    --text-font-size-900: 42px;
    --text-font-size-1000: 46px;
    --text-font-size-1100: 52px;
    --text-font-size-1200: 66px;
    --text-font-size-1300: 74px;

    /* Line Height */
    --leading-heading: 1.1;
    --leading-detail: 1.3;
    --leading-body: 1.5;
    --leading-component-heading: 1.1;
    --leading-component-text: 1.3;

    /* Colors */
    --color-primary-purple: #4f20bd;
    --color-neutral-000: #ffffff;
    --color-link: #6077cd;
    --color-border: #e4e4e7;
    --color-highlight: #e7cb52;

    --color-white-100: #ffffff;

    --color-primary-050: #f6f3fe;
    --color-primary-50: #f6f3fe;
    --color-primary-100: #d7c9fa;
    --color-primary-200: #b8a1f4;
    --color-primary-300: #9a79ee;
    --color-primary-400: #7d52e7;
    --color-primary-500: #602dde;
    --color-primary-600: #4f20bd;
    --color-primary-700: #401b94;
    --color-primary-800: #31156c;
    --color-primary-900: #200f45;

    --color-purple-050: #f6f3fe;
    --color-purple-50: #f6f3fe;
    --color-purple-100: #d7c9fa;
    --color-purple-200: #b8a1f4;
    --color-purple-300: #9a79ee;
    --color-purple-400: #7d52e7;
    --color-purple-500: #602dde;
    --color-purple-600: #4f20bd;
    --color-purple-700: #401b94;
    --color-purple-900: #200f45;

    --color-secondary-050: #fefcf3;
    --color-secondary-100: #faf1c9;
    --color-secondary-200: #f4e4a1;
    --color-secondary-300: #eed779;
    --color-secondary-400: #e7c852;
    --color-secondary-500: #deb92d;
    --color-secondary-600: #bd9e20;
    --color-secondary-700: #947d1b;
    --color-secondary-800: #6c5c15;
    --color-secondary-900: #453c0f;

    --color-gy-050: #f6f5ff;
    --color-gy-100: #faf1c9;
    --color-gy-200: #f4e4a1;
    --color-gy-300: #eed779;
    --color-gy-500: #deb92d;

    --color-neutral-050: #fafafa;
    --color-neutral-100: #f5f5f5;
    --color-neutral-200: #e5e5e5;
    --color-neutral-300: #d4d4d4;
    --color-neutral-400: #a3a3a3;
    --color-neutral-500: #737373;
    --color-neutral-600: #525252;
    --color-neutral-700: #404040;
    --color-neutral-800: #262626;
    --color-neutral-900: #171717;

    --color-success-100: #b8e8d8;
    --color-success-300: #65c4a5;
    --color-success-500: #419a7d;
    --color-success-700: #2d614f;
    --color-success-900: #162b23;

    --color-warning-100: #fef4e9;
    --color-warning-300: #fcb874;
    --color-warning-500: #fd8c23;
    --color-warning-700: #ca6506;
    --color-warning-900: #7a3e06;

    --color-error-100: #fbebea;
    --color-error-300: #e0827c;
    --color-error-500: #c93f38;
    --color-error-700: #9e2131;
    --color-error-900: #482034;

    --color-info-100: #ebeefa;
    --color-info-300: #a4b1e5;
    --color-info-500: #6077cd;
    --color-info-700: #405bbf;
    --color-info-900: #2d3c77;

    --color-cherry-blossom-50: #f6eafe;
    --color-cherry-blossom-300: #d69dfc;
    --color-cherry-blossom-500: #c775fc;
    --color-cherry-blossom-700: #b84cfd;
    --color-cherry-blossom-900: #9903f8;

    --color-ice-turquoise-50: #e8f6f6;
    --color-ice-turquoise-300: #91d1d1;
    --color-ice-turquoise-500: #5ab4b5;
    --color-ice-turquoise-700: #3d7f80;
    --color-ice-turquoise-900: #244747;

    --color-sky-50: #e7eefe;
    --color-sky-300: #6291fc;
    --color-sky-500: #0652ff;
    --color-sky-700: #0436a8;
    --color-sky-900: #041a4e;

    --color-red-card-50: #ffe7e6;
    --color-red-card-300: #fca49b;
    --color-red-card-500: #fd5a4b;
    --color-red-card-700: #cd1405;
    --color-red-card-900: #7d0f06;

    --color-honeycomb-50: #fef6e8;
    --color-honeycomb-300: #fbd797;
    --color-honeycomb-500: #fbb845;
    --color-honeycomb-700: #e49309;
    --color-honeycomb-900: #915f08;

    --color-earth-red-50: #f3e7e4;
    --color-earth-red-300: #dbb4ac;
    --color-earth-red-500: #c78071;
    --color-earth-red-700: #a25747;
    --color-earth-red-900: #673b33;
  }
}

/* Custom animations for help modal */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.4s ease-out;
}

/* Enhanced scrolling support for legal middleware and general application */
.legal-scroll-container {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  /* Ensure scrollbars are always visible when needed */
  overflow-y: auto !important;
  overflow-x: hidden;
}

.legal-scroll-container::-webkit-scrollbar {
  width: 12px;
}

.legal-scroll-container::-webkit-scrollbar-track {
  background: hsl(var(--b2));
  border-radius: 6px;
}

.legal-scroll-container::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.3);
  border-radius: 6px;
  border: 2px solid hsl(var(--b2));
}

.legal-scroll-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.5);
}

/* Enhanced iframe scrolling */
.legal-iframe {
  scroll-behavior: smooth;
  /* Force scrollbars to be visible in iframe */
  scrollbar-width: auto;
  scrollbar-color: hsl(var(--bc) / 0.3) hsl(var(--b2));
}

.legal-iframe::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.legal-iframe::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

.legal-iframe::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.4);
  border-radius: 7px;
  border: 2px solid hsl(var(--b2));
}

.legal-iframe::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.6);
}

.legal-iframe::-webkit-scrollbar-corner {
  background: hsl(var(--b2));
}

/* Ensure middle mouse button scrolling works */
* {
  scroll-behavior: smooth;
}

/* Fix for potential scroll issues on Windows */
html {
  scroll-behavior: smooth;
}

body {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* Enhanced modal scrolling */
.legal-modal-content {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.legal-modal-iframe-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.legal-modal-iframe-container iframe {
  width: 100%;
  height: 100%;
  border: none;
  /* Ensure proper scrolling in iframe */
  overflow: auto;
}

.clip-hexagon-2 {
  clip-path: url(#clip-hexagon-2);
}

.drop-hexagon-shadow {
  filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.75));
}

/* remove scrollbar gutter from modal */
:root:has(
    :is(
        .modal-open,
        .modal:target,
        .modal-toggle:checked + .modal,
        .modal[open]
      )
  ) {
  scrollbar-gutter: unset;
}
