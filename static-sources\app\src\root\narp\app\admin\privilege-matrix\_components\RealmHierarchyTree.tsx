import { useState } from "react";
import {
  FaChevronDown,
  FaChevronRight,
  FaFolder,
  FaFolderOpen,
} from "react-icons/fa6";

interface Realm {
  realmPath: string;
  displayName: string;
  externalID: string;
  urn: string;
  eidURN: string;
  parentPath?: string;
  children?: Realm[];
}

interface RealmHierarchyTreeProps {
  realm: Realm;
  selectedRealm: Realm | null;
  onRealmSelect: (realm: Realm) => void;
  level?: number;
}

export default function RealmHierarchyTree({
  realm,
  selectedRealm,
  onRealmSelect,
  level = 0,
}: RealmHierarchyTreeProps) {
  const [isExpanded, setIsExpanded] = useState(
    level === 0 ||
      (selectedRealm?.realmPath.startsWith(realm.realmPath) ?? false),
  );

  const hasChildren = realm.children && realm.children.length > 0;
  const isSelected = selectedRealm?.externalID === realm.externalID;

  const handleToggle = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleSelect = () => {
    onRealmSelect(realm);
  };

  return (
    <div className="select-none">
      <div
        className={`flex items-center cursor-pointer hover:bg-gray-100 rounded px-2 py-1 ${
          isSelected ? "bg-blue-100 text-blue-700" : ""
        }`}
        style={{ paddingLeft: `${level * 20 + 8}px` }}
        onClick={handleSelect}
      >
        <div
          className="w-4 h-4 flex items-center justify-center mr-1"
          onClick={(e) => {
            e.stopPropagation();
            handleToggle();
          }}
        >
          {hasChildren ? (
            isExpanded ? (
              <FaChevronDown size={12} />
            ) : (
              <FaChevronRight size={12} />
            )
          ) : null}
        </div>

        <div className="w-4 h-4 flex items-center justify-center mr-2">
          {hasChildren ? (
            isExpanded ? (
              <FaFolderOpen size={14} />
            ) : (
              <FaFolder size={14} />
            )
          ) : (
            <FaFolder size={14} />
          )}
        </div>

        <span className="text-sm font-medium truncate">
          {realm.displayName}
        </span>
      </div>

      {hasChildren && isExpanded && (
        <div>
          {realm.children!.map((child) => (
            <RealmHierarchyTree
              key={child.externalID}
              realm={child}
              selectedRealm={selectedRealm}
              onRealmSelect={onRealmSelect}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}
