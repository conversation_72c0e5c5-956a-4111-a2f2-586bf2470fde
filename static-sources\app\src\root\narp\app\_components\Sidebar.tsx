import Logo from "@/assets/brand/color-logo.svg";
import { LuCalendarCheck } from "react-icons/lu";
import {
  FaBars,
  FaFolderClosed,
  FaUserTie,
  FaDatabase,
  FaUser,
} from "react-icons/fa6";
import { NavLink } from "react-router-dom";
import { Dispatch, ReactNode, SetStateAction, useState } from "react";
import ContextBar from "@/root/narp/app/_components/ContextBar.tsx";

export default function Sidebar() {
  const [contextOpen, setContextOpen] = useState(false);

  return (
    <div className="z-10 relative w-[78px] h-screen bg-primary-900 flex flex-col justify-between drop-shadow-lg">
      <ContextBar open={contextOpen} />

      <div className="basis-0 p-3 bg-primary-900">
        <img src={Logo} alt="Logo" className="size-full" />
      </div>

      <div className="basis-0 grow flex flex-col bg-primary-900">
        <SidebarEntry
          to={`/narp/app/planner`}
          title="Planner"
          icon={<LuCalendarCheck className="size-6" />}
        />
        <SidebarEntry
          to={`/narp/app/lor`}
          title="LOR"
          icon={<FaDatabase className="size-6" />}
        />
        <SidebarEntry
          to={`/narp/app/account`}
          title="Account"
          icon={<FaUser className="size-6" />}
        />
      </div>

      <div className={"basis-0 bg-primary-900"}>
        <div className={"basis-0 bg-primary-900"}>
          <SidebarEntry
            to={`/narp/app/asset-manager`}
            title="Assets"
            icon={<FaFolderClosed className="size-6" />}
          />
          <SidebarEntry
            to={`/narp/app/admin`}
            title="Admin"
            icon={<FaUserTie className="size-6" />}
          />
          <ContextBarButton setContextOpen={setContextOpen} />
        </div>
      </div>
    </div>
  );
}

function SidebarEntry({
  end = false,
  to,
  icon,
  title,
}: {
  end?: boolean;
  to: string;
  title: string;
  icon: ReactNode;
}) {
  return (
    <NavLink
      end={end}
      to={to}
      className={({ isActive }) => `
    flex flex-col items-center cursor-pointer py-3 px-4 transition-colors duration-300
    ${isActive ? "bg-white text-primary-900" : "hover:bg-white hover:text-primary-900 text-white"}
  `}
    >
      {icon}
      <span className={"text-xs mt-1 tracking-wide"}>{title}</span>
    </NavLink>
  );
}

function ContextBarButton({
  setContextOpen,
}: {
  setContextOpen: Dispatch<SetStateAction<boolean>>;
}) {
  return (
    <button
      type="button"
      onClick={() => setContextOpen((prev) => !prev)}
      className={`w-full flex flex-col items-center cursor-pointer py-3 px-4 transition-colors duration-300 hover:bg-white hover:text-primary-900 text-white`}
    >
      <FaBars />
      <span className={"text-xs mt-1 tracking-wide"}>Misc.</span>
    </button>
  );
}
