import { <PERSON>Event, MouseEvent, useEffect, useMemo, useState } from "react";
import {
  Authority,
  useCreateScopeSequenceContext,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import Fuse from "fuse.js";
import useGET from "@/util/api/useGET.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { FaSearch } from "react-icons/fa";
import { Navigate, useNavigate } from "react-router-dom";

export function Component() {
  const GET = useGET();
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const navigate = useNavigate();

  const { subregion } = useCreateScopeSequenceContext();

  const [authorities, setAuthorities] = useState<Authority[] | null>(null);

  async function updateSubRegions() {
    if (!subregion) return;

    const { error, response, data } = await GET(
      `/v1/standard-authorities/${subregion.urn}`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
      }
    );

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (data === null) return pushError("Missing data.");

    const envelope: APIEnvelope<Authority[]> = JSON.parse(await data.text());
    if (!envelope.data) {
      pushError("subregion has no authorities");
      return navigate("../subregion");
    }
    setAuthorities(envelope.data);
  }

  useEffect(() => {
    updateSubRegions();
  }, []);

  if (!subregion) return <Navigate to={"../subregion"} />;

  if (!authorities)
    return (
      <div className="grow flex items-center justify-center">
        <div className="loading loading-spinner loading-xl" />
      </div>
    );

  return <Content authorities={authorities} />;
}

function Content({ authorities }: { authorities: Authority[] }) {
  const { authority } = useCreateScopeSequenceContext();
  const navigate = useNavigate();

  const [continuable, setContinuable] = useState<boolean>(false);
  const [searchString, setSearchString] = useState<string>("");

  const searchEntries: Authority[] = useMemo(() => {
    if (searchString == "" || !searchString) return authorities;
    const fuse = new Fuse<Authority>(authorities, { keys: ["name"] });
    return fuse.search(searchString).map((result) => result.item);
  }, [authorities, searchString]);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  useEffect(() => {
    setContinuable(!!authority);
  }, [authority]);

  function handleToNext(e: MouseEvent) {
    e.preventDefault();
    navigate("../standard");
  }

  function handleToPrevious(e: MouseEvent) {
    e.preventDefault();
    navigate("../subregion");
  }

  return (
    <>
      <div className="flex flex-row justify-between px-5">
        <div className="flex flex-col gap-1">
          <div className="text-xl font-bold">Choose an Authority</div>
          <div>Choose the authority you want to base your plan off of.</div>
        </div>

        <label className="input input-lg">
          <FaSearch className=" text-neutral-300 shrink-0" />
          <input onInput={handleInput} type="text" placeholder="Search" />
        </label>
      </div>

      <div className="mb-auto touch-manipulation px-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {searchEntries.map((entry, index) => (
          <AuthorityEntry authority={entry} key={index} />
        ))}
      </div>

      <div className="bg-base-100 shadow-lg shadow-black border-t border-t-base-content/20 p-5 gap-3 flex flex-row justify-end">
        <button
          onClick={handleToPrevious}
          type="button"
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Back
        </button>

        <button
          onClick={handleToNext}
          type="button"
          disabled={!continuable}
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </>
  );
}

function AuthorityEntry({ authority }: { authority: Authority }) {
  const { setAuthority, authority: currentAuthority } =
    useCreateScopeSequenceContext();
  const navigate = useNavigate();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setAuthority(authority);
  }

  function handleDoubleClick(e: MouseEvent) {
    e.preventDefault();
    setAuthority(authority);
    navigate("../standard");
  }

  return (
    <button
      title={authority.name}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      type="button"
      className={`touch-pan-y btn btn-outline gap-3 ${currentAuthority?.code === authority.code && "outline-2 outline-offset-2"}`}
    >
      <div className="grow text-start truncate">
        ({authority.code}) {authority.name}
      </div>
    </button>
  );
}
