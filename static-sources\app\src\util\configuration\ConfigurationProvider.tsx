import { ReactNode, useEffect, useState } from "react";
import {
  Configuration,
  ConfigurationContext,
} from "@/util/configuration/ConfigurationContext.tsx";
import { CookieStorage } from "amazon-cognito-identity-js";
import Icon from "@/assets/brand/color-logo.svg";

interface RemoteConfiguration {
  region: string;
  userPoolClientAutomationAppID: string;
  userPoolClientWebAppID: string;
  userPoolID: string;
}

const cookies = new CookieStorage({
  path: "/",
  secure: !import.meta.env.DEV,
});

export default function ConfigurationProvider({
  children,
}: {
  children: ReactNode;
}) {
  const [configuration, setConfiguration] = useState<Configuration | null>(
    null,
  );
  const [error, setError] = useState<Error | null>(null);

  async function init() {
    setConfiguration(null);

    let baseUrl: string;
    if (import.meta.env.DEV && import.meta.env.VITE_API_ORIGIN) {
      // manually defined api origin (should only be used during local development)
      baseUrl = import.meta.env.VITE_API_ORIGIN;
    } else {
      const rawBase = cookies.getItem("api_base_url");
      if (!rawBase) return setError(new Error("missing base url cookie"));
      baseUrl = "https://" + rawBase;
    }

    const url = new URL("/v1/system/configuration", baseUrl);
    const res = await fetch(url);
    let config: RemoteConfiguration;
    try {
      config = await res.json();
    } catch (err) {
      return setError(new Error("failed to parse remote configuration"));
    }

    setConfiguration({
      apiBaseURL: baseUrl,
      cookieStorage: cookies,
      region: config.region,
      userPoolClientAutomationAppID: config.userPoolClientAutomationAppID,
      userPoolID: config.userPoolID,
      userPoolClientWebAppID: config.userPoolClientWebAppID,
    });
  }

  useEffect(() => {
    init();
  }, []);

  if (error) {
    return (
      <div className="fixed w-screen h-screen top-0 left-0 flex flex-col items-center justify-center gap-3">
        <img src={Icon} className="size-48" />
        <div className="text-xl flex flex-row items-end font-bold text-error">
          <div>{error.message}</div>
        </div>
      </div>
    );
  }

  if (configuration) {
    return (
      <ConfigurationContext.Provider value={configuration}>
        {children}
      </ConfigurationContext.Provider>
    );
  }

  return (
    <div className="fixed w-screen h-screen top-0 left-0 flex flex-col items-center justify-center gap-3">
      <img src={Icon} className="size-48" />

      <div className="text-xl flex flex-row items-end font-bold">
        <span>Loading</span>
        <span className="loading loading-sm loading-dots"></span>
      </div>
    </div>
  );
}
