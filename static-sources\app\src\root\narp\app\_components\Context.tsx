import { createContext, useContext } from "react";

export interface Realm {
  realmPath: string;
  displayName: string;
  externalID: string;
  urn: string;
  eidURN: string;
  parentPath: string;
}

export interface RealmDetails {
  realmPath: string;
  displayName: string;
  externalID: string;
  urn: string;
  eidURN: string;
  children: RealmDetails[];
}

export interface Context {
  topRealms: Realm[];
  currentTopRealm: Realm;
  setTopRealm: (index: number, redirect?: boolean) => void;
  fetchRealmDetails: (
    eid: string,
  ) => Promise<[RealmDetails, null] | [null, Error]>;
}

export const Context = createContext<Context>({} as Context);

export function useTopRealm() {
  return useContext(Context).currentTopRealm;
}

export function useRealmContext() {
  return useContext(Context);
}
