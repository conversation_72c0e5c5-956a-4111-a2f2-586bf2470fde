import BreadCrumbs from "@/util/components/bread-crumbs/BreadCrumbs.tsx";
import Crumb from "@/util/components/bread-crumbs/Crumb.tsx";
import { LuCalendarCheck } from "react-icons/lu";
import NavBar from "@/root/narp/app/planner/scopeAndSequence/edit/_components/NavBar.tsx";
import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { useParams } from "react-router-dom";
import DndHandler from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";
import { Entries } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/Entries.tsx";

export function Component() {
  const { id } = useParams();
  const {
    outline: { title },
  } = useEditorContext();

  return (
    <div className="size-full overflow-y-auto overflow-x-hidden flex flex-col py-5 gap-5">
      <div className="flex flex-col gap-3 px-5">
        <BreadCrumbs>
          <Crumb
            icon={<LuCalendarCheck className="size-4" />}
            label="Planner"
            to="/narp/app/planner"
          />
          <Crumb
            label={title}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}`}
          />
          <Crumb
            label="Topics"
            base
            active={false}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}/topics`}
          />
        </BreadCrumbs>
        <NavBar />
      </div>

      <DndHandler>
        <Entries />
      </DndHandler>
    </div>
  );
}
