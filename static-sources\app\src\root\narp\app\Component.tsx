import Sidebar from "@/root/narp/app/_components/Sidebar.tsx";
import { Navigate, Outlet } from "react-router-dom";
import { useAuthenticated } from "@/util/auth/useAuthenticated.tsx";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";
import { LegalMiddleware } from "@/root/narp/app/_components/LegalMiddleware.tsx";

function AppLayout() {
  return (
    <div className="w-screen h-screen fixed flex flex-row overflow-hidden">
      <Sidebar />
      <div className="w-full overflow-hidden">
        <Outlet />
      </div>
    </div>
  );
}

export function Component() {
  const authenticated = useAuthenticated();

  if (authenticated === null) return <PageSkeleton />;
  if (!authenticated) return <Navigate to="/narp/auth" />;

  return (
    <LegalMiddleware>
      <AppLayout />
    </LegalMiddleware>
  );
}
