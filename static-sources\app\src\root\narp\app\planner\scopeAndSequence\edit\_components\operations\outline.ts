import { GradeLevel } from "@/util/standards.ts";
import { BaseOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/base.ts";

export interface OutlineSetTitleOperation extends BaseOperation {
  namespace: "OUTLINE";
  action: "SET_TITLE";
  data: {
    previous: string;
  };
}

export interface OutlineSetAbstractOperation extends BaseOperation {
  namespace: "OUTLINE";
  action: "SET_ABSTRACT";
  data: {
    previous: string;
  };
}

export interface OutlineSetGradeLevelOperation extends BaseOperation {
  namespace: "OUTLINE";
  action: "SET_GRADE_LEVEL";
  data: {
    previous: GradeLevel;
  };
}

export interface OutlineAddKeywordOperation extends BaseOperation {
  namespace: "OUTLINE";
  action: "ADD_KEYWORD";
  data: {
    index: number;
  };
}

export interface OutlineRemoveKeywordOperation extends BaseOperation {
  namespace: "OUTLINE";
  action: "REMOVE_KEYWORD";
  data: {
    value: string;
    index: number;
  };
}

export type OutlineOperation =
  | OutlineSetTitleOperation
  | OutlineSetAbstractOperation
  | OutlineSetGradeLevelOperation
  | OutlineAddKeywordOperation
  | OutlineRemoveKeywordOperation;
