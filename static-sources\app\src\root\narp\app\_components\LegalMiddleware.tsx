import {
  <PERSON><PERSON><PERSON>,
  MouseE<PERSON>,
  ReactN<PERSON>,
  SetStateAction,
  useEffect,
  useMemo,
  useState,
} from "react";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";
import { FaCheck } from "react-icons/fa6";
import { FaExternalLinkAlt } from "react-icons/fa";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import useGET from "@/util/api/useGET.tsx";
import usePOST from "@/util/api/usePOST.tsx";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { AlertType, usePushAlert } from "@/util/components/Alerts/Context.tsx";

const entries: { label: string; url: string }[] = [
  {
    label: "Terms of Service",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/terms-of-service.html",
  },
  {
    label: "Privacy Policy",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/privacy-policy.html",
  },
  {
    label: "Acceptable Use Policy",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/acceptable-use-policy.html",
  },
  {
    label: "End User License Agreement",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/end-user-license-agreement.html",
  },
  {
    label: "Cookie Policy",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/cookie-policy.html",
  },
  {
    label: "FERPA Notice",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/ferpa.html",
  },
  {
    label: "COPPA Notice",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/coppa.html",
  },
  {
    label: "Contributor Agreement",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/contributor-agreement.html",
  },
  {
    label: "Data Processing Agreement",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/data-processing-agreement.html",
  },
  {
    label: "API Terms of Use",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/api-terms-of-use.html",
  },
  {
    label: "Community Guidelines",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/community-guidelines.html",
  },
  {
    label: "SaaS Subscription Agreement",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/saas-agreement.html",
  },
  {
    label: "Accessibility Statement",
    url: "https://ayode-institute.github.io/ayode-design-documentation/legal/accessibility-statement.html",
  },
];

type AcceptedDoc = {
  [label: string]: boolean;
};

export function LegalMiddleware({ children }: { children: ReactNode }) {
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const GET = useGET();
  const POST = usePOST();
  const pushAlert = usePushAlert();

  const [acceptedDoc, setAcceptedDoc] = useState<AcceptedDoc | null>(null);
  const [continuable, setContinuable] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const allAccepted = useMemo(() => {
    if (!acceptedDoc) return false;
    for (const entry of entries) {
      if (!acceptedDoc[entry.label]) return false;
    }
    return true;
  }, [acceptedDoc]);

  useEffect(() => {
    async function init() {
      setAcceptedDoc(null);
      const { response, data, error } = await GET(
        `/v1/assets/${topRealm.externalID}/~/etc/legal.json`,
        {
          assertedRealmEidUrn: topRealm.eidURN,
          params: [["encoding", "text"]],
        }
      );

      if (!response.ok && response.status === 404) return setAcceptedDoc({});

      if (error) return pushError(error.clientErrorDetail.userMessage);
      if (!response.ok) return pushError(await response.text());
      if (!data) return pushError("missing data");

      const doc: AcceptedDoc = JSON.parse(await data.text());

      setAcceptedDoc(doc);

      for (const entry of entries) {
        if (!doc[entry.label]) return;
      }
      setContinuable(true);
    }

    init();
  }, []);

  async function handleClick(e: MouseEvent) {
    e.preventDefault();
    if (!acceptedDoc) return;

    for (const entry of entries) {
      if (!acceptedDoc[entry.label]) {
        setContinuable(false);
        return pushAlert(
          "You must accept all legal statements before continuing",
          AlertType.WARNING
        );
      }
    }

    setLoading(true);
    const { response, error } = await POST(
      `/v1/assets/${topRealm.externalID}/~/etc/legal.json`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
        headers: { ["Content-Type"]: "application/octet-stream" },
        body: JSON.stringify(acceptedDoc),
      }
    );
    setLoading(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());

    setContinuable(true);
  }

  if (acceptedDoc === null) return <PageSkeleton />;

  if (!continuable)
    return (
      <>
        {/* Main Legal Page with Enhanced Scrolling */}
        <div className="h-screen bg-gradient-to-br from-primary-50 via-base-100 to-secondary-50 legal-scroll-container">
          <div className="container mx-auto px-4 py-12 min-h-full">
            <div className="max-w-4xl mx-auto">
              {/* Hero Section */}
              <div className="text-center mb-12">
                <div className="inline-block p-4 bg-primary-600 rounded-full mb-6 shadow-lg">
                  <FaCheck className="text-white text-3xl" />
                </div>
                <h1 className="text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-500 mb-6">
                  Welcome to Ayode
                </h1>
                <div className="max-w-2xl mx-auto">
                  <p className="text-xl text-base-content/80 leading-relaxed mb-6">
                    Before you can fully access Ayode, please take a moment to
                    review each agreement listed below. By accepting them, you
                    confirm your understanding and continued agreement to use
                    Ayode.
                  </p>
                  <div className="card bg-info/10 border border-info/20">
                    <div className="card-body p-4">
                      <p className="text-base text-info-content/80">
                        📋 Full legal documentation available{" "}
                        <a
                          className="link link-info font-semibold hover:text-info-700 transition-colors"
                          href="https://ayode-institute.github.io/ayode-design-documentation/legal/index.html"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          here
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Legal Documents Grid with Enhanced Scrolling */}
              <div className="grid gap-6 mb-12">
                {entries.map((entry, index) => (
                  <LegalEntry
                    label={entry.label}
                    url={entry.url}
                    key={index}
                    doc={acceptedDoc}
                    setDoc={setAcceptedDoc}
                  />
                ))}
              </div>

              {/* Progress Indicator */}
              <div className="card bg-base-100 shadow-xl border border-base-300 mb-8">
                <div className="card-body">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold">Progress</h3>
                    <div className="text-sm font-medium text-base-content/60">
                      {Object.values(acceptedDoc).filter(Boolean).length} of{" "}
                      {entries.length} accepted
                    </div>
                  </div>
                  <div className="w-full bg-base-300 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-primary-500 to-secondary-500 h-3 rounded-full transition-all duration-500 ease-out"
                      style={{
                        width: `${(Object.values(acceptedDoc).filter(Boolean).length / entries.length) * 100}%`,
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Continue Section */}
              <div className="card bg-gradient-to-r from-primary-400 to-primary-700 text-primary-content shadow-2xl">
                <div className="card-body text-center">
                  <h3 className="text-2xl font-bold mb-4">
                    Ready to Continue?
                  </h3>
                  <p className="text-primary-content/90 mb-6 max-w-lg mx-auto">
                    To continue using Ayode, please accept all the terms and
                    policies listed above. Once accepted, you can click
                    'Continue' to gain full access to the application.
                  </p>

                  <button
                    type="button"
                    disabled={!allAccepted || loading}
                    onClick={handleClick}
                    className={`btn btn-lg gap-3 ${allAccepted && !loading
                      ? "btn-neutral hover:btn-neutral-focus shadow-xl transform hover:scale-105 transition-all duration-200"
                      : "bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700 cursor-not-allowed"
                      }`}
                  >
                    {loading ? (
                      <>
                        <span className="loading loading-spinner loading-sm" />
                        Processing...
                      </>
                    ) : allAccepted ? (
                      <>
                        <FaCheck />
                        Continue to Ayode
                      </>
                    ) : (
                      <>Please accept all terms above</>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );

  return children;
}

function LegalEntry({
  label,
  url,
  doc,
  setDoc,
}: {
  label: string;
  url: string;
  doc: AcceptedDoc;
  setDoc: Dispatch<SetStateAction<AcceptedDoc | null>>;
}) {
  const checked = doc[label] || false;

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setDoc({ ...doc, [label]: !checked });
  }

  return (
    <div
      className={`card bg-base-100 shadow-lg border transition-all duration-300 hover:shadow-xl ${checked
        ? "border-success/30 bg-gradient-to-r from-success/5 to-success/10"
        : "border-base-300 hover:border-primary/30"
        }`}
    >
      <div className="card-body p-3 md:p-6">
        <div className="flex items-center gap-4">
          <input
            required
            name={label}
            type="checkbox"
            hidden
            checked={checked}
            readOnly
          />

          {/* Document Icon - Only show checkmark when accepted */}
          {checked && (
            <div className="flex-shrink-0 w-8 h-8 md:w-12 md:h-12 rounded-full flex items-center justify-center transition-all duration-300 bg-success text-success-content shadow-lg">
              <FaCheck className="text-lg" />
            </div>
          )}

          {/* Document Info */}
          <div className="flex-grow">
            <h3 className="text-sm md:text-lg font-bold text-base-content mb-1">{label}</h3>
            <div className="flex gap-2">
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-sm btn-outline gap-2"
              >
                <FaExternalLinkAlt />
                View Document
              </a>
            </div>
          </div >

          {/* Accept Button */}
          < button
            type="button"
            onClick={handleClick}
            className={`btn btn-lg p-2 px-4 md:p-5 text-sm md:text-xl gap-2 transition-all duration-300 transform hover:scale-105 ${checked
              ? "btn-success shadow-lg"
              : "btn-primary hover:btn-primary-focus"
              }`
            }
          >
            {
              checked ? (
                <>
                  <FaCheck className="text-sm md:text-lg" />
                  Accepted
                </>
              ) : (
                <>Accept</>
              )}
          </button >
        </div >
      </div >
    </div >
  );
}
