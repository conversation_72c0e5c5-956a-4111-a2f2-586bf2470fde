import BreadCrumbs from "@/util/components/BreadCrumbs.tsx";
import { <PERSON><PERSON><PERSON><PERSON>, FaFolder, FaFolderClosed } from "react-icons/fa6";
import TopBar from "@/root/narp/app/asset-manager/_components/topbar/TopBar.tsx";
import Details from "@/root/narp/app/asset-manager/_components/details/Details.tsx";
import { useEffect, useState } from "react";
import { Link, useLocation, useParams } from "react-router-dom";
import { HttpMethod, useAPIRequest } from "@/util/API.tsx";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { AssetNode } from "@/root/narp/app/asset-manager/_components/types.ts";
import path from "path-browserify";

export function Component() {
  const location = useLocation();
  const { realmEIDURN, zone, "*": splat } = useParams();
  const realmEID = realmEIDURN?.replace("urn:ayode:realm-eid:", "");

  const request = useAPIRequest();
  const pushError = usePushError();

  const [data, setData] = useState<{
    currentNode: AssetNode;
    children: AssetNode[];
  } | null>(null);

  async function updateNodes() {
    if (!realmEIDURN || !realmEID || !zone) return;
    setData(null);

    if (splat == "" || splat?.endsWith("/")) {
      // test if directory
      const [response, error] = await request(
        `/v1/assets/${realmEID}/${zone}/${splat}/`,
        HttpMethod.GET,
        {
          headers: { ["X-Ayode-Asserted-Realm-EID"]: realmEIDURN },
        },
      );

      if (!response) return pushError("missing auth token");
      if (error) return pushError(error.clientErrorDetail.userMessage);

      const result: AssetNode[] = await response.json();
      let currentNode: AssetNode | null = null;
      const children: AssetNode[] = [];

      for (const node of result) {
        if (node.userPath === ".") {
          currentNode = node;
        } else {
          children.push(node);
        }
      }
      if (!currentNode) return pushError("missing current node");

      setData({
        currentNode: currentNode,
        children: children,
      });
    } else if (splat !== undefined) {
      // test if file
      const tokens = splat.split("/");
      const containerPath = tokens.slice(0, -1).join("/");
      const [name, extension] = tokens.at(tokens.length - 1)!.split(".");

      const [response, error] = await request(
        // fetch container node
        path.join("/v1/assets", realmEID, zone, containerPath, "/"),
        HttpMethod.GET,
        {
          headers: { ["X-Ayode-Asserted-Realm-EID"]: realmEIDURN },
        },
      );

      if (!response) return pushError("missing auth token");
      if (error) return pushError(error.clientErrorDetail.userMessage);

      const result: AssetNode[] = await response.json();
      const currentNode = result.find(
        (entry) =>
          entry.entityType === "file" &&
          entry.userBasename === name &&
          entry.userExtension === extension,
      );

      if (!currentNode) return pushError("missing current node");

      setData({
        currentNode: currentNode,
        children: [],
      });
      return;
    }
  }

  useEffect(() => {
    updateNodes();
  }, [location]);

  return (
    <div className="size-full flex flex-col">
      <div className="p-5 flex flex-col gap-5">
        <BreadCrumbs
          crumbs={[
            {
              label: "Asset Manager",
              icon: <FaFolderClosed className="size-4" />,
              to: "/narp/app/asset-manager",
            },
          ]}
        />

        <div className="font-bold text-3xl">Asset Manager</div>
        <TopBar />
      </div>

      <div className="divider divider-vertical m-0 p-0" />

      {!realmEIDURN || !realmEID || !zone ? (
        <div className="grow flex items-center justify-center text-neutral-400">
          <div>Select a realm and zone to view assets.</div>
        </div>
      ) : data ? (
        <div className="grow flex flex-col md:flex-row overflow-hidden">
          <div className="basis-1/4 p-5 flex flex-col items-start overflow-auto">
            {splat !== undefined && splat !== "" && (
              <DirectoryEntry
                realmEIDURN={realmEIDURN}
                zone={zone}
                name=".."
                assetPath={
                  splat
                    .split("/")
                    .slice(
                      0,
                      data.currentNode.entityType === "directory" ? -2 : -1,
                    )
                    .join("/") || ""
                }
              />
            )}

            {data.children.map((node, i) => {
              if (node.entityType === "directory") {
                const tokens = node.userPath.split("/");

                return (
                  <DirectoryEntry
                    realmEIDURN={realmEIDURN}
                    zone={zone}
                    name={tokens.at(tokens.length - 1)!}
                    assetPath={node.userPath}
                    key={i}
                  />
                );
              } else if (node.entityType === "file") {
                return (
                  <FileEntry
                    assetPath={node.containerPath}
                    realmEIDURN={realmEIDURN}
                    zone={zone}
                    name={node.userBasename}
                    extension={node.userExtension}
                    key={i}
                  />
                );
              }
            })}
          </div>
          <div className="divider divider-horizontal p-0 m-0" />
          <div className="basis-3/4 p-5 overflow-auto">
            <Details node={data.currentNode} />
          </div>
        </div>
      ) : (
        <div className="grow flex items-center justify-center">
          <div className="loading loading-spinner loading-xl" />
        </div>
      )}
    </div>
  );
}

function FileEntry({
  realmEIDURN,
  zone,
  name,
  extension,
  assetPath,
}: {
  realmEIDURN: string;
  zone: string;
  name: string;
  extension: string;
  assetPath: string;
}) {
  return (
    <Link
      className="btn btn-ghost btn-sm text-sm font-normal min-w-full justify-start"
      to={path.join(
        "/narp/app/asset-manager/",
        realmEIDURN,
        zone,
        assetPath,
        `${name}.${extension}`,
      )}
    >
      <FaFile />
      <div>
        {name}.{extension}
      </div>
    </Link>
  );
}

function DirectoryEntry({
  name,
  assetPath,
  realmEIDURN,
  zone,
}: {
  name: string;
  assetPath: string;
  realmEIDURN: string;
  zone: string;
}) {
  return (
    <Link
      className="btn btn-ghost btn-sm text-sm font-normal min-w-full justify-start"
      to={path.join(
        "/narp/app/asset-manager/",
        realmEIDURN,
        zone,
        assetPath,
        "/",
      )}
    >
      <FaFolder />
      <div>{name}</div>
    </Link>
  );
}
