import {
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { FaXmark } from "react-icons/fa6";
import WordField from "@/root/narp/app/planner/scopeAndSequence/edit/terms/_components/WordField.tsx";
import LexicalCategoryField from "@/root/narp/app/planner/scopeAndSequence/edit/terms/_components/LexicalCategoryField.tsx";
import DefinitionField from "@/root/narp/app/planner/scopeAndSequence/edit/terms/_components/DefinitionField.tsx";

export default function EditMenu({
  editID,
  setEditID,
}: {
  editID: EntityID | null;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    term: { get },
  } = useEditorContext();

  const [generating, setGenerating] = useState<boolean>(false);

  const dialogRef = useRef<HTMLDialogElement>(null);
  const term = editID !== null ? get(editID) : null;

  useEffect(() => {
    if (term) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [term]);

  function closeDialog() {
    dialogRef.current?.close();
  }

  function handleClose() {
    setEditID(null);
  }

  if (!term) return <></>;
  return (
    <dialog onClose={handleClose} ref={dialogRef}>
      <button
        tabIndex={0}
        type="button"
        onClick={closeDialog}
        className="-z-10 fixed w-screen h-screen"
      />

      <div
        className="fixed w-full max-w-xl top-1/2 left-1/2 -translate-1/2
         p-5"
      >
        <div className="bg-white border border-neutral-300 rounded-lg p-5 flex flex-col gap-3">
          <div className="flex flex-row">
            <div className="grow basis-0">
              <div className="tooltip" data-tip="Close">
                <button
                  tabIndex={0}
                  type="button"
                  className="btn btn-sm btn-square btn-error btn-outline"
                  data-tip="what"
                  onClick={closeDialog}
                >
                  <FaXmark />
                </button>
              </div>
            </div>
            <div className="basis-0">
              <div className="text-xl font-bold text-nowrap">Edit Term</div>
            </div>
            <div className="grow basis-0"></div>
          </div>

          <WordField term={term} generating={generating} />
          <LexicalCategoryField term={term} generating={generating} />
          <DefinitionField
            term={term}
            generating={generating}
            setGenerating={setGenerating}
          />
        </div>
      </div>
    </dialog>
  );
}
