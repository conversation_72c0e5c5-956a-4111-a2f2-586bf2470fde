import { FormEvent, RefObject, useImperativeHandle, useRef } from "react";
import { GoalPriority } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaXmark } from "react-icons/fa6";

export interface AddGoalDialogRef {
  close: () => void;
  open: () => void;
  dialog: RefObject<HTMLDialogElement>;
}

export default function AddGoalDialog({
  ref,
  onClose,
  onOpen,
  onSubmit,
}: {
  ref: RefObject<AddGoalDialogRef>;
  onOpen?: () => void;
  onClose?: () => void;
  onSubmit?: (
    type: string,
    priority: GoalPriority,
    description: string,
  ) => void;
}) {
  const dialogRef = useRef<HTMLDialogElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  function open() {
    dialogRef.current?.showModal();
    formRef.current?.reset();
    onOpen && onOpen();
  }

  function close() {
    dialogRef.current?.close();
    onClose && onClose();
  }

  function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const type = data.get("type")?.toString();
    const priority = data.get("priority")?.toString();
    const description = data.get("description")?.toString();

    if (!type || !priority || !description) return;

    onSubmit && onSubmit(type, priority as GoalPriority, description);
    close();
  }

  useImperativeHandle(ref, () => {
    return {
      open: open,
      close: close,
      dialog: dialogRef,
    };
  });

  return (
    <dialog
      ref={dialogRef}
      className="not-open:hidden min-w-md m-auto bg-white border border-neutral-400 rounded-lg shadow overflow-visible"
      onClose={(e) => {
        e.preventDefault();
        close();
      }}
    >
      <form
        ref={formRef}
        onSubmit={handleSubmit}
        className="size-full flex flex-col gap-5 items-center p-5"
      >
        <div className="w-full flex flex-row items-center gap-3">
          <div className="grow basis-0">
            <div className="tooltip" data-tip={"Cancel"}>
              <button
                type="button"
                onClick={close}
                className="btn btn-error btn-square shrink-0"
              >
                <FaXmark />
              </button>
            </div>
          </div>
          <div className="text-xl font-bold">Add New Goal</div>
          <div className="grow basis-0"></div>
        </div>

        <label className="floating-label w-full">
          <span className="font-bold">Type</span>
          <input
            required
            autoFocus
            defaultValue=""
            name="type"
            type="text"
            autoComplete="off"
            className="input input-lg w-full"
          />
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Priority</span>
          <select
            name="priority"
            autoComplete="off"
            className="select select-lg w-full"
            defaultValue={GoalPriority.MUST_HAVE}
          >
            <option value={GoalPriority.MUST_HAVE}>Must Have</option>
            <option value={GoalPriority.NICE_TO_HAVE}>Nice-to-have</option>
            <option value={GoalPriority.OPTIONAL}>Optional</option>
          </select>
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Description</span>
          <textarea
            required
            defaultValue=""
            name="description"
            autoComplete="off"
            className="textarea textarea-lg w-full"
          />
        </label>

        <button type="submit" className="btn btn-primary w-full">
          Create
        </button>
      </form>
    </dialog>
  );
}
