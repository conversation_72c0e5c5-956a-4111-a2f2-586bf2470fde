{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.616.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@lottiefiles/dotlottie-react": "^0.13.2", "@monaco-editor/react": "^4.7.0-rc.0", "amazon-cognito-identity-js": "^6.3.12", "color-hash": "^2.0.2", "country-flag-icons": "^1.5.19", "framer-motion": "^12.23.11", "fuse.js": "^7.1.0", "jszip": "^3.10.1", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "mammoth": "^1.8.0", "monaco-editor": "^0.52.2", "path-browserify": "^1.0.1", "pdfjs-dist": "^4.10.38", "pdfmake": "^0.2.20", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-router-dom": "^7.1.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^5.1.0", "titleize": "^4.0.0", "uuid": "^11.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tanstack/router-vite-plugin": "^1.129.5", "@types/color-hash": "^2.0.0", "@types/node": "^20.14.10", "@types/path-browserify": "^1.0.2", "@types/pdfmake": "^0.2.11", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.19", "daisyui": "^5.0.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.38", "postcss-import": "^16.1.1", "prettier": "3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "6.3.4", "vite-plugin-static-copy": "^3.1.1"}, "overrides": {"eslint": {"file-entry-cache": "^8.0.0"}}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}}