import { BaseOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/base.ts";
import {
  EntityID,
  LexicalCategory,
  Term,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export interface TermAddOperation extends BaseOperation {
  namespace: "TERM";
  action: "ADD";
  data: {
    id: EntityID;
  };
}

export interface TermRemoveOperation extends BaseOperation {
  namespace: "TERM";
  action: "REMOVE";
  data: {
    index: number;
    value: Term;
  };
}

export interface TermSetWordOperation extends BaseOperation {
  namespace: "TERM";
  action: "SET_WORD";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TermSetCategoryOperation extends BaseOperation {
  namespace: "TERM";
  action: "SET_CATEGORY";
  data: {
    id: EntityID;
    previous: LexicalCategory;
  };
}

export interface TermSetDefinitionOperation extends BaseOperation {
  namespace: "TERM";
  action: "SET_DEFINITION";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TermSetParentOperation extends BaseOperation {
  namespace: "TERM";
  action: "SET_PARENT";
  data: {
    id: EntityID;
    previous: EntityID | null;
  };
}

export type TermOperation =
  | TermAddOperation
  | TermRemoveOperation
  | TermSetWordOperation
  | TermSetCategoryOperation
  | TermSetDefinitionOperation
  | TermSetParentOperation;
