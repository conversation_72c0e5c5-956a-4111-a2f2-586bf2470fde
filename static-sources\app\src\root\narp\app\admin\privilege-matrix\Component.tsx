import { useCallback, useEffect, useRef, useState } from "react";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import { useConfiguration } from "@/util/configuration/ConfigurationContext.tsx";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import RealmHierarchyTree from "./_components/RealmHierarchyTree";
import PrivilegeMatrix from "./_components/PrivilegeMatrix";

interface Realm {
  realmPath: string;
  displayName: string;
  externalID: string;
  urn: string;
  eidURN: string;
  parentPath?: string;
  children?: Realm[];
}

interface ApiRealm {
  descendants: { realms: ApiRealm[] };
  displayName: string;
  eidURN: string;
  externalID: string;
  realmPath: string;
  urn: string;
}

interface ApiResponse {
  realms: ApiRealm[];
}

interface PrivilegeAction {
  urn: string;
  name: {
    en_US: string;
    es_ES: string;
    fr_FR: string;
  };
}

interface PrivilegeMatrixEntry {
  privilegeActionURN: string;
  privilegeEIDurn: string;
  access: "allow" | "deny";
  origin: "direct" | "inherited";
  roleName: string;
  roleEIDurn: string;
  ownedByRealmEIDurn: string;
  targetRealmEIDurn: string;
  applicableRealmEIDurn: string;
}

function convertToRealm(apiRealm: ApiRealm): Realm {
  return {
    realmPath: apiRealm.realmPath,
    displayName: apiRealm.displayName,
    externalID: apiRealm.externalID,
    urn: apiRealm.urn,
    eidURN: apiRealm.eidURN,
    children: apiRealm.descendants?.realms?.map(convertToRealm) || [],
  };
}

export function Component() {
  const { getSession } = useAuth();
  const configuration = useConfiguration();
  const pushError = usePushError();
  const topRealm = useTopRealm();

  const handleError = useCallback(
    (error: string) => {
      pushError(error);
    },
    [pushError]
  );

  const [realmHierarchy, setRealmHierarchy] = useState<Realm | null>(null);
  const [selectedRealm, setSelectedRealm] = useState<Realm | null>(null);
  const [privilegeActions, setPrivilegeActions] = useState<PrivilegeAction[]>(
    []
  );
  const [privilegeMatrix, setPrivilegeMatrix] = useState<
    PrivilegeMatrixEntry[]
  >([]);
  const [loading, setLoading] = useState(false);
  const fetchInProgressRef = useRef<string | null>(null); // Fetch realm hierarchy
  const fetchRealmHierarchy = useCallback(async () => {
    const [session, error] = await getSession();
    if (error || !session) {
      console.error("Session error:", error);
      return handleError(error?.message || "Session not available.");
    }
    if (!configuration.apiBaseURL) {
      console.error("API Base URL is not configured.");
      return handleError("API Base URL is not configured.");
    }
    if (!topRealm?.externalID) {
      console.error("Top realm external ID is not available.", topRealm);
      return handleError("Top realm external ID information is missing.");
    }
    if (!topRealm.eidURN || !topRealm.eidURN.startsWith("urn:")) {
      console.error(
        "Top realm EID is not available or not a valid URN.",
        topRealm
      );
      return handleError(
        "Top realm information is missing or invalid (requires URN format)."
      );
    }

    const url = new URL(
      `/v1/realms/${encodeURIComponent(topRealm.externalID)}`,
      configuration.apiBaseURL
    );
    url.searchParams.set("shouldIncludeDescendants", "true");

    const headers = new Headers();
    headers.set(
      "Authorization",
      `Bearer ${session.getAccessToken().getJwtToken()}`
    );
    headers.set("X-Ayode-Asserted-Realm-EID", topRealm.eidURN);

    console.log("Fetching realm hierarchy from URL:", url.toString());
    console.log("Request headers:", Object.fromEntries(headers.entries()));

    try {
      const response = await fetch(url, { method: "GET", headers });
      if (!response.ok) {
        console.error(
          "API request failed with status:",
          response.status,
          response.statusText
        );
        let errorDetails = `API Error: ${response.status} ${response.statusText}`;
        const responseCloneForJson = response.clone();
        const responseCloneForText = response.clone();
        try {
          const apiError = await responseCloneForJson.json();
          console.error("API error response (JSON):", apiError);
          const message =
            apiError?.clientErrorDetail?.message ||
            apiError?.message ||
            errorDetails;
          return handleError(message);
        } catch (jsonError) {
          console.error(
            "Failed to parse API error response as JSON:",
            jsonError
          );
          try {
            const textError = await responseCloneForText.text();
            console.error("API error response (text):", textError);
            errorDetails += `\nResponse body: ${textError}`;
          } catch (textParseError) {
            console.error(
              "Failed to read API error response as text:",
              textParseError
            );
          }
        }
        return handleError(errorDetails);
      }

      const apiResponse: ApiResponse = await response.json();

      // Get the root realm and transform it
      const rootRealm = apiResponse.realms[0];
      if (!rootRealm) {
        return handleError("No realm data received from server");
      }

      const transformedRealm = convertToRealm(rootRealm);
      setRealmHierarchy(transformedRealm);
      setSelectedRealm(transformedRealm);
    } catch (err) {
      console.error("Network or other error fetching realm hierarchy:", err);
      handleError(
        "Failed to fetch realm hierarchy. Check console for details."
      );
    }
  }, [configuration.apiBaseURL, getSession, handleError, topRealm]); //fetch all privilege actions
  const fetchPrivilegeActions = useCallback(async () => {
    const [session, error] = await getSession();
    if (error || !session) return handleError(error.message);

    if (!topRealm || !topRealm.eidURN || !topRealm.eidURN.startsWith("urn:")) {
      console.error(
        "Top realm EID is not available or not a valid URN for fetching privilege actions.",
        topRealm
      );
      return handleError(
        "Top realm information is missing or invalid (requires URN format) for privilege actions."
      );
    }
    if (!configuration.apiBaseURL) {
      console.error(
        "API Base URL is not configured for fetching privilege actions."
      );
      return handleError("API Base URL is not configured.");
    }

    const url = new URL("/v1/privilege-actions", configuration.apiBaseURL);
    const headers = new Headers();
    headers.set(
      "Authorization",
      `Bearer ${session.getAccessToken().getJwtToken()}`
    );
    headers.set("X-Ayode-Asserted-Realm-EID", topRealm.eidURN);

    try {
      const response = await fetch(url, { method: "GET", headers });
      if (!response.ok) {
        const apiError = await response.json();
        const message =
          apiError?.clientErrorDetail?.message ||
          apiError?.message ||
          "Failed to fetch privilege actions";
        return handleError(message);
      }

      const actions: PrivilegeAction[] = await response.json();
      setPrivilegeActions(
        actions.sort((a, b) => a.name.en_US.localeCompare(b.name.en_US))
      );
    } catch (err) {
      console.error("Error fetching privilege actions:", err);
      handleError(
        "Failed to fetch privilege actions. Check console for details."
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const fetchPrivilegeMatrix = useCallback(
    async (realm: Realm) => {
      if (!realm) return;

      if (!realm.realmPath) {
        console.error("Selected realm path is not available.", realm);
        return handleError("Selected realm path information is missing.");
      }

      if (
        !topRealm ||
        !topRealm.eidURN ||
        !topRealm.eidURN.startsWith("urn:")
      ) {
        console.error(
          "Top realm EID is not available or not a valid URN for setting assertion header in fetchPrivilegeMatrix.",
          topRealm
        );
        return handleError(
          "Top realm information is missing or invalid (requires URN format) for request assertion."
        );
      }
      if (!configuration.apiBaseURL) {
        console.error(
          "API Base URL is not configured for fetching privilege matrix."
        );
        return handleError("API Base URL is not configured.");
      }

      setLoading(true);
      const [session, error] = await getSession();
      if (error || !session) {
        setLoading(false);
        return handleError(error.message);
      }
      const realmUUID = realm.eidURN.split(":").pop(); // Extract only the UUID
      const url = new URL(
        `/v1/privilege-matrix/${realmUUID}`,
        configuration.apiBaseURL
      );
      const headers = new Headers();
      headers.set(
        "Authorization",
        `Bearer ${session.getAccessToken().getJwtToken()}`
      );
      headers.set("X-Ayode-Asserted-Realm-EID", topRealm.eidURN);

      try {
        const response = await fetch(url, { method: "GET", headers });
        if (!response.ok) {
          const apiError = await response.json();
          const message =
            apiError?.clientErrorDetail?.message ||
            apiError?.message ||
            "Failed to fetch privilege matrix";
          handleError(message);
          setPrivilegeMatrix([]);
        } else {
          const matrix: PrivilegeMatrixEntry[] = await response.json();
          setPrivilegeMatrix(matrix);
        }
      } catch (err) {
        console.error("Error fetching privilege matrix:", err);
        handleError(
          "Failed to fetch privilege matrix. Check console for details."
        );
        setPrivilegeMatrix([]);
      } finally {
        setLoading(false);
        fetchInProgressRef.current = null;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  //Handle realm selection
  const handleRealmSelect = useCallback((realm: Realm) => {
    setSelectedRealm(realm);
  }, []); //Initial data fetch
  useEffect(() => {
    fetchRealmHierarchy();
    fetchPrivilegeActions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Intentionally empty to run only once on mount

  //Fetch matrix when selected realm changes
  useEffect(() => {
    if (
      selectedRealm &&
      selectedRealm.realmPath !== fetchInProgressRef.current
    ) {
      fetchInProgressRef.current = selectedRealm.realmPath;
      fetchPrivilegeMatrix(selectedRealm);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRealm]); // Only depend on selectedRealm

  return (
    <div className="h-full flex flex-col md:flex-row">
      <div className="w-full md:w-1/3 border-r border-gray-300 p-4 overflow-y-auto">
        <h2 className="text-lg font-bold mb-4">Realm Hierarchy</h2>
        {realmHierarchy ? (
          <RealmHierarchyTree
            realm={realmHierarchy}
            selectedRealm={selectedRealm}
            onRealmSelect={handleRealmSelect}
          />
        ) : (
          <div className="flex items-center justify-center h-32">
            <div className="loading loading-spinner"></div>
          </div>
        )}
      </div>

      <div className="flex-1 p-4 overflow-hidden flex flex-col gap-4">
        <div>
          <h2 className="text-lg font-bold">Privilege Matrix</h2>
          {selectedRealm && (
            <p className="text-sm text-gray-600">
              Selected Realm:{" "}
              <span className="font-medium">{selectedRealm.displayName}</span>
            </p>
          )}
        </div>

        {selectedRealm ? (
          <PrivilegeMatrix
            privilegeActions={privilegeActions}
            privilegeMatrix={privilegeMatrix}
            loading={loading}
          />
        ) : (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <p>
              Select a realm from the hierarchy to view its privilege matrix
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
