import Group from "@/root/narp/app/asset-manager/_components/details/Group.tsx";
import Entry from "@/root/narp/app/asset-manager/_components/details/Entry.tsx";
import { DirectoryNode } from "@/root/narp/app/asset-manager/_components/types.ts";
import { useParams } from "react-router-dom";

export default function DirectoryDetails({ node }: { node: DirectoryNode }) {
  const { "*": splat } = useParams();

  if (splat === undefined) return <></>;
  const name = splat.split("/").slice(-2, -1).at(0);

  return (
    <div className="min-h-full min-w-full flex flex-col gap-3">
      <Group label="Metadata">
        <Entry label="Name" value={name || "root"} />
        <Entry label="EID" value={node.userEID} copyable />
        <Entry label="Realm EID" value={node.realmEID} copyable />
        <Entry
          label="Contained Directory Count"
          value={node.containedDirectoryCount.toString()}
        />
        <Entry
          label="Contained File Count"
          value={node.containedFileCount.toString()}
        />
        <Entry
          label="Contained File Version Count"
          value={node.containedFileCount.toString()}
        />
        <Entry label="Physical Size" value={`${node.physicalSizeInBytes} B`} />
        <Entry
          label="Created At"
          value={new Date(node.createdTimestamp).toLocaleString()}
        />
        <Entry
          label="Updated At"
          value={new Date(node.updatedTimestamp).toLocaleString()}
        />
      </Group>

      <Group label="Raw">
        <pre>{JSON.stringify(node, null, 2)}</pre>
      </Group>
    </div>
  );
}
