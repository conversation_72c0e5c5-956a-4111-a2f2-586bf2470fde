import { useAlerts } from "@/util/components/Alerts/Context.tsx";
import { FaXmark } from "react-icons/fa6";
import { MouseEvent } from "react";
import { IoWarningOutline } from "react-icons/io5";
import { IconContext } from "react-icons";

export default function WarningAlert() {
  const { activeAlert, popAlert } = useAlerts();

  function handleDismiss(e: MouseEvent) {
    e.preventDefault();
    popAlert();
  }

  if (!activeAlert) return <></>;
  return (
    <div
      role={"alert"}
      className={`text-neutral-050 rounded-xl p-4 w-full flex flex-row ${activeAlert.variantColor ? "bg-yellow-700" : "bg-neutral-900"}`}
    >
      <IconContext.Provider
        value={{
          className: "absolute top-0 left-0 m-4",
          size: "40",
          color: activeAlert.variantColor ? "#FFFFFF" : "#CA8A04",
        }}
      >
        <IoWarningOutline className="" />
      </IconContext.Provider>

      <div className="flex flex-col ml-14 mr-4">
        <h1 className="font-black text-2xl align-middle mt-auto mb-auto">
          {activeAlert.message ? activeAlert.message : ""}
        </h1>
        <p className="overflow-y-scroll font-light min-w-auto max-h-13">
          {activeAlert.description ? activeAlert.description : ""}
        </p>{" "}
      </div>
      {/* Title ^ */}
      {/* Description ^ */}
      <div className="ml-auto font-mono flex flex-row space-x-10 col-span-1">
        <div className="p-2">
          {activeAlert.callToActionLink ? (
            <a
              className="ml-auto"
              href={activeAlert.callToActionLink}
              target="_blank"
            >
              {activeAlert.callToActionText
                ? activeAlert.callToActionText
                : activeAlert.callToActionLink}
            </a>
          ) : (
            ""
          )}
        </div>
        <button
          type={"button"}
          onClick={handleDismiss}
          className={"btn btn-ghost btn-square ml-auto mb-auto"}
        >
          <IconContext.Provider
            value={{
              className: "shared-class",
              size: "40",
              attr: { strokeWidth: "1" },
            }}
          >
            <FaXmark />
          </IconContext.Provider>
        </button>
      </div>
    </div>
  );
}
