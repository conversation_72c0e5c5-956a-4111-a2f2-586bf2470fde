import { RouteObject } from "react-router-dom";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";
import { default as DashboardRoute } from "./_index/Route";
import { default as ImportRoute } from "./import/Route";
import { default as RepositoryRoute } from "./repository/Route";
import { default as ExportRoute } from "./export/Route";
import { default as AnalyticsRoute } from "./analytics/Route";
import { default as OrganizationRoute } from "./organization/Route";
import { default as UsersRoute } from "./users/Route";
import { default as AccessibilityRoute } from "./accessibility/Route";

const Route: RouteObject = {
  path: "lor",
  hydrateFallbackElement: <PageSkeleton />,
  lazy: () => import("./Component.tsx"),
  children: [
    DashboardRoute,
    ImportRoute,
    RepositoryRoute,
    ExportRoute,
    AnalyticsRoute,
    OrganizationRoute,
    UsersRoute,
    AccessibilityRoute
  ],
};

export default Route;