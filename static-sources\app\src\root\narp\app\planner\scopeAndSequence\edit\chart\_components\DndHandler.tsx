import {
  CollisionDescriptor,
  CollisionDetection,
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  KeyboardSensor,
  MouseSensor,
  rectIntersection,
  TouchSensor,
  useDndContext,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { ReactNode } from "react";
import {
  DragData,
  DropData,
  useChartContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";
import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaCrosshairs } from "react-icons/fa6";

const collisionDetection: CollisionDetection = (args) => {
  const activeData = args.active.data.current as DragData | undefined;
  if (!activeData) throw new Error("missing drag data");

  const collisions = rectIntersection(args) as unknown as CollisionDescriptor[];

  return collisions.filter(
    (collision) =>
      collision.data.droppableContainer.data.current?.type ===
        activeData.type &&
      collision.data.droppableContainer.data.current?.topic?.id !==
        activeData.topic.id
  );
};

export default function DndHandler({ children }: { children: ReactNode }) {
  const { pixelsToTimestamp } = useChartContext();
  const {
    topic: { setStart, setDuration, addDependency, removeDependency },
  } = useEditorContext();

  function handleDragEnd(e: DragEndEvent) {
    // Restore default cursor and selection
    document.body.style.cursor = "auto";
    document.body.style.userSelect = "auto";
    document.body.style.webkitUserSelect = "auto";

    const activeData = e.active.data.current as DragData | undefined;
    if (!activeData) return console.error("active element missing data");
    const delta = Math.round(pixelsToTimestamp(e.delta.x));

    if (activeData.type === "SET_START") {
      setStart(activeData.topic.id, activeData.topic.start + delta);
    } else if (activeData.type === "SET_DURATION") {
      setDuration(activeData.topic.id, activeData.topic.duration + delta);
    } else if (activeData.type === "SET_DEPENDENCY") {
      if (!e.over) return;
      const overData = e.over.data.current as DropData | undefined;
      if (!overData) return console.error("over element missing data");

      if (overData.topic.id === activeData.topic.id) return;

      if (
        overData.topic.dependencies.findIndex(
          (entry) => entry === activeData.topic.id
        ) === -1
      ) {
        addDependency(overData.topic.id, activeData.topic.id);
      } else {
        removeDependency(overData.topic.id, activeData.topic.id);
      }
    }
  }

  function handleDragStart(e: DragStartEvent) {
    const activeData = e.active.data.current as DragData | undefined;
    if (!activeData) return console.error("active element missing data");

    // Hide the default mouse cursor completely during drag
    document.body.style.cursor = "none";

    // Prevent text selection during drag
    document.body.style.userSelect = "none";
    document.body.style.webkitUserSelect = "none";
  }

  return (
    <DndContext
      collisionDetection={collisionDetection}
      sensors={useSensors(
        useSensor(MouseSensor),
        useSensor(TouchSensor),
        useSensor(KeyboardSensor)
      )}
      onDragEnd={handleDragEnd}
      onDragStart={handleDragStart}
    >
      {children}
      <Overlay />
    </DndContext>
  );
}

function Overlay() {
  const context = useDndContext();

  return (
    <DragOverlay dropAnimation={null}>
      {context.active && (
        <div className="pointer-events-none flex items-center justify-center">
          <FaCrosshairs className="size-8 text-blue-500" />
        </div>
      )}
    </DragOverlay>
  );
}
