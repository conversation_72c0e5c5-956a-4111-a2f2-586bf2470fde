import { NavLink, useNavigate, useParams } from "react-router-dom";
import { FaLock } from "react-icons/fa6";
import { FaUser } from "react-icons/fa";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import { FormEvent, useState } from "react";

export function Component() {
  const { username } = useParams();

  const navigate = useNavigate();
  const { verifyAccount } = useAuth();

  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const username = data.get("username")?.toString();
    const code = data.get("code")?.toString();

    if (username === undefined || code === undefined)
      return setError(new Error("Missing fields"));

    setLoading(true);
    const error = await verifyAccount(code, username);
    setLoading(false);

    if (error) return setError(error);

    return navigate("/narp/auth");
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5">
      <form
        onSubmit={handleSubmit}
        className="max-w-xs w-full p-5 flex flex-col gap-3"
      >
        <input hidden readOnly name={"redirect"} value={"true"} />

        <div className={"text-xl font-bold text-center my-3"}>
          Verify Account
        </div>

        <div>
          <div className="text-center">
            Check your email for verification code.
          </div>
          <div className="text-center">
            If you can't find the email, <b>check your spam folder</b>.
          </div>
        </div>

        {username ? (
          <input hidden readOnly name={"username"} value={username} />
        ) : (
          <fieldset className={"fieldset gap-0"}>
            <label className={"input floating-label validator"}>
              <span>Username</span>
              <FaUser className={"text-base-content/20"} />
              <input
                name={"username"}
                type={"text"}
                required
                autoComplete={"username"}
                placeholder={"Username"}
              />
            </label>
            <p className={"validator-hint hidden"}>Required</p>
          </fieldset>
        )}

        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Verification Code</span>
            <FaLock className={"text-base-content/20"} />
            <input
              name={"code"}
              type={"text"}
              required
              autoComplete={"one-time-code"}
              placeholder={"Verification Code"}
              pattern={"[0-9]{1,6}"}
            />
          </label>
          <p className={"validator-hint hidden"}>Required, 6 Digit Code</p>
        </fieldset>

        <button
          disabled={loading}
          type="submit"
          className={`btn ${error ? "btn-error" : "btn-primary"}`}
        >
          {loading ? <div className="loading" /> : <>Register</>}
        </button>

        {error ? (
          <div className="text-error mt-1.5 text-center">{error?.message}</div>
        ) : (
          <></>
        )}

        <div className="divider m-0">OR</div>

        <NavLink className="btn btn-secondary no-animation mb-1.5" to="../">
          Back To Sign In
        </NavLink>
      </form>
    </div>
  );
}
