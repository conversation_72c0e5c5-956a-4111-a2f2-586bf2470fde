import {
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { useChartContext } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";
import { remToPx } from "@/util/unitConversions.ts";
import { useMemo } from "react";

export function DependencyArrows() {
  const {
    topic: { entries, get },
  } = useEditorContext();
  const { timestampToPixels } = useChartContext();

  const indexMap = useMemo(() => {
    return new Map<EntityID, number>(
      entries.map((entry, index) => [entry.id, index])
    );
  }, [entries]);

  return (
    <svg
      className="absolute -z-10 size-full text-blue-300"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <marker
          markerWidth="5"
          markerHeight="5"
          refX="5"
          refY="2.5"
          viewBox="0 0 5 5"
          orient="auto"
          id="arrow"
        >
          <polygon points="0,5 2.5,2.5 0,0 5,2.5" fill="current"></polygon>
        </marker>

        <marker
          markerWidth="7"
          markerHeight="7"
          refX="5.5"
          refY="3.5"
          viewBox="0 0 7 7"
          orient="auto"
          id="arrow-head-round"
        >
          <polyline
            points="0,3.5 3.5,1.75 0,0"
            fill="none"
            strokeWidth="1."
            stroke="currentColor"
            strokeLinecap="round"
            transform="matrix(1,0,0,1,1,1.75)"
            strokeLinejoin="round"
          ></polyline>
        </marker>
      </defs>

      {entries
        .flatMap((topic, topicIndex) =>
          topic.dependencies.map((dependencyID, index: number) => {
            const dependencyIndex = indexMap.get(dependencyID);
            const dependency = get(dependencyID);
            if (dependencyIndex === undefined || !dependency) return null;

            // Validate inputs to prevent NaN values
            const dependencyStart = isFinite(dependency.start)
              ? dependency.start
              : 0;
            const dependencyDuration = isFinite(dependency.duration)
              ? dependency.duration
              : 1;
            const topicStart = isFinite(topic.start) ? topic.start : 0;

            const fromX = timestampToPixels(
              dependencyStart + dependencyDuration / 2
            );
            const fromY = remToPx((dependencyIndex + 1) * 4) + 2 * topicIndex;
            const toX = timestampToPixels(topicStart);
            const toY = remToPx((topicIndex + 1) * 4 - 2) + 2 * topicIndex;
            const radius = remToPx(2);

            // Additional validation to ensure all calculated values are finite
            if (
              !isFinite(fromX) ||
              !isFinite(fromY) ||
              !isFinite(toX) ||
              !isFinite(toY) ||
              !isFinite(radius)
            ) {
              return null;
            }

            return (
              <path
                className="stroke-current"
                d={`M ${fromX} ${fromY} L ${fromX} ${toY - 0.756 * radius * Math.sign(toY - fromY)} C ${fromX} ${toY} ${fromX + radius - 0.224 * radius * Math.sign(toY - fromY)} ${toY} ${fromX + radius - 0.224 * radius * Math.sign(toY - fromY)} ${toY} L ${toX} ${toY}`}
                markerEnd="url(#arrow-head-round)"
                strokeWidth={3}
                fill="transparent"
                key={topicIndex + "," + index}
              />
            );
          })
        )
        .filter(Boolean)}
    </svg>
  );
}
