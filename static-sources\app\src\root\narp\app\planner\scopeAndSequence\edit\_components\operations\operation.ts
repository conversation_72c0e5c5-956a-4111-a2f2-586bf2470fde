import { GoalOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/goal.ts";
import { OutlineOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/outline.ts";
import { TermOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/term.ts";
import { TopicOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/topic.ts";
import { ActivityOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/activity.ts";

export type Operation =
  | GoalOperation
  | OutlineOperation
  | TermOperation
  | TopicOperation
  | ActivityOperation;
