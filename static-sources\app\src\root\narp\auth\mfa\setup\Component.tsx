import { NavLink, useNavigate } from "react-router-dom";
import { QRCodeSVG } from "qrcode.react";
import { FaLock } from "react-icons/fa6";
import { FormEvent, useEffect, useState } from "react";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";

export function Component() {
  const { associateSoftware, verifySoftware, getUsername } = useAuth();
  const navigate = useNavigate();

  const [initializing, setInitializing] = useState<boolean>(true);
  const [initError, setInitError] = useState<Error | null>(null);
  const [secret, setSecret] = useState<string | null>(null);

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    setInitializing(true);
    associateSoftware()
      .then(([secret, error]) => {
        if (error || secret === null) return setInitError(error);
        setSecret(secret);
      })
      .finally(() => {
        setInitializing(false);
      });
  }, []);

  if (initializing) {
    return <PageSkeleton />;
  }

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const code = data.get("code")?.toString();

    if (code === undefined) return setError(new Error("Missing fields"));

    setLoading(true);
    const error = await verifySoftware(code);
    setLoading(false);

    if (error) return setError(error);
    return navigate("/narp/app");
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5">
      <form
        onSubmit={handleSubmit}
        className="max-w-xs w-full p-5 flex flex-col gap-3"
      >
        {initError ? (
          <div className="text-error mt-1.5 text-center">
            {initError?.message}
          </div>
        ) : (
          <>
            <div className="text-center">
              Scan this QR-Code with a two-factor authentication app and input
              the code that is generated.
            </div>

            <QRCodeSVG
              className={"p-5 bg-white rounded-lg size-full"}
              value={`otpauth://totp/${getUsername()}?secret=${secret}&issuer=AYODE-AUTH-TOTP`}
            />
            <a
              href={`otpauth://totp/${getUsername()}?secret=${secret}&issuer=AYODE-AUTH-TOTP`}
              className="link link-info mx-auto"
            >
              Or use this link
            </a>

            <fieldset className={"fieldset gap-0"}>
              <label className={"input floating-label validator"}>
                <span>MFA Code</span>
                <FaLock className={"text-base-content/20"} />
                <input
                  type={"text"}
                  required
                  name={"code"}
                  minLength={6}
                  maxLength={6}
                  placeholder={"MFA Code"}
                  pattern={"[0-9]{1,6}"}
                />
              </label>
              <p className={"validator-hint hidden"}>Required, 6 Digit Code</p>
            </fieldset>

            <button
              disabled={loading}
              type="submit"
              className={`btn ${error ? "btn-error" : "btn-primary"}`}
            >
              {loading ? <div className="loading" /> : <>Submit</>}
            </button>
          </>
        )}

        <div className="divider m-0">OR</div>

        <NavLink
          className="btn btn-secondary no-animation mb-1.5"
          to="/narp/auth"
        >
          Back To Sign In
        </NavLink>
      </form>
    </div>
  );
}
