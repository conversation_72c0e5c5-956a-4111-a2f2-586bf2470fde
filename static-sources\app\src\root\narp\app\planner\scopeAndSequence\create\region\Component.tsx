import { FormEvent, MouseEvent, useEffect, useMemo, useState } from "react";
import {
  Region,
  useCreateScopeSequenceContext,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import Fuse from "fuse.js";
import useGET from "@/util/api/useGET.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import {
  FaSearch,
  FaThumbtack,
  FaChevronDown,
  FaChevronRight,
} from "react-icons/fa";
import { FaUsersRectangle } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { hasFlag } from "country-flag-icons";
import getCountryFlag from "country-flag-icons/unicode";

// Region groupings for better organization
const REGION_GROUPS = {
  "North America": ["United States of America", "Canada", "Mexico"],
  Europe: [
    "United Kingdom",
    "Germany",
    "France",
    "Spain",
    "Italy",
    "Netherlands",
    "Belgium",
    "Austria",
    "Switzerland",
    "Portugal",
    "Ireland",
    "Norway",
    "Sweden",
    "Denmark",
    "Finland",
  ],
  Asia: [
    "China",
    "Japan",
    "India",
    "South Korea",
    "Singapore",
    "Malaysia",
    "Thailand",
    "Indonesia",
    "Philippines",
    "Vietnam",
  ],
  Oceania: ["Australia", "New Zealand"],
  "South America": [
    "Brazil",
    "Argentina",
    "Chile",
    "Colombia",
    "Peru",
    "Venezuela",
  ],
  Africa: ["South Africa", "Nigeria", "Kenya", "Egypt", "Morocco"],
  "Middle East": [
    "United Arab Emirates",
    "Saudi Arabia",
    "Israel",
    "Turkey",
    "Iran",
  ],
};

function getRegionGroup(regionName: string): string {
  for (const [groupName, countries] of Object.entries(REGION_GROUPS)) {
    if (
      countries.some(
        (country) =>
          regionName.toLowerCase().includes(country.toLowerCase()) ||
          country.toLowerCase().includes(regionName.toLowerCase())
      )
    ) {
      return groupName;
    }
  }
  return "Other Regions";
}

export function Component() {
  const GET = useGET();
  const topRealm = useTopRealm();
  const pushError = usePushError();

  const [region, setRegion] = useState<Region[] | null>(null);

  async function updateRegions() {
    const { error, response, data } = await GET(
      "/v1/worldwide-administrative-divisions",
      {
        assertedRealmEidUrn: topRealm.eidURN,
      }
    );

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (data === null) return pushError("Missing data.");

    const envelope: APIEnvelope<Region[]> = JSON.parse(await data.text());
    setRegion(envelope.data);
  }

  useEffect(() => {
    updateRegions();
  }, []);

  if (!region)
    return (
      <div className="grow flex items-center justify-center">
        <div className="loading loading-spinner loading-xl" />
      </div>
    );

  return <Content organizations={region} />;
}

function Content({ organizations }: { organizations: Region[] }) {
  const { region, setSubregion, setAuthority } =
    useCreateScopeSequenceContext();
  const navigate = useNavigate();

  const [continuable, setContinuable] = useState<boolean>(false);
  const [searchString, setSearchString] = useState<string>("");
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(["North America"])
  ); // North America expanded by default

  // Group regions by geographic area
  const groupedRegions = useMemo(() => {
    const groups: { [key: string]: Region[] } = {};

    for (const region of organizations) {
      const group = getRegionGroup(region.name);
      if (!groups[group]) {
        groups[group] = [];
      }
      groups[group].push(region);
    }

    // Sort regions within each group
    Object.keys(groups).forEach((group) => {
      groups[group].sort((a, b) => {
        // US first in North America
        if (group === "North America") {
          const aIsUS =
            a.code === "US" ||
            a.name.toLowerCase().includes("united states of america");
          const bIsUS =
            b.code === "US" ||
            b.name.toLowerCase().includes("united states of america");
          if (aIsUS && !bIsUS) return -1;
          if (!aIsUS && bIsUS) return 1;
        }
        return a.name.localeCompare(b.name);
      });
    });

    return groups;
  }, [organizations]);

  const searchEntries: Region[] = useMemo(() => {
    if (searchString == "" || !searchString) {
      return [];
    }

    const fuse = new Fuse<Region>(organizations, { keys: ["name"] });
    return fuse.search(searchString).map((result) => result.item);
  }, [organizations, searchString]);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  function toggleGroup(groupName: string) {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupName)) {
      newExpanded.delete(groupName);
    } else {
      newExpanded.add(groupName);
    }
    setExpandedGroups(newExpanded);
  }

  useEffect(() => {
    setSubregion(null);
    setAuthority(null);
    setContinuable(!!region);
  }, [region]);

  function handleToNext(e: MouseEvent) {
    e.preventDefault();
    navigate("../subregion");
  }

  return (
    <>
      <div className="flex flex-row justify-between px-5">
        <div className="flex flex-col gap-1">
          <div className="text-xl font-bold">Choose a Region</div>
          <div>Choose the region you want to base your plan off of.</div>
        </div>

        <label className="input input-lg">
          <FaSearch className=" text-neutral-300 shrink-0" />
          <input onInput={handleInput} type="text" placeholder="Search" />
        </label>
      </div>

      <div className="mb-auto touch-manipulation px-5">
        {searchString ? (
          // Show search results
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {searchEntries.map((entry, index) => (
              <RegionEntry
                region={entry}
                key={index}
                isPinned={
                  entry.code === "US" ||
                  entry.name
                    .toLowerCase()
                    .includes("united states of america") ||
                  entry.name.toLowerCase() === "united states of america"
                }
              />
            ))}
          </div>
        ) : (
          // Show grouped regions
          <div className="space-y-4">
            {Object.entries(groupedRegions)
              .sort(([a], [b]) => {
                // Prioritize North America, then alphabetical
                if (a === "North America") return -1;
                if (b === "North America") return 1;
                if (a === "Other Regions") return 1;
                if (b === "Other Regions") return -1;
                return a.localeCompare(b);
              })
              .map(([groupName, regions]) => (
                <div
                  key={groupName}
                  className="border border-neutral-200 rounded-lg overflow-hidden"
                >
                  <button
                    onClick={() => toggleGroup(groupName)}
                    className="w-full px-4 py-3 bg-neutral-50 hover:bg-neutral-100 flex items-center justify-between text-left font-semibold text-neutral-700"
                  >
                    <span>
                      {groupName} ({regions.length})
                    </span>
                    {expandedGroups.has(groupName) ? (
                      <FaChevronDown className="text-neutral-500" />
                    ) : (
                      <FaChevronRight className="text-neutral-500" />
                    )}
                  </button>
                  {expandedGroups.has(groupName) && (
                    <div className="p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                      {regions.map((entry, index) => (
                        <RegionEntry
                          region={entry}
                          key={index}
                          isPinned={
                            entry.code === "US" ||
                            entry.name
                              .toLowerCase()
                              .includes("united states of america") ||
                            entry.name.toLowerCase() ===
                              "united states of america"
                          }
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}
      </div>

      <div className="bg-base-100 shadow-lg shadow-black border-t border-t-base-content/20 p-5 gap-3 flex flex-row justify-end">
        <button
          onClick={handleToNext}
          type="button"
          disabled={!continuable}
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </>
  );
}

function getIcon(region: Region) {
  if (hasFlag(region.code))
    return (
      <div className="size-8 flex items-center justify-center text-2xl">
        {getCountryFlag(region.code)}
      </div>
    );

  return <FaUsersRectangle className="size-8" />;
}

function RegionEntry({
  region,
  isPinned,
}: {
  region: Region;
  isPinned?: boolean;
}) {
  const { region: currentRegion, setRegion } = useCreateScopeSequenceContext();
  const navigate = useNavigate();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setRegion(region);
  }

  function handleDoubleClick(e: MouseEvent) {
    e.preventDefault();
    setRegion(region);
    navigate("../subregion");
  }

  return (
    <button
      title={region.name}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      type="button"
      className={`touch-pan-y btn btn-outline gap-3 ${currentRegion?.code === region.code && "outline-2 outline-offset-2"} ${isPinned ? "ring-2 ring-primary/20" : ""}`}
    >
      <div className="flex items-center gap-2">
        {isPinned && <FaThumbtack className="text-primary text-sm" />}
        {getIcon(region)}
      </div>

      <div className="grow text-start truncate">{region.name}</div>
    </button>
  );
}
