import {
  Term,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaEdit } from "react-icons/fa";
import { FaWandMagicSparkles } from "react-icons/fa6";
import {
  Dispatch,
  FocusEvent,
  FormEvent,
  MouseEvent,
  SetStateAction,
  useRef,
  useState,
} from "react";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import usePOST from "@/util/api/usePOST.tsx";

type Query = {
  realmEID: string;
  vocabularyWord: {
    phrase: string;
    target: {
      age: number;
      domain: string;
      lexicalCategory: string;
    };
  };
};

type QueryResult = [
  {
    definition: {
      en_US: string;
      es_ES: string;
      fr_FR: string;
    };
  },
];

export default function DefinitionField({
  term,
  generating,
  setGenerating,
}: {
  term: Term;
  generating: boolean;
  setGenerating: Dispatch<SetStateAction<boolean>>;
}) {
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const {
    outline: { gradeLevel, title },
    term: { setDefinition },
  } = useEditorContext();

  const POST = usePOST();

  const [displayValue, setDisplayValue] = useState<string>(term.definition);

  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  function handleInput(e: FormEvent<HTMLTextAreaElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setDefinition(term.id, displayValue);
  }

  async function handleGenerate(e: MouseEvent) {
    e.preventDefault();
    if (generating) return;

    const body: Query = {
      realmEID: topRealm.externalID,
      vocabularyWord: {
        phrase: term.word,
        target: {
          age: 5 + gradeLevel,
          domain: title,
          lexicalCategory: term.category,
        },
      },
    };

    setGenerating(true);
    const { error, response, data } = await POST(
      "/v1/ai/vocabulary/definitions/generate",
      {
        body: JSON.stringify(body),
        assertedRealmEidUrn: topRealm.eidURN,
      },
    );
    setGenerating(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    const envelope: APIEnvelope<QueryResult> = JSON.parse(await data.text());
    const newDefinition = envelope.data[0].definition.en_US;
    setDisplayValue(newDefinition);
    setDefinition(term.id, newDefinition);
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget === buttonRef.current ||
      e.relatedTarget === inputRef.current
    )
      return;
    setDisplayValue(term.definition);
  }

  return (
    <div className="flex flex-col">
      <div className="font-bold">Definition</div>
      <div className="flex flex-row gap-3">
        <textarea
          onBlur={handleBlur}
          onInput={handleInput}
          disabled={generating}
          tabIndex={0}
          ref={inputRef}
          value={displayValue}
          autoComplete="off"
          className="textarea textarea-lg grow"
        />
        <div className="flex flex-col gap-3">
          <div className="tooltip" data-tip="Confirm Edit">
            <button
              onBlur={handleBlur}
              onClick={handleClick}
              disabled={generating || displayValue === term.definition}
              ref={buttonRef}
              tabIndex={0}
              type="button"
              className="btn btn-lg btn-square btn-primary"
            >
              <FaEdit />
            </button>
          </div>

          <div className="tooltip" data-tip="Generate Definition">
            <button
              disabled={generating}
              onClick={handleGenerate}
              tabIndex={0}
              type="button"
              className={`btn btn-lg btn-square btn-primary ${generating && "animate-bounce"}`}
            >
              {generating ? (
                <div className="loading loading-bars" />
              ) : (
                <FaWandMagicSparkles />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
