import { useState } from "react";
import { Info } from "lucide-react";
import { LORLevel } from "./LORContext";

interface LORInfoButtonProps {
  currentLevel: LORLevel;
  className?: string;
}

interface LORLevelInfo {
  features: string[];
  limitations: string[];
  proTip: string;
  color: string;
}

const lorLevelInfo: Record<string, LORLevelInfo> = {
  personal: {
    features: [
      "Private content storage",
      "Personal organization system",
      "Individual curriculum planning",
      "Full editing permissions",
    ],
    limitations: [
      "Content not shared with others",
      "Limited collaboration features",
    ],
    proTip:
      "Pro tip: Switch between LOR levels to access different scopes of content and collaboration features tailored to your role.",
    color: "text-blue-600 bg-blue-50 border-blue-200",
  },
  classroom: {
    features: [
      "Shared class content",
      "Curriculum alignment tools",
      "Parent visibility options",
      "Student assignment tracking",
    ],
    limitations: [
      "Content and sharing with others",
      "Limited collaboration features",
    ],
    proTip:
      "Pro tip: Switch between LOR levels to access different scopes of content and collaboration features tailored to your role.",
    color: "text-green-600 bg-green-50 border-green-200",
  },
  school: {
    features: [
      "Cross-grade collaboration",
      "School-wide curriculum standards",
      "Administrative oversight",
      "Department resource sharing",
    ],
    limitations: [
      "Bulk content compliance required",
      "Administrative approval required for major changes",
    ],
    proTip:
      "Pro tip: Switch between LOR levels to access different scopes of content and collaboration features tailored to your role.",
    color: "text-purple-600 bg-purple-50 border-purple-200",
  },
  district: {
    features: [
      "Multi-school access",
      "District-wide curriculum standards",
      "Professional development resources",
      "Data analytics across schools",
    ],
    limitations: [
      "District-wide compliance required",
      "Major policy restrictions",
    ],
    proTip:
      "Pro tip: Switch between LOR levels to access different scopes of content and collaboration features tailored to your role.",
    color: "text-orange-600 bg-orange-50 border-orange-200",
  },
  state: {
    features: [
      "State standards alignment",
      "Statewide resource sharing",
      "Policy implementation tools",
      "Professional development",
    ],
    limitations: ["State policy for most content", "Limited personalization"],
    proTip:
      "Pro tip: Switch between LOR levels to access different scopes of content and collaboration features tailored to your role.",
    color: "text-red-600 bg-red-50 border-red-200",
  },
  national: {
    features: [
      "National standards alignment",
      "Research-based content",
      "Best practices repository",
      "Cross-state collaboration",
    ],
    limitations: [
      "Read-only for most content",
      "Formal approval processes",
      "Limited real-time updates",
    ],
    proTip:
      "Pro tip: Switch between LOR levels to access different scopes of content and collaboration features tailored to your role.",
    color: "text-indigo-600 bg-indigo-50 border-indigo-200",
  },
};

export default function LORInfoButton({
  currentLevel,
  className = "",
}: LORInfoButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const info = lorLevelInfo[currentLevel.type];

  if (!info) return null;

  return (
    <div
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Info Button */}
      <button className="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
        <Info className="w-4 h-4 text-gray-600" />
      </button>

      {/* Hover Tooltip */}
      {isHovered && (
        <>
          {/* Backdrop to prevent flickering */}
          <div className="fixed inset-0 pointer-events-none z-30" />

          {/* Tooltip Content */}
          <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg border border-gray-200 shadow-xl z-40 p-4">
            {/* Header */}
            <div className="flex items-center gap-2 mb-3">
              <div
                className={`w-8 h-8 rounded-lg border flex items-center justify-center ${info.color}`}
              >
                <span className="text-sm font-semibold">
                  {currentLevel.type.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">
                  {currentLevel.name}
                </h3>
                <p className="text-xs text-gray-600">
                  {currentLevel.description}
                </p>
              </div>
            </div>

            {/* Available Features */}
            <div className="mb-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-1">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Available Features
              </h4>
              <ul className="space-y-1">
                {info.features.map((feature, index) => (
                  <li
                    key={index}
                    className="text-sm text-gray-600 flex items-start gap-2"
                  >
                    <span className="text-green-500 mt-0.5">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Current Limitations */}
            <div className="mb-4">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-1">
                <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                Current Limitations
              </h4>
              <ul className="space-y-1">
                {info.limitations.map((limitation, index) => (
                  <li
                    key={index}
                    className="text-sm text-gray-600 flex items-start gap-2"
                  >
                    <span className="text-red-500 mt-0.5">•</span>
                    {limitation}
                  </li>
                ))}
              </ul>
            </div>

            {/* Pro Tip */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <div className="w-4 h-4 bg-blue-400 rounded-full flex-shrink-0 mt-0.5">
                  <span className="block w-2 h-2 bg-white rounded-full mx-auto mt-1"></span>
                </div>
                <div className="text-sm">
                  <p className="font-medium text-blue-800 mb-1">Pro tip:</p>
                  <p className="text-blue-700">{info.proTip}</p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
