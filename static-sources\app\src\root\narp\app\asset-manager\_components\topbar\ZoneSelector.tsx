import { FocusEvent, FormEvent, MouseEvent, useState } from "react";
import { FaRightToBracket } from "react-icons/fa6";
import { useNavigate, useParams } from "react-router-dom";

export default function ZoneSelector() {
  const navigate = useNavigate();
  const { realmEID, zone } = useParams();

  const [value, setValue] = useState<string>(zone || "~");

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setValue(e.currentTarget.value);
  }

  function handleBlur(e: FocusEvent<HTMLLabelElement>) {
    if (e.currentTarget.contains(e.relatedTarget)) return;
    setValue(zone || "~");
  }

  function handleClick(e: MouseEvent<HTMLButtonElement>) {
    e.preventDefault();

    navigate(`/narp/app/asset-manager/${realmEID}/${value}/`);
  }

  return (
    <label className="floating-label max-w-xs" onBlur={handleBlur}>
      <span>Zone</span>
      <label className="input input-sm flex flex-row group pe-1">
        <input
          disabled={!realmEID}
          onInput={handleInput}
          autoComplete="off"
          name="zone"
          type="text"
          value={value}
        />
        <div className="tooltip" data-tip={"Confirm"}>
          <button
            disabled={!realmEID}
            type="submit"
            className="invisible pointer-events-none group-focus-within:pointer-events-auto group-focus-within:visible btn btn-square btn-xs btn-primary"
            onClick={handleClick}
          >
            <FaRightToBracket />
          </button>
        </div>
      </label>
    </label>
  );
}
