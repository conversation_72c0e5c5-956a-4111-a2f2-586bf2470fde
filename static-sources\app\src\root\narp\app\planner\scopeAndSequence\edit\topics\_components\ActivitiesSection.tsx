import {
  Activity,
  ActivityType,
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import {
  DragData,
  DropData,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";
import { useDraggable, useDroppable } from "@dnd-kit/core";
import {
  FaDice,
  FaGripLinesVertical,
  FaPlus,
  FaWandMagicSparkles,
  FaXmark,
  FaPenToSquare,
} from "react-icons/fa6";
import SearchSelector from "@/util/components/SearchSelector.tsx";
import { MouseEvent, useMemo, useRef, useState } from "react";
import usePOST from "@/util/api/usePOST.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";
import AddActivityDialog, {
  AddActivityDialogRef,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/AddActivityDialog.tsx";
import GeneratingActivitiesOverlay from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/GeneratingActivitiesOverlay.tsx";
import ActivityEditModal from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/ActivityEditModal.tsx";

export function ActivitiesSection({ topic }: { topic: Topic }) {
  const {
    outline: { gradeLevel, keywords },
    activity: { setParentTopic, entries, add },
    topic: { getActivities, getGoals, getTerms },
  } = useEditorContext();
  const topRealm = useTopRealm();
  const POST = usePOST();
  const pushError = usePushError();

  const [generating, setGenerating] = useState<boolean>(false);
  const [editingActivityId, setEditingActivityId] = useState<number | null>(
    null
  );

  const dialogRef = useRef<AddActivityDialogRef>(null);

  const activities = useMemo(() => {
    return getActivities(topic.id).sort((a, b) => (a.title > b.title ? 1 : -1));
  }, [getActivities, topic.id]);

  const dropData: DropData = {
    type: "ACTIVITY",
    topic: topic,
  };

  const { isOver, setNodeRef } = useDroppable({
    id: topic.id + ":activity",
    data: dropData,
    disabled: generating,
  });

  function handleSelect(selected: Activity) {
    setParentTopic(selected.id, topic.id);
  }

  async function handleGenerate(e: MouseEvent) {
    e.preventDefault();

    const payload = {
      realmEID: topRealm.externalID,
      subjects: {
        topics: [
          ...getGoals(topic.id).map((entry) => entry.description),
          ...getTerms(topic.id).map((entry) => entry.word),
          ...keywords,
        ],
        target: {
          age: gradeLevel + 5,
          domain: topic.name,
        },
      },
    };

    setGenerating(true);
    const { response, data, error } = await POST(
      "/v1/ai/curricula/activities/generate",
      {
        assertedRealmEidUrn: topRealm.eidURN,
        body: JSON.stringify(payload),
      }
    );
    setGenerating(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    // remove existing activities
    for (const activity of activities) {
      setParentTopic(activity.id, null);
    }

    // parse response
    const envelope: APIEnvelope<{
      concepts: [{ en_US: string; es_ES: string; fr_FR: string }];
    }> = JSON.parse(await data.text());

    const ids = entries.map((entry) => entry.id);

    for (const concept of envelope.data.concepts) {
      const newID = getNonCollidingID(ids);
      const tokens = concept.en_US.split(": ");
      add({
        id: newID,
        parentTopic: topic.id,
        title: tokens[0],
        type: ActivityType.CREATION,
        description: tokens[1],
        duration: "Not Set",
        resources: [],
        deliverables: [],
      });
      ids.push(newID);
    }
  }

  function handleSubmit(activity: Activity) {
    activity.parentTopic = topic.id;
    add(activity);
  }

  function handleEdit(activity: Activity) {
    setEditingActivityId(activity.id);
  }

  function handleCancelGeneration() {
    setGenerating(false);
  }

  return (
    <div>
      <GeneratingActivitiesOverlay
        isVisible={generating}
        onCancel={handleCancelGeneration}
      />
      <AddActivityDialog ref={dialogRef} onSubmit={handleSubmit} />
      <ActivityEditModal
        editID={editingActivityId}
        setEditID={setEditingActivityId}
      />

      <div className="text-xl mb-3 font-bold">Activities</div>

      <div
        ref={setNodeRef}
        className={`min-h-4 mb-3 gap-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 outline-2 outline-offset-2 rounded-lg ${isOver ? "outline-primary-400" : "outline-transparent"}`}
      >
        {activities.map((activity, index) => (
          <ActivityEntry activity={activity} key={index} onEdit={handleEdit} />
        ))}
      </div>

      <div className="flex flex-row gap-3">
        <div
          className="group relative tooltip grow"
          data-tip="Link Existing Activity from Another Topic"
        >
          <button
            tabIndex={0}
            disabled={generating}
            type="button"
            className="btn btn-sm w-full btn-success btn-outline"
          >
            <FaPlus />
            <span className="hidden sm:inline">Link Existing</span>
            <span className="sm:hidden">Link</span>
          </button>

          <div className="absolute top-full w-full z-10">
            <SearchSelector
              descriptors={entries
                .filter((entry) => entry.parentTopic !== topic.id)
                .map((entry) => {
                  return {
                    label: `${entry.title} (from ${entry.parentTopic ? "another topic" : "unassigned"})`,
                    data: entry,
                  };
                })}
              onSelect={handleSelect}
            />
          </div>
        </div>

        <div className="tooltip grow" data-tip="Create Brand New Activity">
          <button
            onClick={() => dialogRef.current?.open()}
            type="button"
            disabled={generating}
            className={`btn btn-sm w-full btn-primary btn-outline`}
          >
            <FaPlus />
            <span className="hidden sm:inline">Create New</span>
            <span className="sm:hidden">New</span>
          </button>
        </div>

        <div
          className="tooltip grow"
          data-tip="AI Generate Activities for This Topic"
        >
          <button
            onClick={handleGenerate}
            tabIndex={0}
            type="button"
            disabled={generating}
            className={`btn btn-sm w-full btn-secondary btn-outline ${generating && "animate-bounce"}`}
          >
            {generating ? (
              <div className="loading loading-sm loading-bars" />
            ) : (
              <FaWandMagicSparkles />
            )}
            <span className="hidden sm:inline">AI Generate</span>
            <span className="sm:hidden">AI</span>
          </button>
        </div>
      </div>
    </div>
  );
}

function ActivityEntry({
  activity,
  onEdit,
}: {
  activity: Activity;
  onEdit: (activity: Activity) => void;
}) {
  const {
    activity: { setParentTopic },
  } = useEditorContext();

  const dragData: DragData = {
    type: "ACTIVITY",
    activity: activity,
  };

  const { isDragging, setNodeRef, attributes, listeners } = useDraggable({
    id: activity.id + ":activity",
    data: dragData,
  });

  function handleRemove(e: MouseEvent) {
    e.preventDefault();
    setParentTopic(activity.id, null);
  }

  function handleEdit(e: MouseEvent) {
    e.preventDefault();
    onEdit(activity);
  }

  return (
    <div
      ref={setNodeRef}
      className={`flex flex-row p-3 gap-3 bg-white border border-neutral-300 rounded-lg ${isDragging ? "invisible" : "visible"}`}
    >
      <div
        {...attributes}
        {...listeners}
        className="h-full flex items-center justify-center cursor-grab"
      >
        <FaGripLinesVertical className="text-neutral-600" />
      </div>

      <FaDice className="size-6 shrink-0 text-neutral-600" />

      <div className="flex flex-col grow">
        <div className="font-bold text-lg">{activity.title} :</div>
        <div>🕒 {activity.duration}</div>
        <div>{activity.description}</div>

        <div className="flex flex-col mt-3">
          <div className="font-bold">Deliverables</div>
          <ol className="list list-disc list-inside">
            {activity.deliverables.length > 0 ? (
              activity.deliverables.map((deliverable, i) => (
                <li key={i}>{deliverable}</li>
              ))
            ) : (
              <span>No Deliverables</span>
            )}
          </ol>
        </div>

        <div className="flex flex-col mt-3">
          <div className="font-bold">Resources</div>
          <ol className="list list-disc list-inside">
            {activity.resources.length > 0 ? (
              activity.resources.map((resource, i) => (
                <li key={i}>{resource}</li>
              ))
            ) : (
              <span>No Activities</span>
            )}
          </ol>
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <div className="tooltip" data-tip="Edit Activity">
          <button
            tabIndex={0}
            type="button"
            onClick={handleEdit}
            className="btn btn-square btn-primary btn-outline"
          >
            <FaPenToSquare />
          </button>
        </div>

        <div className="tooltip" data-tip="Remove From Topic">
          <button
            tabIndex={0}
            type="button"
            onClick={handleRemove}
            className="btn btn-square"
          >
            <FaXmark />
          </button>
        </div>
      </div>
    </div>
  );
}
