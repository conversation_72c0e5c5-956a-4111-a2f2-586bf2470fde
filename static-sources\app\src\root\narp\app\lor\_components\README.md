# LOR Level Selection Feature

This document describes the LOR (Learning Object Repository) level selection feature implementation and the required backend API endpoints.

## Overview

The LOR level selection feature allows users to switch between different hierarchical levels of learning object repositories, from personal to national levels. Each level provides different access permissions and collaboration features.

## Components

### 1. LORContext.tsx

Provides React context for managing LOR levels state, including:

- Available LOR levels
- Current selected level
- Loading states
- Switch functionality

### 2. LORSelector.tsx

UI component that displays:

- Current LOR level button
- Dropdown modal with all available levels
- Level descriptions and icons
- Loading animations during switching

### 3. LoadingOverlay.tsx

Full-screen loading overlay shown during LOR level transitions.

## Data Structures

### LORLevel Interface

```typescript
interface LORLevel {
  id: string; // Unique identifier
  name: string; // Display name (e.g., "My Personal LOR")
  description: string; // Description text
  type: "personal" | "classroom" | "school" | "district" | "state" | "national";
  realmEID: string; // External realm identifier
  realmURN: string; // Realm URN
  icon?: string; // Optional icon identifier
  level: number; // Hierarchy level (1-6)
}
```

## Required API Endpoints

### 1. GET /v1/lor/levels/{realmEID}

**Purpose**: Fetch available LOR levels for a given realm

**Headers**:

- `Authorization: Bearer {jwt_token}`
- `X-Ayode-Asserted-Realm-EID: {realm_urn}`

**Response**:

```json
{
  "levels": [
    {
      "id": "personal",
      "name": "My Personal LOR",
      "description": "Your private learning object repository",
      "type": "personal",
      "realmEID": "realm_123",
      "realmURN": "urn:ayode:realm:123",
      "level": 1
    },
    {
      "id": "classroom",
      "name": "Ms. Johnson's 3rd Grade",
      "description": "Classroom-level repository for Grade 3",
      "type": "classroom",
      "realmEID": "realm_456",
      "realmURN": "urn:ayode:realm:456",
      "level": 2
    }
    // ... more levels
  ]
}
```

**Error Response**:

```json
{
  "error": {
    "code": "LOR_LEVELS_FETCH_FAILED",
    "message": "Failed to fetch LOR levels",
    "userMessage": "Unable to load available LOR levels"
  }
}
```

### 2. POST /v1/lor/switch

**Purpose**: Switch to a different LOR level

**Headers**:

- `Authorization: Bearer {jwt_token}`
- `X-Ayode-Asserted-Realm-EID: {realm_urn}`
- `Content-Type: application/json`

**Request Body**:

```json
{
  "targetLORLevel": "classroom",
  "realmEID": "realm_123"
}
```

**Response**:

```json
{
  "success": true,
  "currentLevel": {
    "id": "classroom",
    "name": "Ms. Johnson's 3rd Grade",
    "description": "Classroom-level repository for Grade 3",
    "type": "classroom",
    "realmEID": "realm_456",
    "realmURN": "urn:ayode:realm:456",
    "level": 2
  }
}
```

**Error Response**:

```json
{
  "success": false,
  "error": {
    "code": "LOR_SWITCH_FAILED",
    "message": "Failed to switch LOR level",
    "userMessage": "Unable to switch to the selected LOR level"
  }
}
```

## LOR Level Types

1. **Personal** (Level 1): Private user repository
2. **Classroom** (Level 2): Class-specific repository
3. **School** (Level 3): School-wide repository
4. **District** (Level 4): District-level repository
5. **State** (Level 5): State educational repository
6. **National** (Level 6): National education repository

## Implementation Notes

### Frontend Behavior

- Graceful fallback to mock data when API endpoints are not available
- Loading states during API calls
- Error handling with user-friendly messages
- Smooth animations and transitions

### Backend Requirements

- Realm-based access control
- Hierarchical permission system
- User context preservation during level switches
- Audit logging for level switches

### Security Considerations

- Validate user permissions for each LOR level
- Ensure proper realm assertion headers
- Log all level switch attempts
- Implement rate limiting for switch requests

## Testing

The feature includes mock data and fallback behavior for development and testing:

- Mock LOR levels are automatically generated
- API errors gracefully fall back to mock behavior
- Loading states are simulated with delays

## Future Enhancements

- Persistent LOR level preferences
- Level-specific permissions and features
- Batch operations across multiple levels
- Level analytics and usage tracking
