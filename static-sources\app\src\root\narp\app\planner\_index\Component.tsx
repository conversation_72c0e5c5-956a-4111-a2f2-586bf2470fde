import { NavLink } from "react-router-dom";
import { FaArrowRight } from "react-icons/fa6";
import { LuCalendarCheck } from "react-icons/lu";
import ScopeSequenceSVG from "./_assets/Calendar.svg";
import CurriculumPlanSVG from "./_assets/CurriculumPlan.svg";
import BreadCrumbs from "@/util/components/BreadCrumbs.tsx";
import ScopeAndSequences from "@/root/narp/app/planner/_index/_components/ScopeAndSequences.tsx";

export function Component() {
  return (
    <div className="size-full overflow-y-scroll">
      <div className="w-full p-5 flex flex-col gap-5">
        <BreadCrumbs
          crumbs={[
            {
              label: "Planner",
              icon: <LuCalendarCheck className="size-4" />,
              to: "/narp/planner",
            },
          ]}
        />

        <div className="font-bold text-3xl">Planner</div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div className="flex flex-col basis-1/2 border border-[#E4E7EC] p-5 bg-white rounded-lg">
            <div className="text-xl font-bold">Create Scope & Sequence</div>
            <div>
              We will develop a strategic plan detailing the necessary content
              and timeline to achieve your required standards.
            </div>
            <div className="flex flex-row flex-wrap mt-auto pt-5 items-end">
              <img className="me-auto" src={ScopeSequenceSVG} />
              <NavLink
                className="text-primary-600 flex flex-row items-center h-fit gap-1 text-xl font-bold hover:underline"
                to={"./scopeAndSequence"}
              >
                <span>Create Now</span>
                <FaArrowRight />
              </NavLink>
            </div>
          </div>

          <div className="flex flex-col basis-1/2 border border-[#E4E7EC] p-5 bg-white rounded-lg">
            <div className="text-xl font-bold">Join and Give Feedback!</div>
            <div>
              Join our discourse community, a place for educators to help
              improve education. Sign in now with your AYODE acocunt.
            </div>
            <div className="flex flex-row flex-wrap mt-auto pt-5 items-end">
              <img
                className="me-auto max-w-xs w-full"
                src={CurriculumPlanSVG}
              />
              <a
                href="https://discourse-apollo.www.codermerlin.academy/t/welcome-to-ayode-community-discussion-forums/7"
                target="_blank"
                className="text-primary-600 flex flex-row items-center h-fit gap-1 text-xl font-bold hover:underline"
              >
                <span>Join Now</span>
                <FaArrowRight />
              </a>
            </div>
          </div>
        </div>

        {/*<CurrentPlansSection/>*/}
        <ScopeAndSequences />
      </div>
    </div>
  );
}
