"use client";

import { useState, useCallback } from "react";
import Icon from "./Icon";
// Import parsing libraries for proper document parsing
import * as pdfjsLib from "pdfjs-dist";

pdfjsLib.GlobalWorkerOptions.workerSrc = `/pdf.worker.min.js`;

interface ProcessedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  content?: string;
  metadata?: Record<string, unknown>;
  uploadedAt: Date;
}

interface BulkImportComponentProps {
  onFileProcessed: (file: ProcessedFile) => void;
  processedFiles: ProcessedFile[];
  onFileRemoved?: (fileId: string) => void;
  onClearAllFiles?: () => void;
}

export default function BulkImportComponent({
  onFileProcessed,
  processedFiles,
}: BulkImportComponentProps) {
  const [dragActive, setDragActive] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [uploadStats, setUploadStats] = useState({
    total: 0,
    processed: 0,
    errors: 0,
  });
  const processFile = useCallback(
    async (file: File): Promise<void> => {
      console.log("=== STARTING BULK DOCUMENT PARSING ===");
      console.log("File name:", file.name);
      console.log("File type:", file.type);
      console.log("File size:", file.size);

      try {
        let content = "";
        let extractedImages: string[] = [];

        if (file.type === "application/pdf") {
          console.log("📄 Attempting PDF parsing...");
          content = await parsePDF(file);
          console.log("PDF parsing completed, content length:", content.length);
        } else if (
          file.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.name.endsWith(".docx")
        ) {
          console.log("📝 Attempting DOCX parsing...");
          content = await parseDocx(file);
          console.log(
            "DOCX parsing completed, content length:",
            content.length
          );
        } else if (
          file.type ===
            "application/vnd.openxmlformats-officedocument.presentationml.presentation" ||
          file.name.endsWith(".pptx") ||
          file.name.endsWith(".ppt")
        ) {
          console.log("📊 Attempting PowerPoint parsing...");
          content = await parsePowerPoint(file);
          console.log(
            "PowerPoint parsing completed, content length:",
            content.length
          );
        } else if (file.type.startsWith("image/")) {
          console.log("🖼️ Attempting image OCR...");
          const result = await parseImage(file);
          content = result.text;
          extractedImages = result.images;
          console.log("Image OCR completed, content length:", content.length);
        } else if (
          file.type.startsWith("text/") ||
          file.name.endsWith(".txt")
        ) {
          console.log("📄 Attempting text file parsing...");
          content = await parseTextFile(file);
          console.log(
            "Text file parsing completed, content length:",
            content.length
          );
        } else {
          console.log("❌ Unsupported file type:", file.type);
          content = "Unsupported file type for text extraction";
        }

        const processedFile: ProcessedFile = {
          id: `import-${Date.now()}-${Math.random()}`,
          name: file.name,
          type: file.type,
          size: file.size,
          content,
          metadata: {
            extractedImages: extractedImages.length,
            parsingMethod: getParsingMethod(file),
            extractedAt: new Date().toISOString(),
            wordCount: content.split(/\s+/).filter((word) => word.length > 0)
              .length,
            hasImages: extractedImages.length > 0,
            importMethod: "bulk",
          },
          uploadedAt: new Date(),
        };

        onFileProcessed(processedFile);
      } catch (error) {
        console.error("=== BULK PARSING ERROR ===");
        console.error("Error message:", error);
        console.error("File info:", {
          name: file.name,
          type: file.type,
          size: file.size,
        });

        // Create a file with error information
        const errorFile: ProcessedFile = {
          id: `import-error-${Date.now()}-${Math.random()}`,
          name: file.name,
          type: file.type,
          size: file.size,
          content: `❌ Failed to parse "${file.name}"\n\nError: ${
            error instanceof Error ? error.message : "Unknown error"
          }\n\nThis file could not be processed during bulk import.`,
          metadata: {
            parsingMethod: "Failed",
            error: error instanceof Error ? error.message : "Unknown error",
            importMethod: "bulk",
          },
          uploadedAt: new Date(),
        };
        onFileProcessed(errorFile);
        throw error; // Re-throw to increment error count
      }
    },
    [onFileProcessed]
  );

  const parsePDF = async (file: File): Promise<string> => {
    try {
      console.log("PDF.js version:", pdfjsLib.version);

      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      console.log("PDF file size:", file.size);
      console.log("ArrayBuffer length:", arrayBuffer.byteLength);
      console.log("Uint8Array length:", uint8Array.length);

      // Create loading task with browser-optimized options
      const loadingTask = pdfjsLib.getDocument({
        data: uint8Array,
        useSystemFonts: false,
        isEvalSupported: false,
        disableAutoFetch: true,
        disableStream: true,
        disableRange: true,
        useWorkerFetch: false,
      });

      console.log("PDF loading task created successfully");

      const pdf = await loadingTask.promise;
      console.log("PDF loaded successfully, pages:", pdf.numPages);

      let fullText = "";
      let hasValidText = false;
      let processedPages = 0;

      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          // Extract text items with improved handling
          const textItems: string[] = [];

          if (
            textContent &&
            textContent.items &&
            Array.isArray(textContent.items)
          ) {
            textContent.items.forEach((item) => {
              if (
                item &&
                typeof item === "object" &&
                "str" in item &&
                item.str
              ) {
                // Clean up the text and handle encoding properly
                let text = String(item.str);

                // Remove problematic characters and fix encoding
                text = text
                  .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, "") // Control characters
                  .replace(/\ufeff/g, "") // BOM
                  .replace(/\u00a0/g, " ") // Non-breaking space
                  .replace(/[\u2000-\u200F]/g, " ") // Unicode spaces
                  .replace(/[\u2028-\u2029]/g, "\n") // Line separators
                  .trim();

                if (
                  text &&
                  text.length > 0 &&
                  text !== " " &&
                  !/^[\s\u00a0]*$/.test(text)
                ) {
                  textItems.push(text);
                  hasValidText = true;
                }
              }
            });
          }

          // Join text items with proper spacing
          if (textItems.length > 0) {
            const pageText = textItems.join(" ").replace(/\s+/g, " ").trim();
            if (pageText && pageText.length > 0) {
              fullText += `\n\n## Page ${pageNum}\n\n${pageText}`;
              processedPages++;
            }
          }
        } catch (pageError) {
          console.warn(`Error processing page ${pageNum}:`, pageError);
          // Continue processing other pages
        }
      }

      // Clean up the full text
      fullText = fullText.trim();

      if (!hasValidText || fullText.length === 0) {
        return `📄 PDF Processing Complete (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Pages: ${pdf.numPages} (${processedPages} processed successfully)
Parsing Method: PDF.js (Browser)

⚠️ **No readable text found in this PDF**

**Possible reasons:**
- This is a scanned PDF (images only) - try the OCR feature instead
- Text is embedded as graphics/images rather than actual text
- The PDF uses complex formatting that makes text extraction difficult

---
✅ PDF file is valid and was processed successfully
⚠️ No extractable text found - this is common with image-based PDFs`;
      }

      return `📄 PDF Content Extracted Successfully (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Pages: ${pdf.numPages} (${processedPages} with text)
Parsing Method: PDF.js (Browser)

${fullText}

---
✅ Successfully extracted text using Mozilla PDF.js
✅ Content processed and cleaned
✅ No external dependencies required for processing`;
    } catch (error) {
      console.error("PDF parsing error:", error);
      return `❌ PDF Parsing Failed (Bulk Import)

File: ${file.name}
Error: ${error instanceof Error ? error.message : "Unknown error"}

This PDF may have:
• Password protection or security restrictions
• Corrupted or malformed structure
• Complex embedded content that requires specialized handling

Recommended alternatives:
• Try a different PDF file
• Convert to a different format first
• Use server-side processing for complex documents`;
    }
  };

  const parseDocx = async (file: File): Promise<string> => {
    try {
      // Dynamic import to avoid SSR issues
      console.log("Attempting to import mammoth.js...");
      const mammothModule = await import("mammoth");
      console.log("Mammoth module imported:", !!mammothModule);
      const mammoth = mammothModule.default;
      console.log("Mammoth default export:", !!mammoth);

      const arrayBuffer = await file.arrayBuffer();
      console.log(
        "DOCX file size:",
        file.size,
        "ArrayBuffer length:",
        arrayBuffer.byteLength
      );

      // Extract raw text
      const result = await mammoth.extractRawText({ arrayBuffer });
      const text = result.value.trim();

      if (!text || text.length < 10) {
        throw new Error("No readable text found in document");
      }

      return `📝 Word Document Content Extracted (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Parsing Method: mammoth.js (Client-side)
Text Length: ${text.length} characters

${text}

---
✅ Successfully extracted using mammoth.js
✅ Reliable DOCX text extraction
✅ Preserves document structure and content`;
    } catch (error) {
      console.error("DOCX parsing error:", error);
      return `❌ DOCX Parsing Failed (Bulk Import)

File: ${file.name}
Error: ${error instanceof Error ? error.message : "Unknown error"}

This Word document may have:
• Password protection
• Corrupted file structure
• Complex formatting that prevents text extraction
• Unsupported DOCX version

Recommended solutions:
• Try opening and re-saving the document in Word
• Convert to PDF or plain text format
• Check if the file is password protected`;
    }
  };

  const parsePowerPoint = async (file: File): Promise<string> => {
    try {
      // Use JSZip for manual extraction (browser-compatible)
      console.log("Attempting manual PPTX extraction with JSZip...");
      const JSZip = (await import("jszip")).default;

      const arrayBuffer = await file.arrayBuffer();
      console.log(
        "PPTX file size:",
        file.size,
        "ArrayBuffer length:",
        arrayBuffer.byteLength
      );

      const zip = await JSZip.loadAsync(arrayBuffer);

      let extractedText = "";
      let slideCount = 0;

      // Look for slide files in the PPTX structure
      const slideFiles = Object.keys(zip.files)
        .filter((filename) => filename.match(/ppt\/slides\/slide\d+\.xml/))
        .sort((a, b) => {
          const aNum = parseInt(a.match(/slide(\d+)\.xml/)?.[1] || "0");
          const bNum = parseInt(b.match(/slide(\d+)\.xml/)?.[1] || "0");
          return aNum - bNum;
        });

      for (const slideFile of slideFiles) {
        slideCount++;

        try {
          const slideXml = await zip.files[slideFile].async("text");

          // Enhanced text extraction from XML
          const textElements: string[] = [];

          // Extract text from <a:t> elements (main text content)
          const textMatches = slideXml.match(/<a:t[^>]*>(.*?)<\/a:t>/g) || [];
          textMatches.forEach((match) => {
            const text = match
              .replace(/<[^>]*>/g, "") // Remove XML tags
              .replace(/&lt;/g, "<") // Decode HTML entities
              .replace(/&gt;/g, ">")
              .replace(/&amp;/g, "&")
              .replace(/&quot;/g, '"')
              .replace(/&#(\d+);/g, (_match, dec) => String.fromCharCode(dec)) // Decode numeric entities
              .trim();

            if (text && text.length > 0 && !textElements.includes(text)) {
              textElements.push(text);
            }
          });

          if (textElements.length > 0) {
            const slideContent = textElements.join("\n").trim();
            extractedText += `## Slide ${slideCount}\n\n${slideContent}\n\n`;
          } else {
            // If no text found, note it
            extractedText += `## Slide ${slideCount}\n\n[No text content detected]\n\n`;
          }
        } catch (slideError) {
          console.warn(`Error processing slide ${slideCount}:`, slideError);
          extractedText += `## Slide ${slideCount}\n\n[Error processing slide: ${
            slideError instanceof Error ? slideError.message : "Unknown error"
          }]\n\n`;
        }
      }

      extractedText = extractedText.trim();

      if (!extractedText || extractedText.length < 20) {
        return `📊 PowerPoint File Processed (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Parsing Method: JSZip XML extraction (Client-side)
Slides found: ${slideCount}

⚠️ **No readable text found in this PowerPoint file**

**Possible reasons:**
- Slides contain only images, charts, or graphics
- Text is embedded in complex shapes or SmartArt
- The presentation uses advanced formatting that makes text extraction difficult
- This is an older .PPT file (only .PPTX is supported)

---
✅ PowerPoint structure analyzed successfully
⚠️ No extractable text found - try PDF conversion for better results`;
      }

      return `📊 PowerPoint Content Extracted Successfully (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Parsing Method: JSZip XML extraction (Client-side)
Slides processed: ${slideCount}
Text Length: ${extractedText.length} characters

${extractedText}

---
✅ Successfully extracted text using manual XML parsing
✅ Processed entirely in your browser
✅ Pure JavaScript solution - no external dependencies
💡 For better results with complex presentations, try converting to PDF first`;
    } catch (error) {
      console.error("PowerPoint parsing error:", error);
      return `❌ PowerPoint Parsing Failed (Bulk Import)

File: ${file.name}
Error: ${error instanceof Error ? error.message : "Unknown error"}

This PowerPoint file may have:
• Password protection or security restrictions
• Corrupted file structure
• Complex formatting that prevents text extraction
• Unsupported PPTX version (.ppt files are not supported)
• Images or graphics without extractable text
• Client-side library limitations

**Recommended alternatives:**
1. **Convert to PDF first** - Use PowerPoint's "Save as PDF" feature, then upload the PDF
2. **Export slides as images** - Save slides as PNG/JPG and use the OCR feature above
3. **Save as .txt** - Use PowerPoint's "Save As" → "Plain Text" option`;
    }
  };

  const parseImage = async (
    file: File
  ): Promise<{ text: string; images: string[] }> => {
    try {
      // Dynamic import to avoid SSR issues
      console.log("Attempting to import tesseract.js...");
      const tesseractModule = await import("tesseract.js");
      console.log("Tesseract module imported:", !!tesseractModule);
      const Tesseract = tesseractModule.default;
      console.log("Tesseract default export:", !!Tesseract);

      const fileUrl = URL.createObjectURL(file);
      console.log("Image file URL created:", !!fileUrl);

      const {
        data: { text },
      } = await Tesseract.recognize(fileUrl, "eng");

      // Clean up the object URL
      URL.revokeObjectURL(fileUrl);

      const cleanText = text.trim();

      if (!cleanText || cleanText.length < 5) {
        throw new Error("No readable text detected in image");
      }

      return {
        text: `🖼️ Image Text Extraction (OCR) - Bulk Import

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
OCR Engine: Tesseract.js (Client-side)
Detected Text Length: ${cleanText.length} characters

${cleanText}

---
✅ Text successfully extracted using Tesseract.js OCR
✅ Processed entirely in your browser
✅ Supports 100+ languages

Note: OCR accuracy depends on image quality, text clarity, and font size.`,
        images: [fileUrl],
      };
    } catch (error) {
      console.error("OCR error:", error);
      return {
        text: `❌ OCR Processing Failed (Bulk Import)

File: ${file.name}
Error: ${error instanceof Error ? error.message : "Unknown error"}

This image may have:
• Poor quality or resolution
• Complex backgrounds that interfere with text
• Handwritten text (which requires specialized OCR)
• Very small or unclear fonts
• Non-English text (try changing language settings)

Tips for better OCR results:
• Use high-resolution images (300 DPI or higher)
• Ensure good contrast between text and background
• Use images with clear, printed text
• Crop to focus on text areas only`,
        images: [],
      };
    }
  };

  const parseTextFile = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        const text = reader.result as string;
        resolve(`📄 Text File Content (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Character Count: ${text.length}

${text}

---
✅ Plain text file loaded successfully`);
      };
      reader.onerror = () => {
        resolve(`❌ Failed to read text file: ${file.name}`);
      };
      reader.readAsText(file);
    });
  };

  const getParsingMethod = (file: File): string => {
    if (file.type === "application/pdf") return "PDF.js";
    if (file.name.endsWith(".docx")) return "mammoth.js";
    if (file.name.endsWith(".pptx") || file.name.endsWith(".ppt"))
      return "JSZip XML extraction";
    if (file.type.startsWith("image/")) return "Tesseract.js OCR";
    if (file.type.startsWith("text/")) return "FileReader API";
    return "Unsupported";
  };
  const handleFiles = useCallback(
    async (files: FileList) => {
      setProcessing(true);
      setUploadStats({ total: files.length, processed: 0, errors: 0 });

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        try {
          await processFile(file);
          setUploadStats((prev) => ({
            ...prev,
            processed: prev.processed + 1,
          }));
        } catch (error) {
          console.error("Error processing file:", error);
          setUploadStats((prev) => ({ ...prev, errors: prev.errors + 1 }));
        }
      }
      setProcessing(false);
    },
    [processFile]
  );

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFiles(e.dataTransfer.files);
      }
    },
    [handleFiles]
  );

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200">
          Bulk Import
        </h2>{" "}
        <div className="text-sm text-slate-600 dark:text-slate-400">
          Intelligent parsing: PDF, DOCX, PPTX, Images (OCR), Text files
        </div>
      </div>

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : "border-slate-300 dark:border-slate-600 hover:border-slate-400 dark:hover:border-slate-500"
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.md,.jpg,.jpeg,.png,.gif,.bmp"
        />
        <div className="space-y-4">
          <div className="text-4xl">
            <Icon name="folder" className="w-16 h-16" />
          </div>
          <div>
            <p className="text-lg font-medium text-slate-700 dark:text-slate-300">
              Drop files here or click to browse
            </p>
            <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
              You can upload multiple files at once for bulk processing
            </p>
          </div>{" "}
        </div>
      </div>

      {/* Information about parsing capabilities */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
          ✨ Enhanced Bulk Import with Intelligent Parsing:
        </h4>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>
            📄 <strong>PDFs:</strong> PDF.js (Mozilla&apos;s official PDF
            parser)
          </li>
          <li>
            📝 <strong>Word docs:</strong> mammoth.js (reliable DOCX parsing)
          </li>
          <li>
            📊 <strong>PowerPoint:</strong> JSZip XML extraction (manual
            parsing)
          </li>
          <li>
            🖼️ <strong>Images:</strong> Tesseract.js OCR (100+ languages)
          </li>
          <li>
            🔒 <strong>All processing happens in your browser</strong> - no
            uploads to external servers
          </li>
        </ul>
      </div>

      {/* Processing Status */}
      {processing && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium text-blue-800 dark:text-blue-200">
              Processing Files...
            </span>
            <span className="text-sm text-blue-600 dark:text-blue-400">
              {uploadStats.processed}/{uploadStats.total}
            </span>
          </div>
          <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${(uploadStats.processed / uploadStats.total) * 100}%`,
              }}
            />
          </div>
        </div>
      )}

      {/* Upload Stats */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {uploadStats.processed}
          </div>
          <div className="text-sm text-green-700 dark:text-green-300">
            Processed
          </div>
        </div>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {uploadStats.errors}
          </div>
          <div className="text-sm text-red-700 dark:text-red-300">Errors</div>
        </div>
        <div className="bg-slate-50 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-slate-600 dark:text-slate-400">
            {processedFiles.length}
          </div>
          <div className="text-sm text-slate-700 dark:text-slate-300">
            Total Files
          </div>
        </div>
      </div>

      {/* Recent Files */}
      {processedFiles.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">
            Recently Processed Files
          </h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {processedFiles
              .slice(-5)
              .reverse()
              .map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      <Icon
                        name={
                          file.type.startsWith("image/")
                            ? "file-image"
                            : file.name.endsWith(".pdf")
                              ? "pdf"
                              : file.name.endsWith(".doc") ||
                                  file.name.endsWith(".docx")
                                ? "word"
                                : file.name.endsWith(".ppt") ||
                                    file.name.endsWith(".pptx")
                                  ? "powerpoint"
                                  : "file"
                        }
                        className="w-8 h-8"
                      />
                    </div>
                    <div>
                      <div className="font-medium text-slate-800 dark:text-slate-200">
                        {file.name}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        {(file.size / 1024).toFixed(1)} KB •{" "}
                        {String(file.metadata?.fileType || "Unknown")}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-slate-500 dark:text-slate-400">
                    {file.uploadedAt.toLocaleTimeString()}
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}
