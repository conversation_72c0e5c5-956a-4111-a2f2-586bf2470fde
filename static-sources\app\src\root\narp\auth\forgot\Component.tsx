import { NavLink, useNavigate } from "react-router-dom";
import { FormEvent, useState } from "react";
import { useAuth } from "@/util/auth/AuthContext.tsx";

export function Component() {
  const { requestPasswordReset } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const username = data.get("username")?.toString();

    if (username === undefined) return setError(new Error("Missing fields"));

    setLoading(true);
    const error = await requestPasswordReset(username);
    setLoading(false);

    if (error) return setError(error);
    navigate(`/narp/auth/reset/${username}`);
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5">
      <form
        onSubmit={handleSubmit}
        className="max-w-xs p-5 w-full flex flex-col gap-3"
      >
        <input readOnly hidden name={"redirect"} value={"true"} />

        <div className={"text-xl font-bold text-center my-3"}>
          Forgot Password
        </div>

        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Username</span>
            <input
              name={"username"}
              type={"text"}
              required
              autoComplete={"username"}
              placeholder={"Username"}
            />
          </label>
          <p className={"validator-hint hidden"}>Required</p>
        </fieldset>

        <button
          disabled={loading}
          type="submit"
          className={`btn ${error ? "btn-error" : "btn-primary"}`}
        >
          {loading ? <div className="loading" /> : <>Submit</>}
        </button>

        {error ? (
          <div className="text-error text-center">{error?.message}</div>
        ) : (
          <></>
        )}

        <div className="divider m-0">OR</div>

        <NavLink className="btn btn-secondary no-animation" to="../">
          Back To Sign In
        </NavLink>
      </form>
    </div>
  );
}
