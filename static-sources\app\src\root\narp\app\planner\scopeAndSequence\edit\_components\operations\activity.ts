import { BaseOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/base.ts";
import {
  Activity,
  ActivityType,
  EntityID,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export interface ActivityAddOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "ADD";
  data: {
    id: EntityID;
  };
}

export interface ActivityRemoveOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "REMOVE";
  data: {
    index: number;
    value: Activity;
  };
}

export interface ActivitySetTitleOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_TITLE";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface ActivitySetTypeOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_TYPE";
  data: {
    id: EntityID;
    previous: ActivityType;
  };
}

export interface ActivitySetDescriptionOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_DESCRIPTION";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface ActivitySetDurationOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_DURATION";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface ActivitySetResourcesOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_RESOURCES";
  data: {
    id: EntityID;
    previous: string[];
  };
}

export interface ActivitySetDeliverablesOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_DELIVERABLES";
  data: {
    id: EntityID;
    previous: string[];
  };
}

export interface ActivitySetParentOperation extends BaseOperation {
  namespace: "ACTIVITY";
  action: "SET_PARENT";
  data: {
    id: EntityID;
    previous: EntityID | null;
  };
}

export type ActivityOperation =
  | ActivityAddOperation
  | ActivityRemoveOperation
  | ActivitySetTitleOperation
  | ActivitySetTypeOperation
  | ActivitySetDescriptionOperation
  | ActivitySetDurationOperation
  | ActivitySetResourcesOperation
  | ActivitySetDeliverablesOperation
  | ActivitySetParentOperation;