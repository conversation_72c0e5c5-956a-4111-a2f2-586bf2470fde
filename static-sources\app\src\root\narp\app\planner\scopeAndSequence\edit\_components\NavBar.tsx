import { useNavigate, useParams, useLocation } from "react-router-dom";
import { FaSave, FaQuestionCircle } from "react-icons/fa";
import {
  FaListUl,
  FaChartGantt,
  FaGears,
  FaBookOpen,
  FaClipboardList,
} from "react-icons/fa6";
import { MouseEvent, useState } from "react";
import { SaveState, useEditorContext } from "./Context.tsx";

const tabs = [
  {
    icon: <FaBookOpen />,
    label: "Outline",
    path: "",
  },
  {
    icon: <FaListUl />,
    label: "Topics",
    path: "topics",
  },
  {
    icon: <FaGears />,
    label: "Goals",
    path: "goals",
  },
  {
    icon: <FaClipboardList />,
    label: "Terms",
    path: "terms",
  },
  {
    icon: <FaListUl />,
    label: "Activities",
    path: "activities",
  },
  {
    icon: <FaChartGantt />,
    label: "Chart",
    path: "chart",
  },
];

export default function NavBar() {
  const location = useLocation();
  const [showTutorial, setShowTutorial] = useState(false);

  return (
    <div className="flex flex-row justify-between">
      <div className="flex flex-row gap-1 overflow-x-auto">
        {tabs.map((tab, index) => (
          <Tab tab={tab} location={location} key={index} />
        ))}
      </div>

      <div className="flex items-center gap-3">
        <button
          onClick={() => setShowTutorial(true)}
          className="btn btn-sm btn-outline btn-info"
          title="Show Tutorial"
        >
          <FaQuestionCircle />
          Help
        </button>
        <SaveButton />
      </div>

      {/* Tutorial Modal */}
      {showTutorial && <TutorialModal onClose={() => setShowTutorial(false)} />}
    </div>
  );
}

function TutorialModal({ onClose }: { onClose: () => void }) {
  const [currentStep, setCurrentStep] = useState(0);

  const tutorialSteps = [
    {
      title: "Welcome to Scope & Sequence Planner!",
      content: (
        <div className="space-y-4">
          <p>
            This comprehensive tool helps you plan and organize your curriculum.
            Let's explore each section:
          </p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-800">
              💡 <strong>Tip:</strong> You can access this tutorial anytime by
              clicking the Help button in the navigation bar.
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "Outline Tab",
      content: (
        <div className="space-y-3">
          <p>
            <strong>Purpose:</strong> Define the basic information about your
            curriculum.
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Title:</strong> Enter your curriculum name (Press Enter to
              save)
            </li>
            <li>
              <strong>Abstract:</strong> Describe your curriculum overview
              (Ctrl+Enter to save)
            </li>
            <li>
              <strong>Grade Level:</strong> Select appropriate grade level
            </li>
            <li>
              <strong>Keywords:</strong> Add searchable keywords (Press Enter to
              add each one)
            </li>
          </ul>
          <div className="bg-green-50 p-3 rounded text-sm text-green-800">
            <strong>Auto-save:</strong> Changes save automatically after you
            confirm edits!
          </div>
        </div>
      ),
    },
    {
      title: "Topics Tab",
      content: (
        <div className="space-y-3">
          <p>
            <strong>Purpose:</strong> Create and organize your main learning
            topics.
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Add Topics:</strong> Create new learning units
            </li>
            <li>
              <strong>Essential Questions:</strong> Define key questions
              students should answer
            </li>
            <li>
              <strong>Notes & Resources:</strong> Add additional context and
              materials
            </li>
            <li>
              <strong>AI Generation:</strong> Use the magic wand to generate
              content automatically
            </li>
          </ul>
        </div>
      ),
    },
    {
      title: "Goals Tab",
      content: (
        <div className="space-y-3">
          <p>
            <strong>Purpose:</strong> Set specific learning objectives for each
            topic.
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Learning Goals:</strong> Define what students should
              achieve
            </li>
            <li>
              <strong>Priority Levels:</strong> Set Must Have, Should Have, or
              Could Have
            </li>
            <li>
              <strong>Goal Types:</strong> Categorize different types of
              learning objectives
            </li>
            <li>
              <strong>Parent Topics:</strong> Link goals to specific topics
            </li>
          </ul>
        </div>
      ),
    },
    {
      title: "Terms Tab",
      content: (
        <div className="space-y-3">
          <p>
            <strong>Purpose:</strong> Build your curriculum vocabulary.
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Vocabulary Words:</strong> Add key terms students need to
              learn
            </li>
            <li>
              <strong>Definitions:</strong> Provide clear explanations
            </li>
            <li>
              <strong>Categories:</strong> Organize terms by type (noun, verb,
              etc.)
            </li>
            <li>
              <strong>Topic Assignment:</strong> Link terms to relevant topics
            </li>
          </ul>
        </div>
      ),
    },
    {
      title: "Activities Tab",
      content: (
        <div className="space-y-3">
          <p>
            <strong>Purpose:</strong> Plan hands-on learning activities.
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Activity Design:</strong> Create engaging learning
              experiences
            </li>
            <li>
              <strong>Types & Duration:</strong> Set activity categories and
              time requirements
            </li>
            <li>
              <strong>Resources:</strong> List materials needed
            </li>
            <li>
              <strong>Deliverables:</strong> Define what students will produce
            </li>
          </ul>
        </div>
      ),
    },
    {
      title: "Chart Tab",
      content: (
        <div className="space-y-3">
          <p>
            <strong>Purpose:</strong> Visualize your curriculum timeline and
            dependencies.
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Timeline View:</strong> See topics arranged on a calendar
            </li>
            <li>
              <strong>Drag to Move:</strong> Click and drag entire boxes to
              reschedule
            </li>
            <li>
              <strong>Resize Duration:</strong> Drag the right edge to change
              topic length
            </li>
            <li>
              <strong>Dependencies:</strong> Drag blue + buttons between topics
              to create prerequisites
            </li>
            <li>
              <strong>Double-click to Rename:</strong> Edit topic names directly
              on the chart
            </li>
          </ul>
          <div className="bg-purple-50 p-3 rounded text-sm text-purple-800">
            <strong>Pro Tip:</strong> The chart view is perfect for visualizing
            how your curriculum flows over time!
          </div>
        </div>
      ),
    },
    {
      title: "You're Ready to Go!",
      content: (
        <div className="space-y-4">
          <p>You now have everything you need to create amazing curricula!</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">
                Best Practices:
              </h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Start with the Outline tab</li>
                <li>• Add Topics before Goals</li>
                <li>• Use Chart view for timeline planning</li>
                <li>• Save frequently (though auto-save helps!)</li>
              </ul>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Need Help?</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Click Help button anytime</li>
                <li>• Hover tooltips show guidance</li>
                <li>• Auto-save prevents data loss</li>
                <li>• AI tools speed up creation</li>
              </ul>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const currentTutorial = tutorialSteps[currentStep];

  return (
    <div className="fixed inset-0 bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-300">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-800">
              {currentTutorial.title}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ×
            </button>
          </div>

          <div className="mb-6">{currentTutorial.content}</div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Step {currentStep + 1} of {tutorialSteps.length}
            </div>

            <div className="flex gap-3">
              {currentStep > 0 && (
                <button
                  onClick={() => setCurrentStep(currentStep - 1)}
                  className="btn btn-sm btn-outline"
                >
                  Previous
                </button>
              )}

              {currentStep < tutorialSteps.length - 1 ? (
                <button
                  onClick={() => setCurrentStep(currentStep + 1)}
                  className="btn btn-sm btn-primary"
                >
                  Next
                </button>
              ) : (
                <button onClick={onClose} className="btn btn-sm btn-success">
                  Get Started!
                </button>
              )}
            </div>
          </div>

          {/* Progress bar */}
          <div className="mt-4 bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${((currentStep + 1) / tutorialSteps.length) * 100}%`,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

interface TabProps {
  tab: {
    icon: JSX.Element;
    label: string;
    path?: string;
  };
  location: {
    pathname: string;
  };
}

function Tab({ tab, location }: TabProps) {
  const { id } = useParams();
  const navigate = useNavigate();
  const baseUrl = `/narp/app/planner/scopeAndSequence/edit/${id}`;
  const tabUrl = tab.path ? `${baseUrl}/${tab.path}` : baseUrl;

  const isActive =
    location.pathname === tabUrl ||
    (tab.path === "" && location.pathname === baseUrl);

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    navigate(tabUrl);
  }

  return (
    <button
      tabIndex={0}
      onClick={handleClick}
      type="button"
      className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-colors ${
        isActive
          ? "bg-primary-500 text-white"
          : "text-neutral-600 hover:bg-gray-100"
      }`}
    >
      {tab.icon}
      {tab.label}
    </button>
  );
}

function SaveButton() {
  const {
    cloud: { save, state },
  } = useEditorContext();

  function handleClick(e: MouseEvent) {
    if (state === SaveState.SAVING) return;
    e.preventDefault();
    save();
  }

  return (
    <div>
      <button
        tabIndex={0}
        type="button"
        onClick={handleClick}
        className={`shrink-0 btn btn-sm btn-square btn-primary ${state === SaveState.SAVED && "btn-outline"}`}
      >
        {state === SaveState.SAVING ? (
          <div className="loading loading-spinner loading-sm" />
        ) : (
          <FaSave className="shrink-0" />
        )}
      </button>
    </div>
  );
}
