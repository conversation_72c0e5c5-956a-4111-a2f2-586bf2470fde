import { RouteObject } from "react-router-dom";
import { default as IndexRoute } from "./_index/Route";
import { default as ChartRoute } from "./chart/Route";
import { default as TermsRoute } from "./terms/Route";
import { default as GoalsRoute } from "./goals/Route";
import { default as TopicsRoute } from "./topics/Route";
import { default as ActivitiesRoute } from "./activities/Route";
// import { default as ActivitiesRoute } from "./chart/Route";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";

const Route: RouteObject = {
  path: "edit/:id",
  children: [
    IndexRoute,
    TermsRoute,
    GoalsRoute,
    TopicsRoute,
    ChartRoute,
    ActivitiesRoute,
    // ActivitiesRoute,
  ],
  hydrateFallbackElement: <PageSkeleton />,
  lazy: () => import("./Component"),
};

export default Route;
