import {
  Activity,
  ActivityType,
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaDice, FaPlus, FaTrash, FaWandMagicSparkles } from "react-icons/fa6";
import { Dispatch, MouseEvent, SetStateAction, useState } from "react";
import { FaClock, FaEdit, FaFile, FaLink } from "react-icons/fa";
import { getActivityTypeColor } from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/getActivityTypeColor.ts";
import titleize from "titleize";
import usePOST from "@/util/api/usePOST.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";
import GeneratingActivitiesOverlay from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/GeneratingActivitiesOverlay.tsx";

export default function GridView({
  entries,
  setEditID,
}: {
  entries: Activity[];
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    activity: { add },
    outline: { gradeLevel, keywords, title },
    goal: { entries: goals },
    term: { entries: terms },
  } = useEditorContext();
  const topRealm = useTopRealm();
  const POST = usePOST();
  const pushError = usePushError();

  const [generating, setGenerating] = useState<boolean>(false);

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    add();
  }

  async function handleGenerate(e: MouseEvent) {
    e.preventDefault();
    if (generating) return;

    const payload = {
      realmEID: topRealm.externalID,
      subjects: {
        topics: [
          ...goals.map((entry) => entry.description),
          ...terms.map((entry) => entry.word),
          ...keywords,
        ],
        target: {
          age: gradeLevel + 5,
          domain: title,
        },
      },
    };

    setGenerating(true);

    try {
      const { response, error, data } = await POST(
        "/v1/ai/curricula/activities/generate",
        {
          assertedRealmEidUrn: topRealm.eidURN,
          body: JSON.stringify(payload),
        }
      );

      if (error) {
        pushError(error.clientErrorDetail.userMessage);
        return;
      }

      if (!response.ok) {
        pushError(await response.text());
        return;
      }

      if (!data) {
        pushError("No data received from AI service");
        return;
      }

      // Parse response according to the API format used in ActivitiesSection
      const envelope: APIEnvelope<{
        concepts: [{ en_US: string; es_ES: string; fr_FR: string }];
      }> = JSON.parse(await data.text());

      const ids = entries.map((entry) => entry.id);

      // Create activities from the generated concepts
      for (const concept of envelope.data.concepts) {
        const newID = getNonCollidingID(ids);
        const tokens = concept.en_US.split(": ");

        add({
          id: newID,
          parentTopic: null, // Not associated with any specific topic in the activities view
          title: tokens[0] || concept.en_US,
          type: ActivityType.CREATION,
          description: tokens[1] || "AI-generated activity",
          duration: "Not Set",
          resources: [],
          deliverables: [],
        });

        ids.push(newID);
      }
    } catch (err) {
      pushError(
        "Failed to generate activities: " +
          (err instanceof Error ? err.message : String(err))
      );
    } finally {
      setGenerating(false);
    }
  }

  function handleCancelGeneration() {
    setGenerating(false);
  }

  return (
    <>
      <GeneratingActivitiesOverlay
        isVisible={generating}
        onCancel={handleCancelGeneration}
      />

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 px-5 shrink-0 gap-4">
        {entries.map((activity, index) => (
          <Entry activity={activity} key={index} setEditID={setEditID} />
        ))}

        {/* Create New Activity Button */}
        <button
          onClick={handleClick}
          type="button"
          disabled={generating}
          className="min-h-48 size-full btn btn-lg btn-primary btn-outline rounded-lg flex flex-col gap-3 items-center justify-center"
        >
          <FaPlus className="shrink-0 text-2xl" />
          <div className="flex flex-col items-center">
            <span className="font-semibold">Create New Activity</span>
            <span className="text-sm opacity-75">
              Design a custom learning activity
            </span>
          </div>
        </button>

        {/* AI Generate Activities Button */}
        <button
          onClick={handleGenerate}
          type="button"
          disabled={generating}
          className="min-h-48 size-full btn btn-lg btn-secondary btn-outline rounded-lg flex flex-col gap-3 items-center justify-center"
        >
          {generating ? (
            <div className="loading loading-spinner loading-lg" />
          ) : (
            <FaWandMagicSparkles className="shrink-0 text-2xl" />
          )}
          <div className="flex flex-col items-center">
            <span className="font-semibold">
              {generating ? "Generating..." : "AI Generate Activities"}
            </span>
            <span className="text-sm opacity-75">
              {generating
                ? "Creating activities for your curriculum..."
                : "Let AI create activities based on your goals"}
            </span>
          </div>
        </button>
      </div>
    </>
  );
}

function Entry({
  activity,
  setEditID,
}: {
  activity: Activity;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    topic: { get },
    activity: { remove },
  } = useEditorContext();

  const [removeTimeout, setRemoveTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  function handleRemoveClick(e: MouseEvent) {
    e.preventDefault();
    if (removeTimeout !== null) {
      remove(activity.id);
      clearTimeout(removeTimeout);
      setRemoveTimeout(null);
    } else {
      setRemoveTimeout(
        setTimeout(() => {
          setRemoveTimeout(null);
        }, 1000)
      );
    }
  }

  function handleEditClick(e: MouseEvent) {
    e.preventDefault();
    setEditID(activity.id);
  }

  const parentTopic =
    activity.parentTopic !== null ? get(activity.parentTopic) : null;

  return (
    <div className="min-h-48 p-4 border border-neutral-300 rounded-lg flex flex-col gap-3 hover:shadow-md transition-shadow">
      <div className="flex flex-row items-start gap-3">
        <FaDice className="size-6 shrink-0 text-neutral-600" />

        <div className="flex flex-col grow gap-2">
          <div className="flex flex-row items-start justify-between">
            <div className="flex flex-col">
              <span className="text-lg font-bold leading-tight">
                {activity.title}
              </span>

              <span className="text-sm text-neutral-500">
                ({parentTopic?.name || "No Parent"})
              </span>
            </div>

            <div className="flex flex-row gap-1 items-start">
              <div className="tooltip" data-tip="Edit">
                <button
                  type="button"
                  onClick={handleEditClick}
                  className="btn btn-square btn-xs text-sm hover:btn-primary focus-visible:btn-primary hover:text-white focus-visible:text-white"
                >
                  <FaEdit />
                </button>
              </div>

              <div
                className="tooltip"
                data-tip={removeTimeout ? "Click Again To Confirm" : "Remove"}
              >
                <button
                  type="button"
                  onClick={handleRemoveClick}
                  className="btn btn-square btn-xs text-sm hover:btn-error focus-visible:btn-error hover:text-white focus-visible:text-white"
                >
                  <FaTrash />
                </button>
              </div>
            </div>
          </div>

          <div
            style={{ backgroundColor: getActivityTypeColor(activity.type) }}
            className="inline-block self-start rounded-full px-2 py-1 text-xs font-medium text-white"
          >
            {titleize(activity.type)}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2 text-sm text-neutral-600">
        <FaClock className="size-3" />
        <span>{activity.duration}</span>
      </div>

      <div className="text-sm text-neutral-700 line-clamp-3 grow">
        {activity.description}
      </div>

      {activity.resources.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2 text-xs text-neutral-600">
            <FaFile className="size-3" />
            <span>Resources</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {activity.resources.slice(0, 3).map((resource, idx) => (
              <div
                key={idx}
                className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs max-w-full truncate"
              >
                {resource}
              </div>
            ))}
            {activity.resources.length > 3 && (
              <div className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                +{activity.resources.length - 3} more
              </div>
            )}
          </div>
        </div>
      )}

      {activity.deliverables.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2 text-xs text-neutral-600">
            <FaLink className="size-3" />
            <span>Deliverables</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {activity.deliverables.slice(0, 2).map((deliverable, idx) => (
              <div
                key={idx}
                className="bg-green-50 text-green-700 px-2 py-1 rounded text-xs max-w-full truncate"
              >
                {deliverable}
              </div>
            ))}
            {activity.deliverables.length > 2 && (
              <div className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                +{activity.deliverables.length - 2} more
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
