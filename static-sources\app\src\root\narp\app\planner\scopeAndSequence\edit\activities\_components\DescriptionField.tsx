import {
  Activity,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { ChangeEvent } from "react";

export default function DescriptionField({ activity }: { activity: Activity }) {
  const {
    activity: { setDescription },
  } = useEditorContext();

  function handleChange(e: ChangeEvent<HTMLTextAreaElement>) {
    setDescription(activity.id, e.target.value);
  }

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">Description</label>
      <textarea
        value={activity.description}
        onChange={handleChange}
        className="textarea textarea-bordered w-full min-h-20"
        placeholder="Describe the activity..."
      />
    </div>
  );
}
