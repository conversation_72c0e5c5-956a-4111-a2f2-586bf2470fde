import {
  Activity,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { ChangeEvent } from "react";

export default function DurationField({ activity }: { activity: Activity }) {
  const {
    activity: { setDuration },
  } = useEditorContext();

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    setDuration(activity.id, e.target.value);
  }

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">Duration</label>
      <input
        type="text"
        value={activity.duration}
        onChange={handleChange}
        className="input input-bordered w-full"
        placeholder="e.g., 30-45 min"
      />
    </div>
  );
}
