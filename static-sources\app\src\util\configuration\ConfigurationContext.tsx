import { createContext, useContext } from "react";
import { CookieStorage } from "amazon-cognito-identity-js";

export interface Configuration {
  region: string;
  userPoolClientAutomationAppID: string;
  userPoolClientWebAppID: string;
  userPoolID: string;
  cookieStorage: CookieStorage;
  apiBaseURL: string;
}

export const ConfigurationContext = createContext<Configuration>(
  {} as Configuration,
);

export function useConfiguration() {
  return useContext(ConfigurationContext);
}
