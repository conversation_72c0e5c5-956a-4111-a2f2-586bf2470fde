import { Navigate, RouteObject } from "react-router-dom";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";

import { default as PlannerRoute } from "./planner/Route";
import { default as AdminRoute } from "./admin/Route";
import { default as AssetManagerRoute } from "./asset-manager/Route";
import { default as LORRoute } from "./lor/Route";

const RealmRoute: RouteObject = {
  path: "app",
  hydrateFallbackElement: <PageSkeleton />,
  lazy: () => import("./Component.tsx"),
  children: [
    { index: true, element: <Navigate to={"./planner"} /> },
    AssetManagerRoute,
    PlannerRoute,
    AdminRoute,
    LORRoute,
  ],
};

const Route: RouteObject = {
  path: "",
  children: [RealmRoute, { index: true, element: <></> }],
  hydrateFallbackElement: <PageSkeleton />,
  lazy: () => import("../Component.tsx"),
};

export default Route;
