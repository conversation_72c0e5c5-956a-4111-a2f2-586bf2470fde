/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          '050': '#f6f3fe',
          '50': '#f6f3fe',
          '100': '#d7c9fa',
          '200': '#b8a1f4',
          '300': '#9a79ee',
          '400': '#7d52e7',
          '500': '#602dde',
          '600': '#4f20bd',
          '700': '#401b94',
          '800': '#31156c',
          '900': '#200f45',
        },
        secondary: {
          '050': '#fefcf3',
          '100': '#faf1c9',
          '200': '#f4e4a1',
          '300': '#eed779',
          '400': '#e7c852',
          '500': '#deb92d',
          '600': '#bd9e20',
          '700': '#947d1b',
          '800': '#6c5c15',
          '900': '#453c0f',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('daisyui'),
    require('tailwindcss-animate'),
  ],
  daisyui: {
    themes: [
      {
        light: {
          "color-scheme": "light",
          "base-100": "oklch(100% 0 0)",
          "base-200": "oklch(98% 0 0)",
          "base-300": "oklch(95% 0 0)",
          "base-content": "oklch(21% 0.006 285.885)",
          "primary": "#4f20bd",
          "primary-content": "oklch(98% 0.003 247.858)",
          "secondary": "#eed779",
          "secondary-content": "#121214",
          "accent": "oklch(77% 0.152 181.912)",
          "accent-content": "oklch(38% 0.063 188.416)",
          "neutral": "oklch(14% 0.005 285.823)",
          "neutral-content": "oklch(92% 0.004 286.32)",
          "info": "oklch(74% 0.16 232.661)",
          "info-content": "oklch(29% 0.066 243.157)",
          "success": "oklch(76% 0.177 163.223)",
          "success-content": "oklch(37% 0.077 168.94)",
          "warning": "#fbebea",
          "warning-content": "#7f2a27",
          "error": "oklch(71% 0.194 13.428)",
          "error-content": "oklch(27% 0.105 12.094)",
        },
      },
    ],
  },
} 