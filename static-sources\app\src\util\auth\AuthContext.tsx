import { createContext, useContext } from "react";
import {
  CognitoUserPool,
  CognitoUserSession,
} from "amazon-cognito-identity-js";

export enum SignInStatus {
  Success = "SUCCESS",
  Error = "ERROR",
  NewPassword = "NEW_PASSWORD",
  ConfirmRequired = "CONFIRM_REQUIRED",
  MfaSms = "MFA_SMS",
  MfaTotp = "MFA_TOTP",
  MfaTotpSetup = "MFA_TOTP_SETUP",
  MfaSelect = "MFA_SELECT",
}

export enum SignUpStatus {
  Success = "SUCCESS",
  Error = "ERROR",
  ConfirmRequired = "CONFIRM_REQUIRED",
}

export enum MFAStatus {
  Success = "SUCCESS",
  Error = "ERROR",
  ConfirmRequired = "CONFIRM_REQUIRED",
}

export interface AuthContext {
  userPool: CognitoUserPool;

  isAuthenticated: () => Promise<boolean>;
  signIn: (
    username: string,
    password: string,
  ) => Promise<[SignInStatus, Error | null]>;
  signUp: (
    username: string,
    password: string,
    email: string,
    phone?: string,
  ) => Promise<[SignUpStatus, Error | null]>;
  verifyAccount: (code: string, username?: string) => Promise<Error | null>;
  associateSoftware: () => Promise<[string, null] | [null, Error]>;
  verifySoftware: (code: string) => Promise<Error | null>;
  mfa: (
    type: "TOTP" | "SMS",
    code: string,
  ) => Promise<[MFAStatus, Error | null]>;
  mfaTOTP: (code: string) => Promise<[MFAStatus, Error | null]>;
  mfaSMS: (code: string) => Promise<[MFAStatus, Error | null]>;
  requestPasswordReset: (username?: string) => Promise<Error | null>;
  confirmPasswordReset: (
    code: string,
    password: string,
    username?: string,
  ) => Promise<Error | null>;
  signOut: () => Promise<void>;
  getSession: () => Promise<[CognitoUserSession, null] | [null, Error]>;
  getUsername: () => string | null;
}

export const AuthContext = createContext<AuthContext>({} as AuthContext);

export function useAuth() {
  return useContext(AuthContext);
}
