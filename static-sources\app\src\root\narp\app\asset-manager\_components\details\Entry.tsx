import { MouseEvent, useState } from "react";
import { FaCheck, FaCopy } from "react-icons/fa6";

export default function Entry({
  label,
  value,
  copyable = false,
}: {
  label: string;
  value: string;
  copyable?: boolean;
}) {
  const [copiedTimeout, setCopiedTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );

  async function handleClick(e: MouseEvent) {
    e.preventDefault();

    await navigator.clipboard.writeText(value);

    if (copiedTimeout !== null) clearTimeout(copiedTimeout);
    setCopiedTimeout(
      setTimeout(() => {
        setCopiedTimeout(null);
      }, 1500),
    );
  }

  return (
    <div className="flex flex-row items-center">
      <span className="italic me-3">{label}:</span>
      <span className="me-1">{value}</span>
      {copyable && (
        <button
          onClick={handleClick}
          className="btn btn-xs btn-square btn-ghost"
        >
          {copiedTimeout !== null ? <FaCheck /> : <FaCopy />}
        </button>
      )}
    </div>
  );
}
