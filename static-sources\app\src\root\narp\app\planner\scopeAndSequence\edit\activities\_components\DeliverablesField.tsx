import {
  Activity,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { useState } from "react";
import { FaPlus, FaTrash } from "react-icons/fa6";

export default function DeliverablesField({
  activity,
}: {
  activity: Activity;
}) {
  const {
    activity: { setDeliverables },
  } = useEditorContext();

  const [newDeliverable, setNewDeliverable] = useState("");

  function handleAddDeliverable() {
    if (newDeliverable.trim()) {
      setDeliverables(activity.id, [
        ...activity.deliverables,
        newDeliverable.trim(),
      ]);
      setNewDeliverable("");
    }
  }

  function handleRemoveDeliverable(index: number) {
    const updated = activity.deliverables.filter((_, i) => i !== index);
    setDeliverables(activity.id, updated);
  }

  function handleKeyPress(e: React.KeyboardEvent) {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddDeliverable();
    }
  }

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">Deliverables</label>

      <div className="flex gap-2">
        <input
          type="text"
          value={newDeliverable}
          onChange={(e) => setNewDeliverable(e.target.value)}
          onKeyPress={handleKeyPress}
          className="input input-bordered flex-1"
          placeholder="Add a deliverable..."
        />
        <button
          type="button"
          onClick={handleAddDeliverable}
          disabled={!newDeliverable.trim()}
          className="btn btn-square btn-primary"
        >
          <FaPlus />
        </button>
      </div>

      {activity.deliverables.length > 0 && (
        <div className="flex flex-col gap-2 max-h-32 overflow-y-auto">
          {activity.deliverables.map((deliverable, index) => (
            <div
              key={index}
              className="flex items-center gap-2 p-2 bg-gray-50 rounded"
            >
              <span className="flex-1 text-sm">{deliverable}</span>
              <button
                type="button"
                onClick={() => handleRemoveDeliverable(index)}
                className="btn btn-square btn-xs btn-error btn-outline"
              >
                <FaTrash />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
