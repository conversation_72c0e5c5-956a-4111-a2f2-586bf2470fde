import {
  Goal,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaEdit } from "react-icons/fa";
import { FocusEvent, FormEvent, MouseEvent, useRef, useState } from "react";

export default function DescriptionField({ goal }: { goal: Goal }) {
  const {
    goal: { setDescription },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState<string>(goal.description);

  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  function handleInput(e: FormEvent<HTMLTextAreaElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setDescription(goal.id, displayValue);
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget === buttonRef.current ||
      e.relatedTarget === inputRef.current
    )
      return;
    setDisplayValue(goal.description);
  }

  return (
    <div className="flex flex-col">
      <div className="font-bold">Description</div>
      <div className="flex flex-row gap-3">
        <textarea
          onBlur={handleBlur}
          onInput={handleInput}
          tabIndex={0}
          ref={inputRef}
          value={displayValue}
          autoComplete="off"
          className="textarea textarea-lg grow"
        />
        <div className="tooltip" data-tip="Confirm Edit">
          <button
            onBlur={handleBlur}
            onClick={handleClick}
            disabled={displayValue === goal.description}
            ref={buttonRef}
            tabIndex={0}
            type="button"
            className="btn btn-lg btn-square btn-primary"
          >
            <FaEdit />
          </button>
        </div>
      </div>
    </div>
  );
}
