import {
  Alert,
  AlertType,
  Context,
} from '@/util/components/Alerts/Context.tsx';
import { ReactNode, useState } from 'react';
import ErrorAlert from '@/util/components/Alerts/types/ErrorAlert.tsx';
import SuccessAlert from '@/util/components/Alerts/types/SuccessAlert.tsx';
import InfoAlert from '@/util/components/Alerts/types/InfoAlert.tsx';
import WarningAlert from '@/util/components/Alerts/types/WarningAlert.tsx';
import NeutralAlert from '@/util/components/Alerts/types/NeutralAlert.tsx';

export default function AlertsProvider({ children }: { children: ReactNode }) {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const activeAlert = alerts.at(0) || null;
  const alertCount = alerts.length;

  function popAlert(): Alert | null {
    const newAlerts = alerts.slice();
    const poppedAlert = newAlerts.shift();
    setAlerts(newAlerts);
    return poppedAlert || null;
  }

  function pushAlert(
    message: string,
    type: AlertType = AlertType.ERROR,
    description: string | undefined = undefined,
    callToActionLink: string | undefined = undefined,
    callToActionText: string | undefined = undefined,
    variantColor: boolean = false
  ) {
    const newAlerts = alerts.slice();
    newAlerts.push({
      type: type,
      message: message,
      description: description,
      callToActionText: callToActionText,
      callToActionLink: callToActionLink,
      variantColor: variantColor,
    });
    setAlerts(newAlerts);
  }

  function getAlertType(alert: Alert | null): ReactNode {
    switch (alert?.type) {
      case AlertType.ERROR:
        return <ErrorAlert />;
      case AlertType.INFO:
        return <InfoAlert />;
      case AlertType.SUCCESS:
        return <SuccessAlert />;
      case AlertType.WARNING:
        return <WarningAlert />;
      case AlertType.NEUTRAL:
        return <NeutralAlert />;
      default:
        return <></>;
    }
  }

  return (
    <Context.Provider
      value={{
        alerts: alerts,
        activeAlert: activeAlert,
        popAlert: popAlert,
        pushAlert: pushAlert,
      }}
    >
      <div
        className={`z-50 fixed w-screen top-0 left-0 p-5 transition-opacity ${activeAlert ? 'opacity-100 pointer-events-auto items-center' : 'opacity-0 pointer-events-none'}`}
      >
        <div className={'indicator w-full'}>
          {alertCount > 1 && (
            <span className={'indicator-item badge badge-neutral'}>
              {alertCount}
            </span>
          )}
          {getAlertType(activeAlert)}
        </div>
      </div>

      {children}
    </Context.Provider>
  );
}
