import { Navigate, Outlet, RouteObject } from "react-router-dom";
import { default as NarpRoute } from "./narp/Route";
import NotFound from "@/root/NotFound.tsx";

const Route: RouteObject = {
  path: "",
  element: (
    <div className={"fixed bg-base-100 top-0 left-0 w-screen h-screen"}>
      <Outlet />
    </div>
  ),
  children: [
    NarpRoute,
    { path: "/", element: <Navigate to={"/narp"} /> },
    { path: "/*", element: <NotFound /> },
  ],
};

export default Route;
