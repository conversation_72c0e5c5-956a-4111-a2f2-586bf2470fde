import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import Entry from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Entry.tsx";
import { DependencyArrows } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/DependencyArrow.tsx";
import Dividers from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Dividers.tsx";
import Labels from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Labels.tsx";

export default function Chart() {
  const {
    topic: { entries },
  } = useEditorContext();

  return (
    <div className="grow overflow-x-auto overflow-y-auto mx-5">
      <div
        id="topic-container"
        className="relative flex flex-col size-fit border-t-2 border-neutral-200"
      >
        <Labels />
        <div className="relative flex flex-col w-full border-l-2 border-l-neutral-200 border-t-2 border-t-neutral-200">
          {entries.map((entry, index) => (
            <div key={index}>
              <Entry topic={entry} />
              <div className="-z-10 border-b-2 border-b-neutral-200" />
            </div>
          ))}
          <DependencyArrows />
          <Dividers />
        </div>
      </div>
    </div>
  );
}
