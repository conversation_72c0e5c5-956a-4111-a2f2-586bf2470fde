import { FocusEvent, FormEvent, useRef, useState } from "react";
import {
  Goal,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaEdit } from "react-icons/fa";

export default function TypeField({ goal }: { goal: Goal }) {
  const {
    goal: { setType },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState<string | null>(goal.type);

  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    if (!goal) return;
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick() {
    if (!goal || displayValue === goal.type) return;
    if (displayValue === "") return setType(goal.id, null);
    setType(goal.id, displayValue);
  }

  function handleBlur(e: FocusEvent) {
    if (
      !goal ||
      e.relatedTarget == buttonRef.current ||
      e.relatedTarget == inputRef.current
    )
      return;
    setDisplayValue(goal.type);
  }

  return (
    <div className="flex flex-col">
      <div className="font-bold">Type</div>

      <div className="flex flex-row gap-3">
        <input
          tabIndex={0}
          ref={inputRef}
          onBlur={handleBlur}
          onInput={handleInput}
          value={displayValue || ""}
          autoComplete="off"
          className="input input-lg grow"
        />
        <div className="tooltip" data-tip="Confirm Edit">
          <button
            tabIndex={0}
            ref={buttonRef}
            onBlur={handleBlur}
            onClick={handleClick}
            disabled={goal.type === displayValue}
            className={`btn btn-lg btn-square btn-primary`}
          >
            <FaEdit />
          </button>
        </div>
      </div>
    </div>
  );
}
