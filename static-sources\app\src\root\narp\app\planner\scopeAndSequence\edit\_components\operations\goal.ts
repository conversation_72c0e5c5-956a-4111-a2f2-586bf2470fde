import { BaseOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/base.ts";
import {
  EntityID,
  Goal,
  GoalPriority,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export interface GoalAddOperation extends BaseOperation {
  namespace: "GOAL";
  action: "ADD";
  data: {
    id: EntityID;
  };
}

export interface GoalRemoveOperation extends BaseOperation {
  namespace: "GOAL";
  action: "REMOVE";
  data: {
    index: number;
    value: Goal;
  };
}

export interface GoalSetTypeOperation extends BaseOperation {
  namespace: "GOAL";
  action: "SET_TYPE";
  data: {
    id: EntityID;
    previous: string | null;
  };
}

export interface GoalSetDescriptionOperation extends BaseOperation {
  namespace: "GOAL";
  action: "SET_DESCRIPTION";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface GoalSetPriorityOperation extends BaseOperation {
  namespace: "GOAL";
  action: "SET_PRIORITY";
  data: {
    id: EntityID;
    previous: GoalPriority;
  };
}

export interface GoalSetParentOperation extends BaseOperation {
  namespace: "GOAL";
  action: "SET_PARENT";
  data: {
    id: EntityID;
    previous: EntityID | null;
  };
}

export type GoalOperation =
  | GoalAddOperation
  | GoalRemoveOperation
  | GoalSetTypeOperation
  | GoalSetDescriptionOperation
  | GoalSetPriorityOperation
  | GoalSetParentOperation;
