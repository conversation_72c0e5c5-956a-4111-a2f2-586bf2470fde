import { MouseEvent, ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import { FaChevronRight } from "react-icons/fa6";

export default function Crumb({
  label,
  to,
  icon = <></>,
  active = true,
  base = false,
}: {
  label: string;
  to: string;
  icon?: ReactNode;
  active?: boolean;
  base?: boolean;
}) {
  const navigate = useNavigate();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    if (active) navigate(to);
  }

  return (
    <>
      <a
        className={`flex flex-row gap-1 items-center justify-center size-fit ${active && "link link-info"}`}
        onClick={handleClick}
      >
        {icon} <span>{label}</span>
      </a>
      {!base && <FaChevronRight className="size-3" />}
    </>
  );
}
