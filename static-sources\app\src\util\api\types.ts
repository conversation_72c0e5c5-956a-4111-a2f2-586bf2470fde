export interface APIError {
  request: {
    requestID: string;
    uriPath: string;
    httpMethod: "GET" | "POST" | "PUT" | "DELETE";
  };
  timestamp: string;
  clientErrorDetail: {
    username: string;
    sub: string;
    functionName: string;
    executionRegion: string;
    errorType: {
      code: number;
      name: string;
    };
    errorPointID: string;
    subsystem: string;
    userMessage: string;
    embeddedFrames: {
      errorType?: {
        code: number;
        name: string;
      };
      errorPointID: string;
      subsystem?: string;
      userMessage: string;
    }[];
  };
}

export interface APIEnvelope<T> {
  envelope: {
    gatewayRequestID: string;
    lambdaRequestID: string;
    limits: {
      requestsRemaining: number;
      rateLimit: {
        limit: number;
        windowSeconds: number;
      };
    };
    paging: {
      firstPage: number;
      lastPage: number;
      currentPage: number;
    };
    version: {
      buildTimestamp: string;
    };
    asynchronousData?: {
      targetURL: string;
      intervalInSecondsUntilFirstRequest: number;
      intervalInSecondsBetweenSubsequentRequests: number;
      status: "enqueued" | string;
    };
  };
  data: T;
}

export interface APIResult {
  response: Response;
  error: APIError | null;
  data: Blob | null;
}
