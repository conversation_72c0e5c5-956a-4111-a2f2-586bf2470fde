import { Outlet, useParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { AlertType, usePushAlert } from "@/util/components/Alerts/Context.tsx";
import { Provider } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Provider.tsx";
import {
  fromVer0,
  fromVer1,
  SerializedDocument,
} from "@/root/narp/app/planner/_util/plannerDocuments/ver2.ts";
import { GradeLevel } from "@/util/standards.ts";
import useGET from "@/util/api/useGET.tsx";
import { ScopeAndSequenceDocument as Version0 } from "@/root/narp/app/planner/_util/plannerDocuments/version0.ts";
import { ScopeAndSequenceDocument as Version1 } from "@/root/narp/app/planner/_util/plannerDocuments/version1.ts";

export function Component() {
  const { id } = useParams();
  const topRealm = useTopRealm();
  const GET = useGET();
  const pushAlert = usePushAlert();

  const [data, setData] = useState<SerializedDocument | null>(null);

  const updateData = useCallback(async () => {
    const result = await GET(
      `/v1/assets/${topRealm.externalID}/~/scopeAndSequence/${id}.json`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
        params: [["encoding", "text"]],
        headers: { ["Content-Type"]: "application/octet-stream" },
      }
    );
    if (result.error)
      return pushAlert(
        result.error.clientErrorDetail.userMessage,
        AlertType.ERROR
      );
    if (!result.response.ok)
      return pushAlert(await result.response.text(), AlertType.ERROR);
    if (!result.data) return pushAlert("missing data", AlertType.ERROR);

    const raw: { manifest?: "ver1" | "ver2" } = JSON.parse(
      await result.data.text()
    );

    console.log(`[DEBUG] Loaded document from API:`, raw);

    let data: SerializedDocument;
    switch (raw.manifest) {
      case undefined:
        console.log(`[DEBUG] Converting from version 0 document`);
        data = fromVer0(raw as Version0);
        break;

      case "ver1":
        console.log(`[DEBUG] Converting from version 1 document`);
        data = fromVer1(raw as Version1);
        break;

      case "ver2":
        console.log(`[DEBUG] Using version 2 document directly`);
        data = raw as SerializedDocument;
        break;

      default:
        console.log(
          `[DEBUG] Unknown manifest, using default document with KINDERGARTEN`
        );
        data = {
          manifest: "ver2",
          title: "New Document",
          abstract: "",
          gradeLevel: GradeLevel.KINDERGARTEN,
          keywords: [],
          topics: [],
          goals: [],
          terms: [],
          activities: [],
        };
        break;
    }

    console.log(`[DEBUG] Final document data:`, data);

    setData(data);
  }, [id, pushAlert, GET, topRealm.eidURN, topRealm.externalID]);

  useEffect(() => {
    updateData();
  }, []);

  if (!data)
    return (
      <div className="size-full flex items-center justify-center">
        <div className="loading loading-spinner loading-xl" />
      </div>
    );

  return (
    <Provider data={data}>
      <Outlet />
    </Provider>
  );
}
