import { AssetNode } from "@/root/narp/app/asset-manager/_components/types.ts";
import DirectoryDetails from "@/root/narp/app/asset-manager/_components/details/DirectoryDetails.tsx";
import FileDetails from "@/root/narp/app/asset-manager/_components/details/FileDetails.tsx";

export default function Details({ node }: { node: AssetNode }) {
  if (node.entityType === "directory") {
    return <DirectoryDetails node={node} />;
  } else if (node.entityType === "file") {
    return <FileDetails node={node} />;
  }
}
