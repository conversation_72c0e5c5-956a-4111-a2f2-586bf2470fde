import {
  Dispatch,
  MouseEvent,
  SetStateAction,
  useCallback,
  useEffect,
  useState,
} from "react";
import { useConfiguration } from "@/util/configuration/ConfigurationContext.tsx";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { useNavigate } from "react-router-dom";
import { APIError } from "@/util/api/types.ts";
import { FaTrash } from "react-icons/fa6";
import { SerializedDocument } from "@/root/narp/app/planner/_util/plannerDocuments/ver2";

interface Entry {
  containerPath: string;
  createdTimestamp: string;
  entityType: "file" | "folder";
  physicalSizeInBytes: number;
  realmEID: string;
  updatedTimestamp: string;
  userBasename: string;
  userEID: string;
  userExtension: string;
  userPath: string;
  version: number;
}

export default function ScopeAndSequences() {
  const configuration = useConfiguration();
  const { getSession } = useAuth();
  const realm = useTopRealm();
  const pushError = usePushError();

  const [entries, setEntries] = useState<Entry[] | null>(null);

  const updateEntries = useCallback(async () => {
    setEntries(null);

    const [session, error] = await getSession();
    if (error || !session) return pushError("failed to retrieve session");

    const url = new URL(
      `/v1/assets/${realm.externalID}/~/scopeAndSequence/`,
      configuration.apiBaseURL,
    );

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        "X-Ayode-Asserted-Realm-EID": realm.eidURN,
        // Remove Content-Type for GET requests or use application/json
        "Accept": "application/json",
      },
    });

    if (!response.ok) {
      if (response.status === 404) return setEntries([]);

      const error: APIError = await response.json();
      return pushError(error.clientErrorDetail.userMessage);
    }

    if (response.status === 204) return setEntries([]);
    const entries: Entry[] = await response.json();
    if (!entries) return setEntries([]);
    setEntries(entries.filter((entry) => entry.entityType === "file"));
  }, [
    configuration.apiBaseURL,
    getSession,
    pushError,
    realm.eidURN,
    realm.externalID,
  ]);

  useEffect(() => {
    updateEntries();
  }, []);

  return (
    <div className="flex flex-col gap-3">
      <div className="text-xl font-bold">Scope & Sequences</div>

      <div className="grid gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {entries !== null ? (
          entries.length > 0 ? (
            entries.map((entry) => (
              <EntryCard
                entry={entry}
                key={entry.userBasename}
                setEntries={setEntries}
              />
            ))
          ) : (
            <div className="min-h-32 flex flex-col border border-neutral-200 p-5 rounded-lg">
              <div className="text-xl text-primary-500">
                No Scopes & Sequences 😔
              </div>
              <div className="text-sm">
                Click on the 'Create Now' button to create a new Scope &
                Sequence!
              </div>
            </div>
          )
        ) : (
          new Array(4)
            .fill(null)
            .map((_, i) => <div className="h-32 col-span-1 skeleton" key={i} />)
        )}
      </div>
    </div>
  );
}

function EntryCard({
  entry,
  setEntries,
}: {
  entry: Entry;
  setEntries: Dispatch<SetStateAction<Entry[] | null>>;
}) {
  const navigate = useNavigate();
  const configuration = useConfiguration();
  const { getSession } = useAuth();
  const realm = useTopRealm();
  const pushError = usePushError();
  const [data, setData] = useState<SerializedDocument | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  async function updateEntry() {
    setData(null);
    const [session, error] = await getSession();
    if (error || !session) return pushError("failed to retrieve session");

    const url = new URL(
      `/v1/assets/${realm.externalID}/~${entry.userPath}/${entry.userBasename}.${entry.userExtension}`,
      configuration.apiBaseURL,
    );
    url.searchParams.set("encoding", "text");

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        "X-Ayode-Asserted-Realm-EID": realm.eidURN,
        "Accept": "application/json",
      },
    });

    if (!response.ok) {
      const error: APIError = await response.json();
      return pushError(error.clientErrorDetail.userMessage);
    }

    setData(await response.json());
  }

  async function handleDelete(e: MouseEvent) {
    e.stopPropagation();
    setShowDeleteModal(true);
  }

  async function handleConfirmDelete() {
    setIsDeleting(true);
    const [session, error] = await getSession();
    if (error || !session) {
      setIsDeleting(false);
      setShowDeleteModal(false);
      return pushError("failed to retrieve session");
    }

    const url = new URL(
      `/v1/assets/${realm.externalID}/~${entry.userPath}/${entry.userBasename}.${entry.userExtension}`,
      configuration.apiBaseURL,
    );

    const response = await fetch(url.toString(), {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${session.getAccessToken().getJwtToken()}`,
        "X-Ayode-Asserted-Realm-EID": realm.eidURN,
        // Remove Content-Type for DELETE requests
      },
    });

    setIsDeleting(false);
    setShowDeleteModal(false);

    if (!response.ok) {
      const err: APIError = await response.json();
      return pushError(err.clientErrorDetail.userMessage);
    }

    setEntries((prev) => {
      if (prev === null) return prev;
      return prev.filter((item) => item.userBasename !== entry.userBasename);
    });
  }

  function handleCancelDelete() {
    setShowDeleteModal(false);
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    navigate(
      `/narp/app/planner/scopeAndSequence/edit/${encodeURIComponent(
        entry.userBasename,
      )}`,
    );
  }

  useEffect(() => {
    updateEntry();
  }, []);

  if (!data) {
    return <div className="col-span-1 h-32 skeleton" />;
  }
  return (
    <>
      <button
        onClick={handleClick}
        type="button"
        className="relative transition-colors flex flex-col items-start text-start border border-base-content/20 bg-base-100 rounded-lg p-5 h-32 cursor-pointer hover:border-primary-500 hover:bg-primary-050"
      >
        <div className="text-xl font-bold truncate w-full">
          {data?.title || "???"}
        </div>
        <div>
          {new Date(entry.createdTimestamp).toLocaleString(undefined, {
            dateStyle: "medium",
            timeStyle: "short",
          })}
        </div>

        <FaTrash
          className="absolute top-2 right-2 text-red-500 hover:text-red-700"
          onClick={handleDelete}
          title="Delete Scope & Sequence"
        />
      </button>

      {showDeleteModal && (
        <div className="fixed inset-0 backdrop-blur bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white border border-neutral-400 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold mb-4">Delete Scope & Sequence</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "
              {data?.title || entry.userBasename}"? This action cannot be
              undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={handleCancelDelete}
                disabled={isDeleting}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded hover:bg-gray-200 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 flex items-center gap-2"
              >
                {isDeleting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Deleting...
                  </>
                ) : (
                  "Delete"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
