import Provider from "@/root/narp/app/planner/scopeAndSequence/create/_components/Provider.tsx";
import { Outlet, useLocation } from "react-router-dom";
import {
  Chip,
  Chips,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Chip.tsx";
import BreadCrumbs from "@/util/components/bread-crumbs/BreadCrumbs.tsx";
import Crumb from "@/util/components/bread-crumbs/Crumb.tsx";
import titleize from "titleize";

export function Component() {
  const location = useLocation();
  const tokens = location.pathname.split("/");
  const baseToken = tokens.at(tokens.length - 1) || "";

  let step = 0;
  if (baseToken === "subregion") step = 1;
  if (baseToken === "authority") step = 2;
  if (baseToken === "standard") step = 3;
  if (baseToken === "customize") step = 4;
  if (baseToken === "generate") step = 5;

  return (
    <div className="size-full overflow-x-hidden overflow-y-auto">
      <div className="flex flex-col gap-5 min-h-full ">
        <div className="pt-5 px-5 flex flex-col gap-5">
          <BreadCrumbs>
            <Crumb label="Planner" to="/narp/app/planner" />
            <Crumb label="Create" to="/narp/app/planner/scopeAndSequence" />
            <Crumb
              label={titleize(baseToken)}
              to={location.pathname}
              base
              active={false}
            />
          </BreadCrumbs>

          <div className="font-extrabold text-3xl">
            Generate Scope & Sequence
          </div>

          <Chips>
            <Chip label="Choose Region" index={1} active={step >= 0} />
            <Chip label="Choose Subregion" index={2} active={step >= 1} />
            <Chip label="Choose Authority" index={3} active={step >= 2} />
            <Chip label="Choose Standard" index={4} active={step >= 3} />
            <Chip label="Customize Plan" index={5} active={step >= 4} />
            <Chip label="Generate" index={6} active={step >= 5} base />
          </Chips>

          <div className="border border-neutral-300 rounded-full p-0 m-0" />
        </div>
        <Provider>
          <Outlet />
        </Provider>
      </div>
    </div>
  );
}
