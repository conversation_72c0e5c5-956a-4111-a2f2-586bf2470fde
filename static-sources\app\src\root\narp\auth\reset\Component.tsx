import { NavLink, useNavigate, usePara<PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aLock } from "react-icons/fa6";
import { FaUser } from "react-icons/fa";
import { FormEvent, useState } from "react";
import { check } from "@/util/password.ts";
import { useAuth } from "@/util/auth/AuthContext.tsx";

export function Component() {
  const { username: specifiedUsername } = useParams();
  const navigate = useNavigate();
  const { confirmPasswordReset } = useAuth();

  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const code = data.get("code")?.toString();
    const password = data.get("password")?.toString();
    const username = data.get("username")?.toString() || specifiedUsername;

    if (code === undefined || password === undefined || username === undefined)
      return setError(new Error("Missing fields"));
    if (!check(password))
      return setError(
        new Error("Password does not meet minimum security requirements"),
      );

    setLoading(true);
    const error = await confirmPasswordReset(code, password, username);
    setLoading(false);

    if (error) return setError(error);
    return navigate("/narp/auth");
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5">
      <form
        onSubmit={handleSubmit}
        className="max-w-xs w-full p-5 flex flex-col gap-3"
      >
        <div className={"text-xl my-3 font-bold text-center"}>
          Reset Password
        </div>
        <div className={"text-center"}>Check your email for reset code</div>

        {specifiedUsername ? (
          <input hidden readOnly name={"username"} value={specifiedUsername} />
        ) : (
          <fieldset className={"fieldset gap-0"}>
            <label className={"input floating-label validator"}>
              <span>Username / Email</span>
              <FaUser className={"text-base-content/20"} />
              <input
                type={"text"}
                required
                name="username"
                autoComplete="username"
                placeholder="Username / Email"
              />
            </label>
            <p className={"hidden validator-hint"}>Required</p>
          </fieldset>
        )}

        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Code</span>
            <FaLock className={"text-base-content/20"} />
            <input
              type={"text"}
              required
              name={"code"}
              maxLength={6}
              placeholder={"Code"}
            />
          </label>
          <p className={"hidden validator-hint"}>Required</p>
        </fieldset>

        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>New Password</span>
            <FaKey className={"text-base-content/20"} />
            <input
              type={"password"}
              required
              name={"password"}
              autoComplete={"new-password"}
              placeholder={"New Password"}
              minLength={8}
              pattern={`/^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[$^*.[]{}()?"!@#%&/\\,><':;|_~\`=+- ])[A-Za-z0-9$^*.[]{}()?"!@#%&/\\,><':;|_~\`=+- ]{6,256}$/`}
              title={
                "Must be more than 8 characters, less than 255 characters, including number, lowercase letter, uppercase letter, and special character"
              }
            />
          </label>
          <p className="hidden validator-hint">
            Must be more than 8 characters including,
            <br />
            At least one special character
            <br />
            At least one lowercase letter
            <br />
            At least one uppercase letter
          </p>
        </fieldset>
        <button
          disabled={loading}
          type="submit"
          className={`btn ${error ? "btn-error" : "btn-primary"}`}
        >
          {loading ? <div className="loading" /> : <>Reset</>}
        </button>

        {error ? (
          <div className="text-error text-center">{error?.message}</div>
        ) : (
          <></>
        )}

        <div className="divider m-0">OR</div>

        <NavLink className="btn btn-secondary no-animation " to="/narp/auth">
          Back To Sign In
        </NavLink>
      </form>
    </div>
  );
}
