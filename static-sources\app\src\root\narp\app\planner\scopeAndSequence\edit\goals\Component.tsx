import { useLocation, useParams } from "react-router-dom";
import {
  EntityID,
  Goal,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import BreadCrumbs from "@/util/components/bread-crumbs/BreadCrumbs.tsx";
import Crumb from "@/util/components/bread-crumbs/Crumb.tsx";
import { LuCalendarCheck } from "react-icons/lu";
import NavBar from "@/root/narp/app/planner/scopeAndSequence/edit/_components/NavBar.tsx";
import {
  FaSearch,
  FaChevronDown,
  FaFolder,
  FaFolderOpen,
  FaEdit,
} from "react-icons/fa";
import { FormEvent, useMemo, useState, useEffect } from "react";
import { FaBorderAll, FaListUl } from "react-icons/fa6";
import Fuse from "fuse.js";
import ListView from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/ListView.tsx";
import GridView from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/GridView.tsx";
import EditMenu from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/EditMenu.tsx";
import useSessionState from "@/util/hooks/useSessionState.tsx";
import titleize from "titleize";

interface GroupedGoals {
  [groupKey: string]: Goal[];
}

export function Component() {
  const { id } = useParams();
  const location = useLocation();
  const {
    goal: { entries: allEntries },
    outline: { title },
  } = useEditorContext();

  const [displayMode, setDisplayMode] = useSessionState<"GRID" | "LIST">(
    location.pathname + ":displayMode",
    "LIST"
  );
  const [groupBy, setGroupBy] = useSessionState<"none" | "type" | "priority">(
    location.pathname + ":groupBy",
    "type"
  );

  // Ensure displayMode is always valid
  const validDisplayMode =
    displayMode === "GRID" || displayMode === "LIST" ? displayMode : "LIST";
  const [collapsedGroupsRaw, setCollapsedGroups] = useSessionState<unknown[]>(
    location.pathname + ":collapsedGroups",
    []
  );
  // Always use a Set for collapsedGroups
  const collapsedGroups = useMemo(() => {
    if (collapsedGroupsRaw instanceof Set) return collapsedGroupsRaw;
    if (Array.isArray(collapsedGroupsRaw)) return new Set(collapsedGroupsRaw);
    return new Set();
  }, [collapsedGroupsRaw]);

  // When collapsedGroups changes, sync back to session state as an array
  useEffect(() => {
    setCollapsedGroups(Array.from(collapsedGroups));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [collapsedGroups]);

  const [searchString, setSearchString] = useState<string>("");
  const [editID, setEditID] = useState<EntityID | null>(null);

  const filteredEntries: Goal[] = useMemo(() => {
    if (searchString == "" || !searchString) return allEntries;
    const fuse = new Fuse<Goal>(allEntries, {
      keys: ["type", "priority", "description"],
    });
    const results = fuse.search(searchString);
    return results.map((result) => result.item);
  }, [searchString, allEntries]);

  const groupedEntries: GroupedGoals = useMemo(() => {
    if (groupBy === "none") {
      return { "All Goals": filteredEntries };
    }

    const grouped: GroupedGoals = {};

    filteredEntries.forEach((goal) => {
      let groupKey: string;

      if (groupBy === "type") {
        groupKey = goal.type || "No Type";
      } else if (groupBy === "priority") {
        groupKey = titleize(goal.priority);
      } else {
        groupKey = "All Goals";
      }

      if (!grouped[groupKey]) {
        grouped[groupKey] = [];
      }
      grouped[groupKey].push(goal);
    });

    return grouped;
  }, [filteredEntries, groupBy]);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  function toggleGroup(groupKey: string) {
    const newCollapsed = new Set(collapsedGroups);
    if (newCollapsed.has(groupKey)) {
      newCollapsed.delete(groupKey);
    } else {
      newCollapsed.add(groupKey);
    }
    setCollapsedGroups(Array.from(newCollapsed));
  }

  const groupKeys = Object.keys(groupedEntries).sort();

  return (
    <div className="size-full overflow-y-auto overflow-x-hidden flex flex-col py-5 gap-5">
      <EditMenu editID={editID} setEditID={setEditID} />

      <div className="flex flex-col gap-3 px-5">
        <BreadCrumbs>
          <Crumb
            icon={<LuCalendarCheck className="size-4" />}
            label="Planner"
            to="/narp/app/planner"
          />
          <Crumb
            label={title}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}`}
          />
          <Crumb
            label="Goals"
            base
            active={false}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}/goals`}
          />
        </BreadCrumbs>
        <NavBar />
      </div>

      <div className="flex flex-row gap-3 sticky top-5 px-5 z-10">
        {/* Group By Selector */}
        <select
          value={groupBy}
          onChange={(e) =>
            setGroupBy(e.target.value as "none" | "type" | "priority")
          }
          className="select select-lg shadow"
        >
          <option value="none">No Grouping</option>
          <option value="type">Group by Type</option>
          <option value="priority">Group by Priority</option>
        </select>

        {/* View Mode Toggle */}

        <div className="join shadow">
          <button
            onClick={() => setDisplayMode("LIST")}
            type="button"
            className={`btn btn-lg btn-square join-item transition-colors ${validDisplayMode === "LIST" && "btn-primary"}`}
          >
            <FaListUl />
          </button>

          <button
            onClick={() => setDisplayMode("GRID")}
            type="button"
            className={`btn btn-lg btn-square join-item transition-colors ${validDisplayMode === "GRID" && "btn-primary"}`}
          >
            <FaBorderAll />
          </button>
        </div>

        {/* Search */}
        <label className="input input-lg grow shadow">
          <FaSearch className="text-neutral-300" />
          <input
            type="text"
            autoComplete="off"
            autoCapitalize="off"
            placeholder="Search goals..."
            onInput={handleInput}
          />
        </label>
      </div>

      {/* Grouped Content */}
      <div className="flex flex-col gap-4">
        {groupKeys.map((groupKey) => {
          const isCollapsed = collapsedGroups.has(groupKey);
          const groupGoals = groupedEntries[groupKey];

          if (groupBy === "none") {
            // No grouping - render directly
            return validDisplayMode === "LIST" ? (
              <ListView key="all" entries={groupGoals} setEditID={setEditID} />
            ) : (
              <GridView key="all" entries={groupGoals} setEditID={setEditID} />
            );
          }

          return (
            <div key={groupKey} className="px-5">
              {/* Group Header */}
              <div
                className="flex items-center gap-3 p-3 bg-gray-100 rounded-t-lg cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => toggleGroup(groupKey)}
              >
                {isCollapsed ? <FaFolder /> : <FaFolderOpen />}
                <h3 className="text-lg font-bold">{groupKey}</h3>
                <div className="bg-gray-300 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">
                  {groupGoals.length}{" "}
                  {groupGoals.length === 1 ? "goal" : "goals"}
                </div>
                <div className="ml-auto">
                  <FaChevronDown
                    className={`transition-transform ${isCollapsed ? "rotate-180" : "rotate-0"}`}
                  />
                </div>
              </div>

              {/* Group Content */}
              {!isCollapsed && (
                <div className="border-x border-b border-gray-200 rounded-b-lg p-4 bg-white">
                  {validDisplayMode === "LIST" ? (
                    <div className="flex flex-col gap-3">
                      {groupGoals.map((goal) => (
                        <div
                          key={goal.id}
                          className="p-3 border border-neutral-300 rounded-lg flex flex-row gap-3"
                        >
                          {/* Goal content - simplified from ListView Entry */}
                          <div className="flex flex-col grow">
                            <div className="flex flex-row gap-3 items-center mb-2">
                              <span className="text-lg font-bold">
                                {goal.type === null ? "No Type" : goal.type}
                              </span>
                              <div className="bg-neutral-200 rounded-full p-0.5 px-2 font-bold text-sm">
                                {titleize(goal.priority)}
                              </div>
                            </div>
                            <div>{goal.description}</div>
                          </div>
                          <div className="flex gap-1">
                            <button
                              onClick={() => setEditID(goal.id)}
                              className="btn btn-square btn-sm hover:btn-primary"
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 gap-3">
                      {groupGoals.map((goal) => (
                        <div
                          key={goal.id}
                          className="p-3 border border-neutral-300 rounded-lg"
                        >
                          <div className="flex flex-row gap-3 items-center mb-2">
                            <span className="font-bold">
                              {goal.type === null ? "No Type" : goal.type}
                            </span>
                            <button
                              onClick={() => setEditID(goal.id)}
                              className="btn btn-square btn-xs hover:btn-primary ml-auto"
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                          </div>
                          <div className="mb-2">
                            <div className="bg-neutral-200 rounded-full p-0.5 px-2 font-bold text-xs inline-block">
                              {titleize(goal.priority)}
                            </div>
                          </div>
                          <div className="text-sm">{goal.description}</div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Add New Goal Button - Always visible */}
      <AddNewGoalButton />
    </div>
  );
}

function AddNewGoalButton() {
  const {
    goal: { add },
  } = useEditorContext();

  return (
    <div className="px-5">
      <button onClick={() => add()} className="btn btn-lg btn-success w-full">
        Add New Goal
      </button>
    </div>
  );
}
