import { useCallback, useEffect, useState } from "react";
import {
  Standard,
  useCreateScopeSequenceContext,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import { Navigate, useNavigate } from "react-router-dom";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { v4 as uuidv4 } from "uuid";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import Lot<PERSON> from "lottie-react";
import loadingAnimationData from "@/assets/animations/code-magic.lottie.json";
import { SerializedDocument } from "@/root/narp/app/planner/_util/plannerDocuments/ver2.ts";
import usePOST from "@/util/api/usePOST.tsx";
import useGET from "@/util/api/useGET.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import {
  Goal,
  GoalPriority,
  LexicalCategory,
  Term,
  Topic,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { GradeLevel } from "@/util/standards.ts";

// Error types for better error categorization
enum ErrorType {
  NETWORK_ERROR = "network",
  SERVER_ERROR = "server",
  VALIDATION_ERROR = "validation",
  TIMEOUT_ERROR = "timeout",
  PERMISSION_ERROR = "permission",
  UNKNOWN_ERROR = "unknown",
}

interface ErrorDetail {
  type: ErrorType;
  message: string;
  retryable: boolean;
  timestamp: Date;
  context?: string;
}

interface Block {
  authoritySpecificIdentifier: string;
  classifications: {
    categoryLabel: string;
    classificationSystem: string;
  };
  text: {
    en_US: string;
  };
}

/**
 * Extracts grade level from standard properties
 * This function maps standard identifiers and names to appropriate grade levels
 */
function extractGradeLevelFromStandard(standard: Standard): GradeLevel {
  const sectionName = standard.sectionName?.toLowerCase() || "";
  const formattedSectionNumber =
    standard.formattedSectionNumber?.toLowerCase() || "";

  // High School English patterns
  if (sectionName.includes("english i")) {
    return GradeLevel.NINTH_GRADE;
  }
  if (sectionName.includes("english ii")) return GradeLevel.TENTH_GRADE;
  if (sectionName.includes("english iii")) return GradeLevel.ELEVENTH_GRADE;
  if (sectionName.includes("english iv")) return GradeLevel.TWELFTH_GRADE;

  // Grade-specific patterns
  if (sectionName.includes("kindergarten")) return GradeLevel.KINDERGARTEN;
  if (sectionName.includes("grade 1") || sectionName.includes("first grade"))
    return GradeLevel.FIRST_GRADE;
  if (sectionName.includes("grade 2") || sectionName.includes("second grade"))
    return GradeLevel.SECOND_GRADE;
  if (sectionName.includes("grade 3") || sectionName.includes("third grade"))
    return GradeLevel.THIRD_GRADE;
  if (sectionName.includes("grade 4") || sectionName.includes("fourth grade"))
    return GradeLevel.FOURTH_GRADE;
  if (sectionName.includes("grade 5") || sectionName.includes("fifth grade"))
    return GradeLevel.FIFTH_GRADE;
  if (sectionName.includes("grade 6") || sectionName.includes("sixth grade"))
    return GradeLevel.SIXTH_GRADE;
  if (sectionName.includes("grade 7") || sectionName.includes("seventh grade"))
    return GradeLevel.SEVENTH_GRADE;
  if (sectionName.includes("grade 8") || sectionName.includes("eighth grade"))
    return GradeLevel.EIGHTH_GRADE;
  if (sectionName.includes("grade 9") || sectionName.includes("ninth grade"))
    return GradeLevel.NINTH_GRADE;
  if (sectionName.includes("grade 10") || sectionName.includes("tenth grade"))
    return GradeLevel.TENTH_GRADE;
  if (
    sectionName.includes("grade 11") ||
    sectionName.includes("eleventh grade")
  )
    return GradeLevel.ELEVENTH_GRADE;
  if (sectionName.includes("grade 12") || sectionName.includes("twelfth grade"))
    return GradeLevel.TWELFTH_GRADE;

  // High school level patterns (for courses that span multiple grades)
  if (sectionName.includes("algebra i")) return GradeLevel.NINTH_GRADE;
  if (sectionName.includes("algebra ii")) return GradeLevel.TENTH_GRADE;
  if (sectionName.includes("geometry")) return GradeLevel.TENTH_GRADE;
  if (
    sectionName.includes("pre-calculus") ||
    sectionName.includes("precalculus")
  )
    return GradeLevel.ELEVENTH_GRADE;
  if (sectionName.includes("calculus")) return GradeLevel.TWELFTH_GRADE;

  // Level-based patterns (often used for high school electives)
  if (sectionName.includes("level i") || sectionName.includes("level 1"))
    return GradeLevel.NINTH_GRADE;
  if (sectionName.includes("level ii") || sectionName.includes("level 2"))
    return GradeLevel.TENTH_GRADE;
  if (sectionName.includes("level iii") || sectionName.includes("level 3"))
    return GradeLevel.ELEVENTH_GRADE;
  if (sectionName.includes("level iv") || sectionName.includes("level 4"))
    return GradeLevel.TWELFTH_GRADE;

  // Middle school patterns
  if (
    sectionName.includes("grades 6-8") ||
    sectionName.includes("middle school")
  )
    return GradeLevel.SEVENTH_GRADE;
  if (sectionName.includes("grades 7-8")) return GradeLevel.SEVENTH_GRADE;

  // High school patterns
  if (
    sectionName.includes("grades 9-12") ||
    sectionName.includes("high school")
  )
    return GradeLevel.TENTH_GRADE;

  // Check formatted section number for grade indicators
  const sectionNumberMatch = formattedSectionNumber.match(/\d+/);
  if (sectionNumberMatch) {
    const sectionNum = parseInt(sectionNumberMatch[0]);
    // Some patterns based on common section numbering schemes
    if (sectionNum >= 36 && sectionNum <= 39)
      return GradeLevel.NINTH_GRADE + (sectionNum - 36); // English I-IV
    if (sectionNum >= 110 && sectionNum <= 120)
      return GradeLevel.KINDERGARTEN + Math.min(sectionNum - 110, 12); // K-12 sequence
  }

  // Default to middle school if we can't determine (safer than kindergarten for unknown standards)
  return GradeLevel.SEVENTH_GRADE;
}

/**
 * Extracts key vocabulary terms from goal text
 * This function parses goal descriptions to identify important vocabulary terms
 */
function extractVocabularyTerms(goals: Goal[], standardText: string): Term[] {
  const termId = 1000; // Starting ID for terms, away from other IDs
  const terms: Term[] = [];

  // Enhanced subject-specific terms with more comprehensive coverage
  const subjectTermMap: {
    [subject: string]: { terms: string[]; indicators: string[] };
  } = {
    math: {
      terms: [
        "equation",
        "variable",
        "function",
        "algebra",
        "geometry",
        "calculus",
        "theorem",
        "polynomial",
        "coefficient",
        "integer",
        "fraction",
        "decimal",
        "ratio",
        "proportion",
        "percentage",
        "statistics",
        "probability",
        "slope",
        "intercept",
        "quadratic",
        "linear",
        "exponential",
        "logarithm",
        "derivative",
        "integral",
        "matrix",
        "vector",
        "coordinate",
        "graph",
        "domain",
        "range",
      ],
      indicators: [
        "math",
        "mathematics",
        "algebraic",
        "geometric",
        "calculate",
        "solve",
        "formula",
        "number",
        "numerical",
      ],
    },
    science: {
      terms: [
        "hypothesis",
        "experiment",
        "theory",
        "data",
        "observation",
        "conclusion",
        "variable",
        "control",
        "scientific method",
        "analysis",
        "evidence",
        "research",
        "investigation",
        "laboratory",
        "specimen",
        "organism",
        "cell",
        "molecule",
        "atom",
        "element",
        "compound",
        "reaction",
        "energy",
        "force",
        "motion",
        "velocity",
        "acceleration",
        "temperature",
        "pressure",
        "volume",
        "density",
        "ecosystem",
        "habitat",
        "biodiversity",
      ],
      indicators: [
        "science",
        "scientific",
        "biology",
        "chemistry",
        "physics",
        "experiment",
        "research",
        "study",
      ],
    },
    english: {
      terms: [
        "metaphor",
        "simile",
        "plot",
        "character",
        "setting",
        "theme",
        "conflict",
        "resolution",
        "protagonist",
        "antagonist",
        "narrative",
        "dialogue",
        "exposition",
        "climax",
        "symbolism",
        "irony",
        "alliteration",
        "personification",
        "hyperbole",
        "onomatopoeia",
        "rhetoric",
        "syntax",
        "semantics",
        "diction",
        "tone",
        "mood",
        "genre",
        "allegory",
        "foreshadowing",
        "flashback",
        "point of view",
        "perspective",
      ],
      indicators: [
        "english",
        "language arts",
        "literature",
        "reading",
        "writing",
        "grammar",
        "composition",
        "poetry",
        "prose",
      ],
    },
    history: {
      terms: [
        "primary source",
        "secondary source",
        "civilization",
        "culture",
        "government",
        "economics",
        "society",
        "politics",
        "democracy",
        "republic",
        "monarchy",
        "empire",
        "revolution",
        "constitution",
        "amendment",
        "legislation",
        "citizenship",
        "chronology",
        "timeline",
        "era",
        "dynasty",
        "colonization",
        "independence",
        "treaty",
        "alliance",
        "diplomacy",
        "archaeology",
        "artifact",
        "historical context",
      ],
      indicators: [
        "history",
        "historical",
        "social studies",
        "government",
        "civics",
        "geography",
        "cultural",
        "political",
      ],
    },
    art: {
      terms: [
        "technique",
        "medium",
        "composition",
        "perspective",
        "color theory",
        "texture",
        "form",
        "space",
        "balance",
        "contrast",
        "emphasis",
        "rhythm",
        "movement",
        "unity",
        "variety",
        "proportion",
        "scale",
        "line",
        "shape",
        "value",
        "hue",
        "saturation",
        "complementary",
        "analogous",
        "monochromatic",
        "abstract",
        "realistic",
        "impressionism",
        "expressionism",
        "cubism",
      ],
      indicators: [
        "art",
        "artistic",
        "visual",
        "creative",
        "design",
        "aesthetic",
        "drawing",
        "painting",
        "sculpture",
      ],
    },
    music: {
      terms: [
        "rhythm",
        "melody",
        "harmony",
        "tempo",
        "dynamics",
        "timbre",
        "pitch",
        "notation",
        "scale",
        "chord",
        "interval",
        "key signature",
        "time signature",
        "beat",
        "measure",
        "phrase",
        "cadence",
        "crescendo",
        "diminuendo",
        "staccato",
        "legato",
        "forte",
        "piano",
        "andante",
        "allegro",
        "composition",
        "improvisation",
        "ensemble",
        "solo",
      ],
      indicators: [
        "music",
        "musical",
        "sound",
        "audio",
        "instrument",
        "vocal",
        "rhythm",
        "melody",
      ],
    },
    technology: {
      terms: [
        "algorithm",
        "programming",
        "coding",
        "software",
        "hardware",
        "database",
        "network",
        "internet",
        "digital",
        "binary",
        "data structure",
        "debugging",
        "interface",
        "protocol",
        "encryption",
        "cybersecurity",
        "artificial intelligence",
        "machine learning",
        "cloud computing",
        "automation",
      ],
      indicators: [
        "technology",
        "computer",
        "digital",
        "programming",
        "software",
        "technical",
        "cyber",
        "electronic",
      ],
    },
    health: {
      terms: [
        "anatomy",
        "physiology",
        "nutrition",
        "metabolism",
        "cardiovascular",
        "respiratory",
        "nervous system",
        "immune system",
        "hormone",
        "enzyme",
        "protein",
        "carbohydrate",
        "lipid",
        "vitamin",
        "mineral",
        "exercise",
        "fitness",
        "wellness",
        "disease",
        "prevention",
        "diagnosis",
        "treatment",
      ],
      indicators: [
        "health",
        "medical",
        "body",
        "physical",
        "wellness",
        "fitness",
        "nutrition",
        "anatomy",
      ],
    },
  };

  // Get a collection of all text to analyze
  const allText =
    goals.map((goal) => goal.description).join(" ") + " " + standardText;
  const lowerText = allText.toLowerCase();

  // Enhanced subject detection with scoring
  const detectedSubjects: { subject: string; score: number }[] = [];

  for (const [subject, data] of Object.entries(subjectTermMap)) {
    let score = 0;

    // Score based on indicator words
    for (const indicator of data.indicators) {
      const regex = new RegExp(`\\b${indicator}\\b`, "gi");
      const matches = (lowerText.match(regex) || []).length;
      score += matches * 3; // Indicator words are weighted higher
    }

    // Score based on subject-specific terms
    for (const term of data.terms) {
      const regex = new RegExp(`\\b${term}\\b`, "gi");
      const matches = (lowerText.match(regex) || []).length;
      score += matches;
    }

    if (score > 0) {
      detectedSubjects.push({ subject, score });
    }
  }

  // Sort subjects by score
  detectedSubjects.sort((a, b) => b.score - a.score);

  // Extract terms from top 2 detected subjects
  const topSubjects = detectedSubjects.slice(0, 2);

  for (const { subject } of topSubjects) {
    const subjectData = subjectTermMap[subject];
    let termCount = 0;
    const maxTermsPerSubject =
      subject === detectedSubjects[0]?.subject ? 12 : 6; // More terms from primary subject

    for (const term of subjectData.terms) {
      if (termCount >= maxTermsPerSubject) break;

      // Check if term appears in the text (case insensitive)
      const termRegex = new RegExp(`\\b${term}\\b`, "i");
      if (
        termRegex.test(allText) &&
        !terms.some((t) => t.word.toLowerCase() === term.toLowerCase())
      ) {
        terms.push({
          id: termId + terms.length,
          parentTopic: goals[0]?.parentTopic || 0,
          word: term,
          category: categorizeWord(term),
          definition: generateDefinitionHint(term, subject),
        });
        termCount++;
      }
    }
  }

  // Enhanced pattern-based term extraction
  const goalText = goals.map((goal) => goal.description).join(" ");
  const combinedText = goalText + " " + standardText;

  // 1. Extract quoted terms (exact phrases in quotes)
  const quotedTerms = combinedText.match(/"([^"]+)"/g) || [];
  for (const quotedTerm of quotedTerms) {
    const cleanTerm = quotedTerm.replace(/"/g, "").trim();
    if (
      cleanTerm.length > 2 &&
      cleanTerm.length < 50 &&
      !terms.some((t) => t.word.toLowerCase() === cleanTerm.toLowerCase())
    ) {
      terms.push({
        id: termId + terms.length,
        parentTopic: goals[0]?.parentTopic || 0,
        word: cleanTerm,
        category: categorizeWord(cleanTerm),
        definition: generateDefinitionHint(
          cleanTerm,
          detectedSubjects[0]?.subject || "general"
        ),
      });
    }
  }

  // 2. Extract technical terms (capitalized multi-word phrases)
  const technicalTermRegex = /\b[A-Z][a-z]+(?: [A-Z][a-z]+)+\b/g;
  const technicalTerms = combinedText.match(technicalTermRegex) || [];
  for (const techTerm of technicalTerms) {
    if (
      techTerm.length < 50 &&
      !terms.some((t) => t.word.toLowerCase() === techTerm.toLowerCase())
    ) {
      terms.push({
        id: termId + terms.length,
        parentTopic: goals[0]?.parentTopic || 0,
        word: techTerm,
        category: categorizeWord(techTerm),
        definition: generateDefinitionHint(
          techTerm,
          detectedSubjects[0]?.subject || "general"
        ),
      });
    }
  }

  // 3. Extract important single capitalized words (but filter common words)
  const commonWords = new Set([
    "The",
    "A",
    "An",
    "This",
    "That",
    "These",
    "Those",
    "In",
    "On",
    "At",
    "By",
    "For",
    "With",
    "Without",
    "Through",
    "During",
    "Before",
    "After",
    "Above",
    "Below",
    "Up",
    "Down",
    "Out",
    "Off",
    "Over",
    "Under",
    "Again",
    "Further",
    "Then",
    "Once",
    "Here",
    "There",
    "When",
    "Where",
    "Why",
    "How",
    "All",
    "Any",
    "Both",
    "Each",
    "Few",
    "More",
    "Most",
    "Other",
    "Some",
    "Such",
    "Only",
    "Own",
    "Same",
    "So",
    "Than",
    "Too",
    "Very",
    "Can",
    "Will",
    "Just",
    "Should",
    "Now",
  ]);

  const capitalizedTermRegex = /\b[A-Z][a-z]{3,}\b/g;
  const capitalizedTerms = combinedText.match(capitalizedTermRegex) || [];
  for (const capTerm of capitalizedTerms) {
    if (
      !commonWords.has(capTerm) &&
      capTerm.length >= 4 &&
      capTerm.length < 20 &&
      !terms.some((t) => t.word.toLowerCase() === capTerm.toLowerCase())
    ) {
      terms.push({
        id: termId + terms.length,
        parentTopic: goals[0]?.parentTopic || 0,
        word: capTerm,
        category: categorizeWord(capTerm),
        definition: generateDefinitionHint(
          capTerm,
          detectedSubjects[0]?.subject || "general"
        ),
      });
    }
  }

  // 4. Extract domain-specific compound terms
  const compoundTermRegex = /\b[a-z]+(?:-[a-z]+)+\b/g;
  const compoundTerms = combinedText.match(compoundTermRegex) || [];
  for (const compound of compoundTerms) {
    if (
      compound.length >= 6 &&
      compound.length < 30 &&
      !terms.some((t) => t.word.toLowerCase() === compound.toLowerCase())
    ) {
      terms.push({
        id: termId + terms.length,
        parentTopic: goals[0]?.parentTopic || 0,
        word: compound,
        category: categorizeWord(compound),
        definition: generateDefinitionHint(
          compound,
          detectedSubjects[0]?.subject || "general"
        ),
      });
    }
  }

  // 5. Extract key educational terms based on action verbs
  const educationalActionRegex =
    /\b(?:analyze|synthesize|evaluate|compare|contrast|interpret|explain|demonstrate|identify|classify|categorize|investigate|explore|examine|assess|apply|implement|create|design|develop|construct|formulate)\b\s+([a-zA-Z\s]{3,30})/gi;
  const educationalMatches = [...combinedText.matchAll(educationalActionRegex)];
  for (const match of educationalMatches) {
    const concept = match[1].trim();
    if (
      concept.length >= 3 &&
      concept.length < 30 &&
      !terms.some((t) => t.word.toLowerCase() === concept.toLowerCase())
    ) {
      terms.push({
        id: termId + terms.length,
        parentTopic: goals[0]?.parentTopic || 0,
        word: concept,
        category: categorizeWord(concept),
        definition: generateDefinitionHint(
          concept,
          detectedSubjects[0]?.subject || "general"
        ),
      });
    }
  }

  // Remove duplicates and sort by relevance
  const uniqueTerms = terms.filter(
    (term, index, self) =>
      index ===
      self.findIndex((t) => t.word.toLowerCase() === term.word.toLowerCase())
  );

  // Score terms by relevance and limit to reasonable number
  const scoredTerms = uniqueTerms.map((term) => ({
    ...term,
    relevanceScore: calculateTermRelevance(
      term.word,
      allText,
      detectedSubjects
    ),
  }));

  // Sort by relevance score and take top terms
  scoredTerms.sort((a, b) => b.relevanceScore - a.relevanceScore);

  const finalTerms = scoredTerms
    .slice(0, 25)
    .map(({ relevanceScore: _, ...term }) => term);

  return finalTerms;
}

/**
 * Helper function to categorize words into lexical categories
 */
function categorizeWord(word: string): LexicalCategory {
  const lowerWord = word.toLowerCase();

  // Common verb patterns
  if (
    lowerWord.endsWith("ing") ||
    lowerWord.endsWith("tion") ||
    lowerWord.endsWith("ment") ||
    lowerWord.includes("analyze") ||
    lowerWord.includes("evaluate") ||
    lowerWord.includes("compare") ||
    lowerWord.includes("investigate") ||
    lowerWord.includes("explore") ||
    lowerWord.includes("examine")
  ) {
    return LexicalCategory.VERB;
  }

  // Common adjective patterns
  if (
    lowerWord.endsWith("ive") ||
    lowerWord.endsWith("ous") ||
    lowerWord.endsWith("ful") ||
    lowerWord.endsWith("less") ||
    lowerWord.endsWith("able") ||
    lowerWord.endsWith("ical")
  ) {
    return LexicalCategory.ADJECTIVE;
  }

  // Common adverb patterns
  if (lowerWord.endsWith("ly")) {
    return LexicalCategory.ADVERB;
  }

  // Default to noun for most terms
  return LexicalCategory.NOUN;
}

/**
 * Helper function to generate definition hints for terms
 */
function generateDefinitionHint(_term: string, subject: string): string {
  const subjectContexts: { [key: string]: string } = {
    math: "A mathematical concept or technique used in problem-solving and analysis.",
    science:
      "A scientific principle, process, or method used in research and investigation.",
    english:
      "A literary or linguistic device used in reading, writing, and communication.",
    history:
      "A historical concept, event, or method used in studying past societies and cultures.",
    art: "An artistic technique, principle, or element used in creative expression and design.",
    music:
      "A musical element, technique, or concept used in composition and performance.",
    technology:
      "A technological concept, tool, or method used in digital systems and programming.",
    health:
      "A health-related concept, process, or principle related to human body and wellness.",
    general:
      "An important academic concept or term relevant to the subject area.",
  };

  return subjectContexts[subject] || subjectContexts.general;
}

/**
 * Helper function to calculate term relevance based on frequency and context
 */
function calculateTermRelevance(
  term: string,
  text: string,
  detectedSubjects: { subject: string; score: number }[]
): number {
  const lowerText = text.toLowerCase();
  const lowerTerm = term.toLowerCase();

  let score = 0;

  // Base frequency score
  const termRegex = new RegExp(`\\b${lowerTerm}\\b`, "g");
  const frequency = (lowerText.match(termRegex) || []).length;
  score += frequency * 2;

  // Bonus for terms in subject areas
  for (const { subject, score: subjectScore } of detectedSubjects) {
    const subjectTermMap: { [key: string]: string[] } = {
      math: [
        "equation",
        "variable",
        "function",
        "algebra",
        "geometry",
        "calculus",
        "theorem",
        "polynomial",
      ],
      science: [
        "hypothesis",
        "experiment",
        "theory",
        "data",
        "observation",
        "analysis",
        "research",
      ],
      english: [
        "metaphor",
        "simile",
        "plot",
        "character",
        "theme",
        "narrative",
        "rhetoric",
      ],
      history: [
        "primary source",
        "civilization",
        "culture",
        "government",
        "politics",
        "chronology",
      ],
      art: [
        "technique",
        "medium",
        "composition",
        "perspective",
        "color theory",
        "balance",
      ],
      music: ["rhythm", "melody", "harmony", "tempo", "dynamics", "notation"],
    };

    if (subjectTermMap[subject]?.includes(lowerTerm)) {
      score += subjectScore * 0.5;
    }
  }

  // Bonus for educational action words context
  const actionWords = [
    "analyze",
    "evaluate",
    "compare",
    "investigate",
    "explore",
    "examine",
    "assess",
    "apply",
    "create",
    "design",
  ];
  for (const action of actionWords) {
    if (
      lowerText.includes(`${action} ${lowerTerm}`) ||
      lowerText.includes(`${lowerTerm} ${action}`)
    ) {
      score += 3;
    }
  }

  // Bonus for capitalized terms (likely proper nouns or important concepts)
  if (term.charAt(0) === term.charAt(0).toUpperCase()) {
    score += 1;
  }

  // Penalty for very common words
  const commonWords = [
    "information",
    "knowledge",
    "understanding",
    "learning",
    "student",
    "teacher",
    "school",
    "education",
    "study",
    "work",
  ];
  if (commonWords.includes(lowerTerm)) {
    score -= 2;
  }

  return score;
}

/**
 * Extracts keywords from standard and goal text
 * This function analyzes goal descriptions and standard text to identify important keywords
 */
function extractKeywords(
  goals: Goal[],
  standardText: string,
  standard?: Standard
): string[] {
  const keywords: string[] = [];

  // Common educational keywords by subject area
  const subjectKeywordMap: { [subject: string]: string[] } = {
    math: [
      "mathematics",
      "numeracy",
      "problem solving",
      "computation",
      "mathematical reasoning",
      "logical thinking",
    ],
    science: [
      "scientific inquiry",
      "scientific method",
      "investigation",
      "discovery",
      "observation",
      "experimentation",
    ],
    english: [
      "literacy",
      "reading comprehension",
      "writing",
      "communication",
      "language arts",
      "critical thinking",
    ],
    history: [
      "historical analysis",
      "civic literacy",
      "geography",
      "cultural awareness",
      "chronology",
      "primary sources",
    ],
    art: [
      "creativity",
      "expression",
      "aesthetics",
      "visual literacy",
      "design",
      "cultural appreciation",
    ],
    music: [
      "musical literacy",
      "performance",
      "composition",
      "appreciation",
      "auditory skills",
    ],
    general: [
      "critical thinking",
      "collaboration",
      "creativity",
      "communication",
      "problem-solving",
      "21st century skills",
    ],
  };

  // Get all text to analyze
  const allText =
    goals.map((goal) => goal.description).join(" ") + " " + standardText;
  const lowerText = allText.toLowerCase();

  // Identify which subject area this might be
  let detectedSubject = "";
  let maxMatches = 0;

  for (const [subject, termList] of Object.entries(subjectKeywordMap)) {
    let matches = 0;
    for (const term of termList) {
      if (lowerText.includes(term)) {
        matches++;
      }
    }

    if (matches > maxMatches) {
      maxMatches = matches;
      detectedSubject = subject;
    }
  }

  // Always add some general keywords
  keywords.push(...subjectKeywordMap.general.slice(0, 3));

  // Add subject-specific keywords
  if (detectedSubject && subjectKeywordMap[detectedSubject]) {
    keywords.push(...subjectKeywordMap[detectedSubject].slice(0, 4));
  }

  // Extract specific terms from standard name/text
  if (standardText) {
    // Extract subject area from standard name if available
    if (standard && standard.sectionName) {
      const subjectMatch = standard.sectionName.match(
        /\b(Math|Science|English|History|Art|Music|Language)\b/i
      );
      if (subjectMatch && subjectMatch[1]) {
        const subjectKey = subjectMatch[1].toLowerCase();
        const matchedSubject = Object.keys(subjectKeywordMap).find(
          (key) => key.includes(subjectKey) || subjectKey.includes(key)
        );
        if (matchedSubject) {
          keywords.push(...subjectKeywordMap[matchedSubject].slice(0, 3));
        }
      }
    }

    // Match significant terms (3+ letter words that start with capital Letter)
    const significantTerms = standardText.match(/\b[A-Z][a-z]{2,}\b/g) || [];
    for (const term of significantTerms) {
      if (!keywords.includes(term.toLowerCase()) && keywords.length < 10) {
        keywords.push(term);
      }
    }
  }

  // Look for key phrases in goal descriptions (two or more words often make good keywords)
  const goalText = goals.map((goal) => goal.description).join(" ");
  const phrases = goalText.match(/\b[A-Z][a-z]+ [a-z]+ [a-z]+\b/g) || []; // Match phrases like "Scientific inquiry process"
  for (const phrase of phrases) {
    if (!keywords.includes(phrase) && keywords.length < 12) {
      keywords.push(phrase);
    }
  }

  // Remove duplicates and limit to reasonable number
  return [...new Set(keywords)].slice(0, 10);
}

/**
 * Categorizes and formats errors for better handling and user feedback
 */
function categorizeError(error: unknown, context: string): ErrorDetail {
  const timestamp = new Date();

  // Handle different error types
  if (typeof error === "string") {
    // Check for specific error patterns
    if (
      error.toLowerCase().includes("network") ||
      error.toLowerCase().includes("fetch")
    ) {
      return {
        type: ErrorType.NETWORK_ERROR,
        message:
          "Network connection issue. Please check your internet connection.",
        retryable: true,
        timestamp,
        context,
      };
    }

    if (error.toLowerCase().includes("timeout")) {
      return {
        type: ErrorType.TIMEOUT_ERROR,
        message: "Request timed out. The server might be busy.",
        retryable: true,
        timestamp,
        context,
      };
    }

    if (
      error.toLowerCase().includes("unauthorized") ||
      error.toLowerCase().includes("permission")
    ) {
      return {
        type: ErrorType.PERMISSION_ERROR,
        message: "You don't have permission to perform this action.",
        retryable: false,
        timestamp,
        context,
      };
    }

    if (
      error.toLowerCase().includes("validation") ||
      error.toLowerCase().includes("invalid")
    ) {
      return {
        type: ErrorType.VALIDATION_ERROR,
        message: "Invalid data provided. Please check your selections.",
        retryable: false,
        timestamp,
        context,
      };
    }

    // Check for AI generation specific errors
    if (
      error.toLowerCase().includes("failed to start ai generation") ||
      error.toLowerCase().includes("ai generation") ||
      error.toLowerCase().includes("syllabus generation")
    ) {
      return {
        type: ErrorType.SERVER_ERROR,
        message:
          "AI generation service is unavailable. Please try again later.",
        retryable: true,
        timestamp,
        context,
      };
    }

    // Check for missing required parameters
    if (
      error.toLowerCase().includes("missing required") ||
      error.toLowerCase().includes("standard is missing") ||
      error.toLowerCase().includes("realm external id")
    ) {
      return {
        type: ErrorType.VALIDATION_ERROR,
        message:
          "Missing required information. Please go back and check your selections.",
        retryable: false,
        timestamp,
        context,
      };
    }

    // Check for JSON parsing errors
    if (
      error.toLowerCase().includes("invalid json") ||
      error.toLowerCase().includes("unexpected token")
    ) {
      return {
        type: ErrorType.SERVER_ERROR,
        message: "Server returned invalid data. Please try again.",
        retryable: true,
        timestamp,
        context,
      };
    }

    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: error,
      retryable: true,
      timestamp,
      context,
    };
  }

  // Handle HTTP response errors
  if (error && typeof error === "object" && "status" in error) {
    const status = (error as { status: number }).status;
    const errorWithMessage = error as {
      status: number;
      message?: string;
      details?: unknown;
    };

    if (status === 400) {
      return {
        type: ErrorType.VALIDATION_ERROR,
        message: `API rejected request (400): ${errorWithMessage.message || "Invalid payload structure or missing required fields"}`,
        retryable: false,
        timestamp,
        context,
      };
    }

    if (status === 401 || status === 403) {
      return {
        type: ErrorType.PERMISSION_ERROR,
        message: "You don't have permission to perform this action.",
        retryable: false,
        timestamp,
        context,
      };
    }

    if (status === 429) {
      return {
        type: ErrorType.TIMEOUT_ERROR,
        message:
          "Rate limit exceeded. The API is receiving too many requests. Please wait before trying again.",
        retryable: true,
        timestamp,
        context,
      };
    }

    if (status >= 400 && status < 500) {
      return {
        type: ErrorType.VALIDATION_ERROR,
        message: `Request error (${status}). Please check your data and try again.`,
        retryable: false,
        timestamp,
        context,
      };
    }

    if (status >= 500) {
      return {
        type: ErrorType.SERVER_ERROR,
        message: "Server error. Please try again later.",
        retryable: true,
        timestamp,
        context,
      };
    }
  }

  // Handle JavaScript Error objects
  if (error instanceof Error) {
    const errorMessage = error.message || "An unexpected error occurred";

    // Check for specific error patterns in Error messages
    if (
      errorMessage.toLowerCase().includes("network") ||
      errorMessage.toLowerCase().includes("fetch")
    ) {
      return {
        type: ErrorType.NETWORK_ERROR,
        message:
          "Network connection issue. Please check your internet connection.",
        retryable: true,
        timestamp,
        context,
      };
    }

    if (
      errorMessage.toLowerCase().includes("ai generation") ||
      errorMessage.toLowerCase().includes("syllabus")
    ) {
      return {
        type: ErrorType.SERVER_ERROR,
        message:
          "AI generation service encountered an error. Please try again.",
        retryable: true,
        timestamp,
        context,
      };
    }

    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: errorMessage,
      retryable: true,
      timestamp,
      context,
    };
  }

  // Fallback for unknown error types
  return {
    type: ErrorType.UNKNOWN_ERROR,
    message: "An unexpected error occurred",
    retryable: true,
    timestamp,
    context,
  };
}

export function Component() {
  const { standard, blockCount, region, subregion } =
    useCreateScopeSequenceContext();
  const topRealm = useTopRealm();
  const navigate = useNavigate();
  const pushError = usePushError();
  const POST = usePOST();
  const GET = useGET();

  const [bottomMessage, setMessage] = useState<string>(
    "Preparing to generate..."
  );
  const [errorCount, setErrorCount] = useState<number>(0);
  const [hasReachedErrorLimit, setHasReachedErrorLimit] =
    useState<boolean>(false);
  const [errorHistory, setErrorHistory] = useState<ErrorDetail[]>([]);
  const [isRetrying, setIsRetrying] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isCancelled, setIsCancelled] = useState(false);

  const ERROR_LIMIT = 10;
  const MAX_RETRIES = 3;

  const createAsset = useCallback(
    async (doc: SerializedDocument) => {
      if (hasReachedErrorLimit) {
        setMessage("Error limit reached. Cannot proceed.");
        return;
      }

      const newId = uuidv4().replace(/[^a-zA-Z0-9]/g, "");

      setMessage("Creating New Plan...");
      const { response, error, data } = await POST(
        `/v1/assets/${topRealm.externalID}/~/scopeAndSequence/${newId}.json`,
        {
          assertedRealmEidUrn: topRealm.eidURN,
          body: JSON.stringify(doc),
          headers: {
            ["Content-Type"]: "application/octet-stream",
          },
        }
      );

      setMessage("Loading...");

      if (error) {
        throw error.clientErrorDetail?.userMessage || "Failed to create asset";
      }

      if (!response.ok) {
        // FIX: For POST utility, if error is null but response is not ok,
        // the error details should be in the data blob if it exists
        let errorText = response.statusText;
        if (data) {
          try {
            errorText = await data.text();
          } catch (e) {
            // If we can't read the data, fall back to statusText
          }
        }
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      setMessage("Finished! Redirecting...");
      navigate(`/narp/app/planner/scopeAndSequence/edit/${newId}`);
    },
    [POST, navigate, topRealm.externalID, topRealm.eidURN, hasReachedErrorLimit]
  );

  const waitForGeneration = useCallback(
    async (data: APIEnvelope<unknown>): Promise<SerializedDocument> => {
      if (hasReachedErrorLimit) {
        throw "Error limit reached. Cannot proceed with generation.";
      }

      if (!data.envelope.asynchronousData)
        throw "missing expected asynchronous data";

      const start = Date.now();

      const timeInterval = setInterval(
        () =>
          setMessage(
            `Generating... (${Math.floor((Date.now() - start) / 1000)} s)`
          ),
        1000
      );

      await new Promise<void>((resolve) =>
        setTimeout(
          () => resolve(),
          (data.envelope.asynchronousData?.intervalInSecondsUntilFirstRequest ||
            0) * 1000
        )
      );

      return new Promise<SerializedDocument>((resolve) => {
        if (
          !data.envelope.asynchronousData
            ?.intervalInSecondsBetweenSubsequentRequests
        )
          throw "missing expected asynchronous data: intervalBetweenSubsequentRequests";

        const interval = setInterval(async () => {
          if (hasReachedErrorLimit) {
            clearInterval(timeInterval);
            clearInterval(interval);
            throw new Error("Error limit reached. Generation stopped.");
          }

          // API COMPLIANCE: Handle both possible response structures
          const responseData = data as {
            data?: { pollURL?: string };
            envelope: { asynchronousData?: { targetURL?: string } };
          };
          const pollingURL =
            responseData.data?.pollURL ||
            responseData.envelope.asynchronousData?.targetURL;

          if (!pollingURL) {
            clearInterval(timeInterval);
            clearInterval(interval);
            console.error("[DEBUG] No polling URL found in response:", {
              hasDataPollURL: !!responseData.data?.pollURL,
              hasTargetURL: !!responseData.envelope.asynchronousData?.targetURL,
              dataStructure: responseData.data
                ? Object.keys(responseData.data)
                : null,
              asyncDataStructure: responseData.envelope.asynchronousData
                ? Object.keys(responseData.envelope.asynchronousData)
                : null,
            });
            throw new Error(
              "Missing polling URL in API response. Cannot continue generation polling."
            );
          }

          console.log("[DEBUG] Using polling URL:", pollingURL);

          try {
            const {
              response,
              error,
              data: pollingData,
            } = await GET(pollingURL, {
              assertedRealmEidUrn: topRealm.eidURN,
            });

            if (error) {
              clearInterval(timeInterval);
              clearInterval(interval);
              throw new Error(
                error.clientErrorDetail?.userMessage || "API request failed"
              );
            }

            console.log("[DEBUG] ===== POLLING RESPONSE ANALYSIS =====");
            console.log("[DEBUG] Polling response status:", response.status);
            console.log("[DEBUG] Polling response ok:", response.ok);

            if (!response.ok) {
              clearInterval(timeInterval);
              clearInterval(interval);

              // Special handling for rate limit in polling
              if (response.status === 429) {
                const rateLimitError = {
                  status: 429,
                  message: `Rate limit during polling: ${response.statusText}`,
                };
                throw rateLimitError;
              }

              const httpError = {
                status: response.status,
                message: `Polling Error (${response.status}): ${response.statusText}`,
              };
              throw httpError;
            }

            // FIX: Use the pollingData blob from the GET utility instead of reading from response
            if (!pollingData) {
              console.error(
                "[DEBUG] No polling data received from GET utility"
              );
              return; // Continue polling
            }

            let pollingResponseText: string;
            try {
              // Convert blob data to text
              pollingResponseText = await pollingData.text();
              console.log(
                "[DEBUG] Successfully converted polling data to text, length:",
                pollingResponseText.length
              );
              console.log(
                "[DEBUG] Polling response preview:",
                pollingResponseText.substring(0, 150) + "..."
              );
            } catch (pollingTextError) {
              console.error(
                "[DEBUG] Failed to convert polling data to text:",
                pollingTextError
              );
              clearInterval(timeInterval);
              clearInterval(interval);
              throw new Error(
                `Failed to read polling response: ${pollingTextError instanceof Error ? pollingTextError.message : "Unknown error"}`
              );
            }

            console.log("[DEBUG] ===== POLLING RESPONSE READ COMPLETE =====");

            const parsed: APIEnvelope<
              { block: number; objectives: Block[] }[]
            > = JSON.parse(pollingResponseText);

            if (parsed.envelope.asynchronousData?.status === "pending") return;

            if (parsed.envelope.asynchronousData?.status === "failed") {
              clearInterval(timeInterval);
              clearInterval(interval);
              throw new Error("Generation failed on server");
            }

            clearInterval(timeInterval);
            clearInterval(interval);

            let topicIdCount = 0;
            const topics: Topic[] = [];
            let goalIdCount = 0;
            const goals: Goal[] = [];

            for (const group of parsed.data) {
              topics.push({
                dependencies: topicIdCount <= 0 ? [] : [topicIdCount - 1],
                duration: 7,
                essentialQuestions: "",
                evidenceOfLearning: "",
                id: topicIdCount,
                name: `Topic ${topicIdCount + 1}`,
                notes: "",
                resources: "",
                start: topicIdCount * 7,
              });

              for (const block of group.objectives) {
                goals.push({
                  description:
                    block.authoritySpecificIdentifier +
                    " : " +
                    block.text.en_US,
                  id: goalIdCount,
                  parentTopic: topicIdCount,
                  priority: GoalPriority.MUST_HAVE,
                  type: block.classifications.categoryLabel,
                });
                goalIdCount += 1;
              }
              topicIdCount += 1;
            }

            // Extract vocabulary terms from the goals and standard
            const standardText = standard?.text?.en_US || "";
            const vocabularyTerms = extractVocabularyTerms(goals, standardText);

            // Extract keywords from the goals and standard
            const keywords = extractKeywords(
              goals,
              standardText,
              standard || undefined
            );

            const extractedGradeLevel = extractGradeLevelFromStandard(
              standard!
            );

            resolve({
              abstract: `${region?.name || ""}, ${subregion?.name || ""}, ${standard?.formattedSectionNumber || ""} ${standard?.sectionName || ""} ${standard?.text?.en_US || ""}`,
              activities: [],
              goals: goals,
              keywords: keywords,
              terms: vocabularyTerms,
              topics: topics,
              gradeLevel: extractedGradeLevel,
              manifest: "ver2",
              title: `${standard?.formattedSectionNumber} ${standard?.sectionName}`,
            });
          } catch (intervalError) {
            clearInterval(timeInterval);
            clearInterval(interval);
            throw intervalError;
          }
        }, data.envelope.asynchronousData?.intervalInSecondsBetweenSubsequentRequests * 1000);
      });
    },
    [GET, standard, topRealm, region, subregion, hasReachedErrorLimit]
  );

  const initializeGeneration = useCallback(async (): Promise<
    APIEnvelope<unknown>
  > => {
    if (hasReachedErrorLimit) {
      throw new Error("Error limit reached. Cannot initialize generation.");
    }

    if (!standard || !blockCount) {
      navigate("../customize");
      throw new Error("Missing required parameters: standard or blockCount");
    }

    // Enhanced validation for standard properties
    if (!standard.urn) {
      throw new Error("Standard is missing required URN identifier");
    }

    // Create a working copy to avoid mutating the original standard object
    const workingStandard = { ...standard };

    // ALWAYS ensure text object exists - this is critical for API compliance

    // Create fallback text with priority: sectionName > levelTitle > formattedSectionNumber > URN
    function createFallbackText(): string {
      // First priority: sectionName (most descriptive)
      if (
        workingStandard.sectionName &&
        typeof workingStandard.sectionName === "string" &&
        workingStandard.sectionName.trim() !== ""
      ) {
        return workingStandard.sectionName.trim();
      }

      // Second priority: levelTitle
      if (
        workingStandard.levelTitle &&
        typeof workingStandard.levelTitle === "string" &&
        workingStandard.levelTitle.trim() !== "" &&
        workingStandard.levelTitle !== "Rule §"
      ) {
        return workingStandard.levelTitle.trim();
      }

      // Third priority: formattedSectionNumber with description
      if (
        workingStandard.formattedSectionNumber &&
        typeof workingStandard.formattedSectionNumber === "string" &&
        workingStandard.formattedSectionNumber.trim() !== ""
      ) {
        return `Educational Standard Section ${workingStandard.formattedSectionNumber.trim()}`;
      }

      // Fourth priority: Extract meaningful part from URN
      if (workingStandard.urn && typeof workingStandard.urn === "string") {
        const urnParts = workingStandard.urn.split(":");
        const lastPart = urnParts[urnParts.length - 1];
        if (lastPart && lastPart.trim() !== "") {
          return `Educational Standard ${lastPart}`;
        }
      }

      // Final fallback
      return "Educational Standard";
    }

    // Handle missing or malformed text data
    if (!workingStandard.text) {
      workingStandard.text = { en_US: createFallbackText() };
    } else {
      // Create a copy of the existing text object to avoid mutation
      workingStandard.text = { ...workingStandard.text };

      // Ensure en_US property exists and is valid
      if (
        !workingStandard.text.en_US ||
        typeof workingStandard.text.en_US !== "string" ||
        workingStandard.text.en_US.trim() === ""
      ) {
        workingStandard.text.en_US = createFallbackText();
      }
    }

    // Final safety check to ensure we have valid text
    if (
      !workingStandard.text ||
      !workingStandard.text.en_US ||
      workingStandard.text.en_US.trim() === ""
    ) {
      workingStandard.text = { en_US: "Educational Standard" };
    }

    if (!topRealm.externalID) {
      throw new Error("Realm external ID is required");
    }

    if (!topRealm.eidURN) {
      throw new Error("Realm EID URN is required");
    }

    // Use existing canonicalPath/parentCanonicalPath if available, otherwise construct them
    const canonicalPath =
      workingStandard.canonicalPath ||
      (workingStandard.levelTitle && workingStandard.sectionName
        ? `${workingStandard.levelTitle} > ${workingStandard.sectionName}`
        : workingStandard.sectionName ||
          workingStandard.levelTitle ||
          "Standard");

    const parentCanonicalPath =
      workingStandard.parentCanonicalPath ||
      workingStandard.levelTitle ||
      "Root";

    // Extract UUID from realm URN for API compliance
    const realmUUID = topRealm.eidURN.split(":").pop() || topRealm.externalID;

    // Construct payload exactly as shown in API documentation
    const payload = {
      realmEID: realmUUID,
      blockCount: parseInt(blockCount.toString(), 10),
      standards: [
        {
          urn: workingStandard.urn,
          levelTitle: workingStandard.levelTitle || "",
          sectionNumber: workingStandard.sectionNumber || 1,
          sectionName: workingStandard.sectionName || "",
          canonicalPath: canonicalPath,
          parentCanonicalPath: parentCanonicalPath,
          text: workingStandard.text,
          // Only include children if they exist and are not empty
          ...(workingStandard.children &&
            workingStandard.children.length > 0 && {
              children: workingStandard.children,
            }),
        },
      ],
    };

    setMessage("Initializing Generation...");

    try {
      const { response, error, data } = await POST(
        `/v1/ai/syllabus/async-generate`,
        {
          assertedRealmEidUrn: topRealm.eidURN,
          body: JSON.stringify(payload),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      setMessage("Loading...");

      if (error) {
        console.error("[DEBUG] API Error detected:", {
          error,
          clientErrorDetail: error.clientErrorDetail,
          userMessage: error.clientErrorDetail?.userMessage,
        });

        const errorMessage =
          error.clientErrorDetail?.userMessage ||
          "Failed to initialize generation";
        throw new Error(`API Error: ${errorMessage}`);
      }

      if (!response.ok) {
        console.error("[DEBUG] HTTP Error:", {
          status: response.status,
          statusText: response.statusText,
        });
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // FIX: Use the data blob from the API utility instead of reading from response
      if (!data) {
        throw new Error("No data received from server");
      }

      let responseText: string;
      try {
        // Convert blob data to text
        responseText = await data.text();
        console.log(
          "[DEBUG] Successfully converted data to text, length:",
          responseText.length
        );
        console.log(
          "[DEBUG] Response text preview:",
          responseText.substring(0, 200) + "..."
        );
      } catch (textError) {
        console.error("[DEBUG] Failed to convert data to text:", textError);
        throw new Error(
          `Failed to read response data: ${textError instanceof Error ? textError.message : "Unknown error"}`
        );
      }

      if (!responseText) {
        throw new Error("Empty response from server");
      }

      let parsedResponse;
      try {
        parsedResponse = JSON.parse(responseText);
      } catch (parseError) {
        console.error(
          "[DEBUG] Failed to parse response:",
          responseText.substring(0, 500) + "..."
        );
        throw new Error(
          "Invalid JSON response from server. Check if API endpoint is correct."
        );
      }

      console.log("[DEBUG] Parsed response from API:", {
        hasEnvelope: !!parsedResponse.envelope,
        hasData: !!parsedResponse.data,
        hasAsyncData: !!parsedResponse.envelope?.asynchronousData,
        status: parsedResponse.envelope?.asynchronousData?.status,
        responseKeys: Object.keys(parsedResponse),
      });

      // Validate the response structure according to API documentation
      if (!parsedResponse.envelope) {
        console.error("[DEBUG] Response missing envelope:", parsedResponse);
        throw new Error(
          "Invalid response format: missing envelope. This may indicate an authentication or API endpoint issue."
        );
      }

      if (!parsedResponse.envelope.asynchronousData) {
        console.error(
          "[DEBUG] Response missing asynchronous data:",
          parsedResponse.envelope
        );
        throw new Error(
          "Invalid response format: missing asynchronous data. The AI generation may not have started properly."
        );
      }

      // Check if the generation was accepted
      if (parsedResponse.envelope.asynchronousData.status === "failed") {
        throw new Error(
          "AI generation was rejected by the server. Check your standard data and try again."
        );
      }

      console.log("[DEBUG] Successfully initialized generation:", {
        envelopeStatus: parsedResponse.envelope.asynchronousData?.status,
        dataStatus: parsedResponse.data?.status,
        token: parsedResponse.data?.token,
        pollURL: parsedResponse.data?.pollURL,
        targetURL: parsedResponse.envelope.asynchronousData?.targetURL,
      });

      return parsedResponse;
    } catch (networkError) {
      console.error("[DEBUG] ===== FINAL ERROR HANDLER =====");
      console.error("[DEBUG] Error details:", networkError);

      // Handle different error types
      if (
        networkError instanceof TypeError &&
        networkError.message.includes("fetch")
      ) {
        throw new Error(
          "Network connection failed. Please check your internet connection."
        );
      }

      if (
        networkError instanceof Error &&
        networkError.message.includes("timeout")
      ) {
        throw new Error(
          "Request timed out. The server may be busy. Please try again in a few moments."
        );
      }

      // Re-throw other errors with additional context
      if (networkError instanceof Error) {
        throw new Error(`Generation failed: ${networkError.message}`);
      } else {
        throw new Error(`Unknown error occurred: ${String(networkError)}`);
      }
    }
  }, [standard, blockCount, POST, topRealm, navigate, hasReachedErrorLimit]);

  // Enhanced error handler
  const handleError = useCallback(
    (error: unknown, context: string): boolean => {
      const errorDetail = categorizeError(error, context);

      // Add to error history
      setErrorHistory((prev) => [...prev, errorDetail]);

      // Increment error count
      const newErrorCount = errorCount + 1;
      setErrorCount(newErrorCount);

      // Log error for debugging
      console.error(`[${context}] Error ${newErrorCount}/${ERROR_LIMIT}:`, {
        type: errorDetail.type,
        message: errorDetail.message,
        retryable: errorDetail.retryable,
        timestamp: errorDetail.timestamp,
        originalError: error,
      });

      // Check if we've reached the error limit
      if (newErrorCount >= ERROR_LIMIT) {
        setHasReachedErrorLimit(true);
        setMessage(
          `Error limit reached (${ERROR_LIMIT} errors). Generation stopped.`
        );
        pushError(
          `Too many errors encountered. Generation has been stopped after ${ERROR_LIMIT} attempts.`
        );
        return false; // Cannot continue
      }

      // Provide user-friendly error message
      const userMessage = `${errorDetail.message} (Error ${newErrorCount}/${ERROR_LIMIT})`;
      setMessage(userMessage);
      pushError(errorDetail.message);

      // Return whether this error is retryable
      return errorDetail.retryable;
    },
    [errorCount, ERROR_LIMIT, pushError]
  );

  const generate = useCallback(async () => {
    if (hasReachedErrorLimit) {
      setMessage("Error limit reached. Cannot proceed.");
      return;
    }

    setIsRetrying(true);

    try {
      console.log("[DEBUG] Starting generation process...");
      const envelope = await initializeGeneration();
      console.log("[DEBUG] Generation initialized, waiting for completion...");
      const doc = await waitForGeneration(envelope);
      console.log("[DEBUG] Generation completed, creating asset...");
      await createAsset(doc);
      console.log("[DEBUG] Asset created successfully!");

      // Reset retry count on success
      setRetryCount(0);
    } catch (err: unknown) {
      console.error("[DEBUG] Generation failed:", err);
      const canRetry = handleError(err, "Generation Process");

      // Special handling for rate limit errors - respect API's token bucket refill rate
      const isRateLimit =
        err &&
        typeof err === "object" &&
        "status" in err &&
        (err as { status: number }).status === 429;
      const isRateLimitString =
        typeof err === "string" &&
        (err.toLowerCase().includes("429") ||
          err.toLowerCase().includes("too many requests"));

      if (isRateLimit || isRateLimitString) {
        if (retryCount < MAX_RETRIES && !hasReachedErrorLimit) {
          const newRetryCount = retryCount + 1;
          setRetryCount(newRetryCount);

          // API has 0.1 tokens/second refill rate = 10 seconds per token
          // Using ultra-conservative 12.5 seconds per token (0.08 tokens/second) for safety
          // With capacity of 6 tokens, wait 75 seconds (6 × 12.5) plus extra safety margin
          const baseDelay = 75000; // 75 seconds base (6 tokens × 12.5 seconds)
          const safetyMargin = 45000; // Additional 45 seconds safety margin
          const retryDelay = Math.min(
            baseDelay + safetyMargin * newRetryCount,
            240000
          ); // Max 4 minutes

          setMessage(
            `AI service rate limit reached. API requires ${Math.round(retryDelay / 1000)} seconds between requests... (Attempt ${newRetryCount}/${MAX_RETRIES})`
          );

          console.log(
            `[DEBUG] Rate limit hit, using ultra-conservative 0.08 tokens/second rate. Scheduling retry ${newRetryCount}/${MAX_RETRIES} in ${retryDelay}ms`
          );

          setTimeout(() => {
            if (!hasReachedErrorLimit) {
              generate();
            }
          }, retryDelay);
        }
        return;
      }

      // If error is retryable and we haven't exceeded max retries, schedule a retry
      if (canRetry && retryCount < MAX_RETRIES && !hasReachedErrorLimit) {
        const newRetryCount = retryCount + 1;
        setRetryCount(newRetryCount);

        // Use conservative delays to avoid any chance of rate limiting
        // Minimum 15 seconds between retries to stay well below API limits
        const retryDelay = Math.min(
          15000 * Math.pow(1.5, newRetryCount),
          60000
        ); // Conservative backoff, max 60s
        setMessage(
          `Retrying in ${retryDelay / 1000} seconds... (Attempt ${newRetryCount}/${MAX_RETRIES})`
        );

        console.log(
          `[DEBUG] Conservative retry scheduling ${newRetryCount}/${MAX_RETRIES} in ${retryDelay}ms`
        );

        setTimeout(() => {
          if (!hasReachedErrorLimit) {
            generate();
          }
        }, retryDelay);
      } else {
        console.log("[DEBUG] Not retrying:", {
          canRetry,
          retryCount,
          MAX_RETRIES,
          hasReachedErrorLimit,
        });
      }
    } finally {
      setIsRetrying(false);
    }
  }, [
    initializeGeneration,
    waitForGeneration,
    createAsset,
    handleError,
    hasReachedErrorLimit,
    retryCount,
    MAX_RETRIES,
  ]);

  useEffect(() => {
    // API COMPLIANCE: Ultra-conservative timing to stay well below 0.1 tokens/second limit
    // Using 0.08 tokens/second equivalent (12.5 seconds per token) for safety margin
    // This prevents any possibility of hitting the rate limit
    const conservativeDelay =
      retryCount > 0 ? Math.min(12500 * Math.pow(2, retryCount), 60000) : 12500;

    console.log(
      `[DEBUG] API Compliance: Ultra-conservative timing - ${conservativeDelay}ms delay (${conservativeDelay / 1000}s)`
    );
    console.log(
      `[DEBUG] This stays well below 0.1 tokens/second limit (retry count: ${retryCount})`
    );

    const timer = setTimeout(() => {
      generate();
    }, conservativeDelay);

    return () => clearTimeout(timer);
  }, [generate, retryCount]);

  if (!standard || !blockCount) return <Navigate to={"../customize"} />;

  if (isCancelled) {
    return <Navigate to={"/narp/app"} />;
  }

  return (
    <div className="grow overflow-hidden flex flex-col items-center justify-center p-4">
      <div className="flex flex-col items-center justify-center w-full max-w-2xl">
        <Lottie
          animationData={loadingAnimationData}
          autoPlay
          loop={!hasReachedErrorLimit}
          className="w-full max-w-md max-h-80 sm:max-h-96 md:max-h-[20rem] mb-6"
        />

        {/* Main status message - larger and more prominent */}
        <div
          className={`text-center mb-4 ${
            hasReachedErrorLimit
              ? "text-red-500 font-semibold text-lg"
              : "text-lg font-medium text-gray-800"
          }`}
        >
          {bottomMessage}
        </div>

        {/* Progress details */}
        <div className="text-center space-y-2 mb-6">
          {isRetrying && !hasReachedErrorLimit && (
            <div className="text-sm text-blue-600 bg-blue-50 px-4 py-2 rounded-lg">
              Retrying... (Attempt {retryCount + 1}/{MAX_RETRIES})
            </div>
          )}

          {errorCount > 0 && !hasReachedErrorLimit && (
            <div className="text-sm text-orange-600 bg-orange-50 px-4 py-2 rounded-lg">
              {errorCount} error{errorCount > 1 ? "s" : ""} encountered (
              {errorCount}/{ERROR_LIMIT})
            </div>
          )}

          {/* Process details based on current message */}
          <div className="text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-lg max-w-md">
            {bottomMessage.includes("Preparing") &&
              "Setting up AI generation parameters..."}
            {bottomMessage.includes("Initializing") &&
              "Connecting to AI curriculum engine..."}
            {bottomMessage.includes("Generating") &&
              "AI is creating your personalized curriculum..."}
            {bottomMessage.includes("Creating New Plan") &&
              "Writing curriculum file to your workspace..."}
            {bottomMessage.includes("Loading") &&
              "Finalizing and preparing your scope & sequence..."}
            {bottomMessage.includes("Finished") &&
              "Success! Redirecting to your new curriculum..."}
            {bottomMessage.includes("rate limit") &&
              "Waiting for AI service to become available..."}
            {bottomMessage.includes("Retrying") &&
              "Attempting to reconnect to AI service..."}
            {bottomMessage.includes("Error limit") &&
              "Too many errors occurred. Generation stopped."}
          </div>
        </div>

        <button
          className="btn btn-outline btn-error"
          onClick={() => {
            setIsCancelled(true);
          }}
        >
          Cancel Generation
        </button>

        {hasReachedErrorLimit && (
          <div className="mt-6 text-center space-y-3 bg-red-50 p-6 rounded-lg border border-red-200">
            <div className="text-red-700 font-medium">
              Generation stopped after {ERROR_LIMIT} errors
            </div>
            {errorHistory.length > 0 && (
              <div className="text-sm text-red-600">
                Last error: {errorHistory[errorHistory.length - 1]?.message}
              </div>
            )}
            <div className="space-x-3">
              <button
                onClick={() => navigate("../customize")}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                ← Back to Customize
              </button>
              <button
                onClick={() => {
                  setErrorCount(0);
                  setHasReachedErrorLimit(false);
                  setErrorHistory([]);
                  setRetryCount(0);
                  setMessage("Preparing to generate...");
                  generate();
                }}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
