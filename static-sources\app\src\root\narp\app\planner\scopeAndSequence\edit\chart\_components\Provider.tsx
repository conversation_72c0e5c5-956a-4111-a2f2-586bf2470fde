import { Context } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";
import { ReactNode, useCallback, useEffect } from "react";
import { remToPx } from "@/util/unitConversions.ts";
import useSessionState from "@/util/hooks/useSessionState.tsx";
import { useLocation } from "react-router-dom";

const remPerDay = 10;
const maxScale = 1;
const minScale = 1 / 7 / 4;

export function Provider({ children }: { children: ReactNode }) {
  const location = useLocation();
  const [scale, setScaleValue] = useSessionState<number>(
    location.pathname + ":scale",
    1 / 7
  );

  // Always reset to week view when component mounts
  useEffect(() => {
    setScaleValue(1 / 7); // Force week view on mount
  }, [location.pathname]); // Reset when path changes (new scope and sequence)

  const timestampToPixels = useCallback(
    (timestamp: number) => {
      // Validate input to prevent NaN propagation
      if (!isFinite(timestamp)) return 0;
      const result = remToPx(scale * remPerDay) * timestamp;
      return isFinite(result) ? result : 0;
    },
    [scale]
  );

  const pixelsToTimestamp = useCallback(
    (pixels: number) => {
      // Validate input to prevent NaN propagation
      if (!isFinite(pixels)) return 0;
      const divisor = remToPx(scale * remPerDay);
      if (!isFinite(divisor) || divisor === 0) return 0;
      const result = pixels / divisor;
      return isFinite(result) ? result : 0;
    },
    [scale]
  );

  const zoomOut = useCallback(() => {
    setScaleValue((prev) => Math.max(minScale, prev - 0.1));
  }, []);

  const zoomIn = useCallback(() => {
    setScaleValue((prev) => Math.min(maxScale, prev + 0.1));
  }, []);

  const setScale = useCallback((value: number) => {
    setScaleValue(Math.max(Math.min(value, maxScale), minScale));
  }, []);

  return (
    <Context.Provider
      value={{
        maxScale,
        minScale,
        scale,
        setScale,
        zoomIn,
        zoomOut,
        timestampToPixels,
        pixelsToTimestamp,
      }}
    >
      {children}
    </Context.Provider>
  );
}
