import { useMemo } from "react";

interface PrivilegeAction {
  urn: string;
  name: {
    en_US: string;
    es_ES: string;
    fr_FR: string;
  };
}

interface PrivilegeMatrixEntry {
  privilegeActionURN: string;
  privilegeEIDurn: string;
  access: "allow" | "deny";
  origin: "direct" | "inherited";
  roleName: string;
  roleEIDurn: string;
  ownedByRealmEIDurn: string;
  targetRealmEIDurn: string;
  applicableRealmEIDurn: string;
}

interface PrivilegeMatrixProps {
  privilegeActions: PrivilegeAction[];
  privilegeMatrix: PrivilegeMatrixEntry[];
  loading: boolean;
}

// Helper function to get the symbol for privilege status
function getPrivilegeSymbol(
  access: "allow" | "deny",
  origin: "direct" | "inherited",
): string {
  if (origin === "direct") {
    return access === "allow" ? "✅" : "❌";
  } else {
    return access === "allow" ? "🔁✅" : "🔁❌";
  }
}

// Helper function to get the title for privilege status
function getPrivilegeTitle(
  access: "allow" | "deny",
  origin: "direct" | "inherited",
): string {
  if (origin === "direct") {
    return access === "allow" ? "Allow (Direct)" : "Deny (Direct)";
  } else {
    return access === "allow" ? "Inherited Allow" : "Inherited Deny";
  }
}

export default function PrivilegeMatrix({
  privilegeActions,
  privilegeMatrix,
  loading,
}: PrivilegeMatrixProps) {
  // Group matrix entries by role
  const roleMatrix = useMemo(() => {
    const roles = new Map<string, Map<string, PrivilegeMatrixEntry>>();

    privilegeMatrix.forEach((entry) => {
      if (!roles.has(entry.roleName)) {
        roles.set(entry.roleName, new Map());
      }
      roles.get(entry.roleName)!.set(entry.privilegeActionURN, entry);
    });

    return roles;
  }, [privilegeMatrix]);

  const sortedRoles = useMemo(() => {
    return Array.from(roleMatrix.keys()).sort();
  }, [roleMatrix]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (privilegeActions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <p>No privilege actions available</p>
      </div>
    );
  }

  if (sortedRoles.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <p>No roles found for this realm</p>
      </div>
    );
  }

  return (
    <div className="grow flex flex-col gap-4 overflow-hidden">
      <div className="p-3 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-2">Key:</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          <div>
            <span className="font-mono">✅</span> Allow (Direct)
          </div>
          <div>
            <span className="font-mono">❌</span> Deny (Direct)
          </div>
          <div>
            <span className="font-mono">🔁✅</span> Inherited Allow
          </div>
          <div>
            <span className="font-mono">🔁❌</span> Inherited Deny
          </div>
          <div>
            <span className="font-mono">∅</span> Not Referenced
          </div>
        </div>
      </div>
      <div className="border border-gray-300 rounded-lg overflow-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-100">
              <th className="relative md:sticky left-0 bg-gray-100 border-r border-gray-300 p-3 text-left font-medium min-w-[150px]">
                Role
              </th>
              {privilegeActions.map((action) => (
                <th
                  key={action.urn}
                  className="border-r border-gray-300 p-2 text-xs font-medium min-w-[120px] whitespace-nowrap"
                  title={action.urn}
                >
                  <div>{action.name.en_US}</div>
                  <div className="text-gray-500 text-xs">
                    {action.urn.split("/").pop()}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {sortedRoles.map((roleName, roleIndex) => {
              const rolePrivileges = roleMatrix.get(roleName)!;

              return (
                <tr
                  key={roleName}
                  className={roleIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  <td className="relative md:sticky left-0 bg-inherit border-r border-gray-300 p-3 font-medium">
                    <div className="truncate" title={roleName}>
                      {roleName}
                    </div>
                  </td>
                  {privilegeActions.map((action) => {
                    const entry = rolePrivileges.get(action.urn);

                    return (
                      <td
                        key={action.urn}
                        className="border-r border-gray-300 p-2 text-center min-w-[120px] whitespace-nowrap"
                      >
                        {entry ? (
                          <span
                            className="text-lg cursor-help"
                            title={getPrivilegeTitle(
                              entry.access,
                              entry.origin,
                            )}
                          >
                            {getPrivilegeSymbol(entry.access, entry.origin)}
                          </span>
                        ) : (
                          <span
                            className="text-gray-400"
                            title="Not Referenced"
                          >
                            ∅
                          </span>
                        )}
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
