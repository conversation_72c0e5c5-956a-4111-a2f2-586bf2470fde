export interface DirectoryNode {
  containedDirectoryCount: number;
  containedFileCount: number;
  containedFileVersionCount: number;
  containerPath: string;
  createdTimestamp: string;
  entityType: "directory";
  physicalSizeInBytes: number;
  realmEID: string;
  updatedTimestamp: string;
  userEID: string;
  userPath: string;
}

export interface FileNode {
  containerPath: string;
  createdTimestamp: string;
  entityType: "file";
  physicalSizeInBytes: number;
  realmEID: string;
  updatedTimestamp: string;
  userBasename: string;
  userEID: string;
  userExtension: string;
  userPath: string;
  version: number;
}

export type AssetNode = DirectoryNode | FileNode;
