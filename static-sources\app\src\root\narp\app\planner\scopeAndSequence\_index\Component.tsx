import { Fa<PERSON>ox<PERSON><PERSON>, FaWandMagicSparkles } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { MouseEvent, useState } from "react";
import BreadCrumbs, { BreadCrumb } from "@/util/components/BreadCrumbs.tsx";
import { LuCalendarCheck } from "react-icons/lu";
import { HttpMethod, useAPIRequest } from "@/util/API.tsx";
import {
  GradeLevel,
  ScopeAndSequenceDocument,
} from "@/root/narp/app/planner/_util/plannerDocuments/version1.ts";
import { v4 as uuidv4 } from "uuid";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";

const crumbs: BreadCrumb[] = [
  {
    label: "Planner",
    icon: <LuCalendarCheck className="size-4" />,
    to: "/narp/app/planner",
  },
  {
    label: "Create",
    to: "/narp/app/planner/scopeAndSequence",
  },
];

export function Component() {
  return (
    <div className="size-full p-5 flex flex-col">
      <BreadCrumbs crumbs={crumbs} />

      <div className="grow flex flex-col items-center justify-center gap-15">
        <div className="flex flex-col items-center gap-3">
          <div className="text-5xl font-bold">Scope & Sequence</div>
          <div>
            Create a blank Scope & Sequence or generate one using our{" "}
            <span className="font-bold text-primary-500">
              Ayode Intelligence
            </span>
            !
          </div>
        </div>

        <div className="flex flex-row gap-15 flex-wrap">
          <CreateEmptyButton />
          <CreateWithIntelligenceButton />
        </div>
      </div>
    </div>
  );
}

function CreateEmptyButton() {
  const request = useAPIRequest();
  const realm = useTopRealm();
  const pushError = usePushError();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);

  async function handleClick(e: MouseEvent) {
    e.preventDefault();

    if (loading) return;

    const document: ScopeAndSequenceDocument = {
      manifest: "ver1",
      outline: {
        title: "New Scope & Sequence",
        abstract: "",
        keywords: [],
        grade: GradeLevel.Kindergarten,
      },
      topics: [],
    };

    const newId = uuidv4().replace(/[^a-zA-Z0-9]/g, "");

    setLoading(true);
    const [response, error] = await request(
      `/v1/assets/${realm.externalID}/~/scopeAndSequence/${newId}.json`,
      HttpMethod.POST,
      {
        assertedRealm: realm,
        body: JSON.stringify(document),
        headers: { ["Content-Type"]: "application/octet-stream" },
      }
    );
    setLoading(false);

    if (!response) return pushError("Missing auth token");
    if (error) return pushError(error.clientErrorDetail.userMessage);

    navigate(`/narp/app/planner/scopeAndSequence/edit/${newId}`);
  }

  return (
    <button
      onClick={handleClick}
      type="button"
      disabled={loading}
      className="flex flex-col items-center justify-center size-40 border border-neutral-400 rounded-lg p-3 hover:bg-neutral-100 transition-colors cursor-pointer"
    >
      {loading ? (
        <div className="loading loading-spinner loading-xl" />
      ) : (
        <>
          <FaBoxOpen className="size-12 grow" />
          <span className="font-bold">Create Empty Plan</span>
        </>
      )}
    </button>
  );
}

function CreateWithIntelligenceButton() {
  const navigate = useNavigate();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    navigate("./create/region");
  }

  return (
    <button
      onClick={handleClick}
      className="flex flex-col items-center justify-center size-40 border border-neutral-400 rounded-lg p-3 hover:bg-neutral-100 transition-colors cursor-pointer"
    >
      <FaWandMagicSparkles className="size-12 grow" />
      <span className="font-bold">Generate with AI Assistant</span>
    </button>
  );
}
