import { <PERSON><PERSON><PERSON>, MouseEvent, useEffect, useMemo, useState } from "react";
import {
  Subregion,
  useCreateScopeSequenceContext,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import Fuse from "fuse.js";
import useGET from "@/util/api/useGET.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { FaSearch, FaThumbtack } from "react-icons/fa";
import { Navigate, useNavigate } from "react-router-dom";

export function Component() {
  const GET = useGET();
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const navigate = useNavigate();

  const { region } = useCreateScopeSequenceContext();

  const [subRegions, setSubRegions] = useState<Subregion[] | null>(null);

  async function updateSubRegions() {
    if (!region) return;

    const { error, response, data } = await GET(
      `/v1/worldwide-administrative-divisions/${region.urn}`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
      }
    );

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (data === null) return pushError("Missing data.");

    const envelope: APIEnvelope<Subregion[]> = JSON.parse(await data.text());
    if (!envelope.data) {
      pushError("region has no subregions");
      return navigate("../region");
    }
    setSubRegions(envelope.data);
  }

  useEffect(() => {
    updateSubRegions();
  }, []);

  if (!region) return <Navigate to={"../region"} />;

  if (!subRegions)
    return (
      <div className="grow flex items-center justify-center">
        <div className="loading loading-spinner loading-xl" />
      </div>
    );

  return <Content organizations={subRegions} />;
}

function Content({ organizations }: { organizations: Subregion[] }) {
  // Only Texas is supported for now
  const supportedStates = ["TX"];
  const { region, subregion, setAuthority } = useCreateScopeSequenceContext();
  const navigate = useNavigate();

  const [continuable, setContinuable] = useState<boolean>(false);
  const [searchString, setSearchString] = useState<string>("");

  const isUSSelected =
    region?.code === "US" ||
    region?.name.toLowerCase().includes("united states of america") ||
    region?.name.toLowerCase() === "united states of america";

  const { texasRegion, disabledCount } = useMemo(() => {
    if (!isUSSelected) {
      return {
        texasRegion: null,
        otherRegions: organizations,
        disabledCount: 0,
      };
    }

    const texas = organizations.find(
      (org) => org.name.toLowerCase().includes("texas") || org.code === "TX"
    );
    const others = organizations.filter(
      (org) => !org.name.toLowerCase().includes("texas") && org.code !== "TX"
    );

    // Count how many would be disabled (for informational purposes)
    const disabledCount = others.length;

    return {
      texasRegion: texas || null,
      otherRegions: [], // Hide other regions when US is selected since only Texas is available
      disabledCount: disabledCount,
    };
  }, [organizations, isUSSelected]);

  const searchEntries: Subregion[] = useMemo(() => {
    if (searchString == "" || !searchString) {
      if (isUSSelected && texasRegion) {
        return [texasRegion]; // Only show available options when US is selected
      }
      return organizations;
    }

    // When searching, only show available options
    const availableOrganizations = isUSSelected
      ? organizations.filter(
          (org) => org.name.toLowerCase().includes("texas") || org.code === "TX"
        )
      : organizations;

    const fuse = new Fuse<Subregion>(availableOrganizations, {
      keys: ["name"],
    });
    return fuse.search(searchString).map((result) => result.item);
  }, [organizations, searchString, isUSSelected, texasRegion]);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  useEffect(() => {
    setAuthority(null);
    setContinuable(!!subregion);
  }, [subregion]);

  function handleToNext(e: MouseEvent) {
    e.preventDefault();
    navigate("../authority");
  }

  function handleToPrevious(e: MouseEvent) {
    e.preventDefault();
    navigate("../region");
  }

  return (
    <>
      <div className="flex flex-row justify-between px-5">
        <div className="flex flex-col gap-1">
          <div className="text-xl font-bold">Choose a Subregion</div>
          <div>Choose the subregion you want to base your plan off of.</div>
          {isUSSelected && disabledCount > 0 && (
            <div className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md p-2 mt-2">
              <strong>Note:</strong> Currently, only Texas is available for
              United States regions. {50 - supportedStates.length} other states
              are not yet supported.
            </div>
          )}
        </div>

        <label className="input input-lg">
          <FaSearch className=" text-neutral-300 shrink-0" />
          <input onInput={handleInput} type=" text" placeholder="Search" />
        </label>
      </div>

      <div className="mb-auto touch-manipulation px-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {searchEntries.length === 0 && searchString ? (
          <div className="col-span-full text-center text-neutral-500 py-8">
            No available subregions found matching "{searchString}"
            {isUSSelected && (
              <div className="text-sm mt-2">
                Only Texas is currently available for United States regions.
              </div>
            )}
          </div>
        ) : (
          searchEntries.map((entry, index) => (
            <SubregionEntry
              subregion={entry}
              key={index}
              isPinned={
                isUSSelected &&
                (entry.name.toLowerCase().includes("texas") ||
                  entry.code === "TX")
              }
            />
          ))
        )}
      </div>

      <div className="bg-base-100 shadow-lg shadow-black border-t border-t-base-content/20 p-5 gap-3 flex flex-row justify-end">
        <button
          onClick={handleToPrevious}
          type="button"
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Back
        </button>

        <button
          onClick={handleToNext}
          type="button"
          disabled={!continuable}
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </>
  );
}

function SubregionEntry({
  subregion,
  isPinned,
}: {
  subregion: Subregion;
  isPinned?: boolean;
}) {
  const { subregion: currentSubregion, setSubregion } =
    useCreateScopeSequenceContext();
  const navigate = useNavigate();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setSubregion(subregion);
  }

  function handleDoubleClick(e: MouseEvent) {
    e.preventDefault();
    setSubregion(subregion);
    navigate("../authority");
  }

  return (
    <button
      title={subregion.name}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      type="button"
      className={`touch-pan-y btn btn-outline gap-3 ${
        currentSubregion?.code === subregion.code &&
        "outline-2 outline-offset-2"
      } ${isPinned ? "ring-2 ring-primary/20" : ""}`}
    >
      <div className="flex items-center gap-2">
        {isPinned && <FaThumbtack className="text-primary text-sm" />}
        <div className="grow text-start truncate">{subregion.name}</div>
      </div>
    </button>
  );
}
