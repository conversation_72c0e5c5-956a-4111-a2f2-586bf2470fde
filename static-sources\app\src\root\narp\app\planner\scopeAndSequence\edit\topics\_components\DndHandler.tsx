import {
  CollisionDescriptor,
  CollisionDetection,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  MouseSensor,
  rectIntersection,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { ReactNode } from "react";
import {
  Activity,
  Goal,
  Term,
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import Overlay from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/Overlay.tsx";

export type DragData =
  | {
      type: "TOPIC";
      index: number;
      topic: Topic;
    }
  | {
      type: "GOAL";
      goal: Goal;
    }
  | {
      type: "ACTIVITY";
      activity: Activity;
    }
  | {
      type: "TERM";
      term: Term;
    };

export type DropData =
  | {
      type: "TOPIC";
      index: number;
    }
  | {
      type: "GOAL";
      topic: Topic;
    }
  | {
      type: "ACTIVITY";
      topic: Topic;
    }
  | {
      type: "TERM";
      topic: Topic;
    };

const collisionAlgorithm: CollisionDetection = (args) => {
  let collisions = rectIntersection(args) as CollisionDescriptor[];

  const activeData = args.active.data.current as DragData | undefined;
  if (!activeData) return [];

  if (activeData.type === "TOPIC") {
    collisions = collisions.filter((collision) => {
      const dropData = collision.data.droppableContainer.data.current as
        | DropData
        | undefined;

      if (!dropData || dropData.type !== "TOPIC") return false;
      return (
        dropData.index !== activeData.index &&
        dropData.index !== activeData.index + 1
      );
    });
  } else if (activeData.type === "GOAL") {
    collisions = collisions.filter((collision) => {
      const dropData = collision.data.droppableContainer.data.current as
        | DropData
        | undefined;

      if (!dropData || dropData.type !== "GOAL") return false;

      return dropData.topic.id !== activeData.goal.parentTopic;
    });
  } else if (activeData.type === "ACTIVITY") {
    collisions = collisions.filter((collision) => {
      const dropData = collision.data.droppableContainer.data.current as
        | DropData
        | undefined;

      if (!dropData || dropData.type !== "ACTIVITY") return false;

      return dropData.topic.id !== activeData.activity.parentTopic;
    });
  } else if (activeData.type === "TERM") {
    collisions = collisions.filter((collision) => {
      const dropData = collision.data.droppableContainer.data.current as
        | DropData
        | undefined;

      if (!dropData || dropData.type !== "TERM") return false;

      return dropData.topic.id !== activeData.term.parentTopic;
    });
  }

  return collisions.filter(
    (collision) =>
      collision.data.droppableContainer.data.current?.type === activeData.type,
  );
};

export default function DndHandler({ children }: { children: ReactNode }) {
  const {
    topic: { move },
    term: { setParentTopic: setTermParent },
    goal: { setParentTopic: setGoalParent },
    activity: { setParentTopic: setActivityParent },
  } = useEditorContext();

  function handleDragEnd(e: DragEndEvent) {
    document.body.style.cursor = "auto";

    const activeData = e.active.data.current as DragData | undefined;
    const overData = e.over?.data?.current as DropData | undefined;
    if (!activeData || !overData) return;

    if (activeData.type === "TOPIC" && overData.type === "TOPIC") {
      if (overData.index > activeData.index) {
        move(activeData.index, overData.index - 1);
      } else {
        move(activeData.index, overData.index);
      }
    } else if (activeData.type === "ACTIVITY" && overData.type === "ACTIVITY") {
      setActivityParent(activeData.activity.id, overData.topic.id);
    } else if (activeData.type === "TERM" && overData.type === "TERM") {
      setTermParent(activeData.term.id, overData.topic.id);
    } else if (activeData.type === "GOAL" && overData.type === "GOAL") {
      setGoalParent(activeData.goal.id, overData.topic.id);
    }
  }

  function handleDragStart() {
    document.body.style.cursor = "grabbing";
  }

  return (
    <DndContext
      collisionDetection={collisionAlgorithm}
      sensors={useSensors(
        useSensor(MouseSensor),
        useSensor(TouchSensor),
        useSensor(KeyboardSensor),
      )}
      onDragEnd={handleDragEnd}
      onDragStart={handleDragStart}
    >
      {children}
      <Overlay />
    </DndContext>
  );
}
