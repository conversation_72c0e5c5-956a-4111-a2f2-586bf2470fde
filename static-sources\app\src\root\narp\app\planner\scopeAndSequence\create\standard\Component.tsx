import {
  ChangeEvent,
  FormEvent,
  MouseEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  Standard,
  useCreateScopeSequenceContext,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import { Navigate, useNavigate } from "react-router-dom";
import useGET from "@/util/api/useGET.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { FaChevronUp } from "react-icons/fa6";
import { FaSearch } from "react-icons/fa";
import Fuse from "fuse.js";

export function Component() {
  const topRealm = useTopRealm();
  const GET = useGET();
  const { authority } = useCreateScopeSequenceContext();
  const pushError = usePushError();

  const [rootStandards, setRootStandards] = useState<Standard[] | null>(null);

  const updateRootStandards = useCallback(async () => {
    if (!authority) return;

    const { response, error, data } = await GET(
      `/v1/standards/${authority.eidURN}`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
      }
    );

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    const envelope: APIEnvelope<Standard[]> = JSON.parse(await data.text());
    setRootStandards(envelope.data);
  }, [authority, GET, pushError, topRealm.eidURN]);

  useEffect(() => {
    updateRootStandards();
  }, []);

  if (!authority) return <Navigate to={"../authority"} />;

  if (!rootStandards)
    return (
      <div className="grow flex items-center justify-center">
        <div className="loading loading-spinner loading-xl" />
      </div>
    );

  return <Content rootStandards={rootStandards} />;
}

function Content({ rootStandards }: { rootStandards: Standard[] }) {
  const { standard } = useCreateScopeSequenceContext();
  const navigate = useNavigate();

  const [continuable, setContinuable] = useState<boolean>(false);
  const [searchString, setSearchString] = useState<string>("");

  // Function to flatten the hierarchical standards for searching
  const flattenStandards = useCallback((standards: Standard[]): Standard[] => {
    const flattened: Standard[] = [];

    function traverse(standardList: Standard[]) {
      for (const standard of standardList) {
        flattened.push(standard);
        if (standard.children) {
          traverse(standard.children);
        }
      }
    }

    traverse(standards);
    return flattened;
  }, []);

  // Get all standards (including cached children) for searching
  const allStandards = useMemo(() => {
    const allCachedStandards = [...rootStandards];

    // Add all cached children from the response cache
    for (const cachedChildren of responseCache.values()) {
      allCachedStandards.push(...cachedChildren);
    }

    return flattenStandards(allCachedStandards);
  }, [rootStandards, flattenStandards]);

  // Filter standards based on search
  const searchEntries = useMemo(() => {
    if (!searchString || searchString.trim() === "") {
      return null; // Return null to show original hierarchy
    }

    const fuse = new Fuse(allStandards, {
      keys: [
        "sectionName",
        "levelTitle",
        "formattedSectionNumber",
        "text.en_US",
      ],
      threshold: 0.3, // More lenient matching
      includeScore: true,
    });

    return fuse.search(searchString).map((result) => result.item);
  }, [allStandards, searchString]);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  useEffect(() => {
    setContinuable(!!standard);
  }, [standard]);

  function handleToNext(e: MouseEvent) {
    e.preventDefault();
    navigate("../customize");
  }

  function handleToPrevious(e: MouseEvent) {
    e.preventDefault();
    navigate("../authority");
  }

  return (
    <>
      <div className="flex flex-row justify-between px-5">
        <div className="flex flex-col gap-1">
          <div className="text-xl font-bold">Choose a Standard</div>
          <div>Choose the standard you want to base your plan off of.</div>
        </div>

        <label className="input input-lg">
          <FaSearch className="text-neutral-300 shrink-0" />
          <input
            onInput={handleInput}
            type="text"
            placeholder="Search standards..."
            value={searchString}
            onChange={(e) => setSearchString(e.target.value)}
          />
        </label>
      </div>

      <div className="flex flex-col gap-3 px-5 mb-auto">
        {searchEntries ? (
          // Show filtered search results
          searchEntries.length > 0 ? (
            <>
              <div className="text-sm text-neutral-600 mb-2">
                Found {searchEntries.length} standard
                {searchEntries.length !== 1 ? "s" : ""}
              </div>
              {searchEntries.map((entry, index) => (
                <StandardEntry
                  standard={entry}
                  key={`search-${entry.urn}-${index}`}
                  depth={0}
                  isSearchResult={true}
                />
              ))}
            </>
          ) : (
            <div className="text-center text-neutral-500 py-8">
              No standards found matching "{searchString}"
            </div>
          )
        ) : (
          // Show original hierarchy
          rootStandards.map((entry, index) => (
            <StandardEntry standard={entry} key={index} depth={0} />
          ))
        )}
      </div>

      <div className="bg-base-100 shadow-lg shadow-black border-t border-t-base-content/20 p-5 gap-3 flex flex-row justify-end">
        <button
          onClick={handleToPrevious}
          type="button"
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Back
        </button>

        <button
          onClick={handleToNext}
          type="button"
          disabled={!continuable}
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </>
  );
}

const responseCache: Map<string, Standard[]> = new Map();

function StandardEntry({
  standard,
  depth,
  isSearchResult = false,
}: {
  standard: Standard;
  depth: number;
  isSearchResult?: boolean;
}) {
  const topRealm = useTopRealm();
  const GET = useGET();
  const {
    authority,
    standard: currentStandard,
    setStandard,
  } = useCreateScopeSequenceContext();
  const pushError = usePushError();

  const [loading, setLoading] = useState<boolean>(false);
  const [children, setChildren] = useState<Standard[] | null>(null);

  const updateChildren = useCallback(async () => {
    if (responseCache.get(standard.urn))
      return setChildren(responseCache.get(standard.urn)!);

    setLoading(true);
    const { response, error, data } = await GET(
      `/v1/standards/${authority!.eidURN}/${standard.urn}?expansionDepth=${1}`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
      }
    );
    setLoading(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    const envelope: APIEnvelope<Standard[]> = JSON.parse(await data.text());
    const children = envelope.data[0]?.children || [];
    responseCache.set(standard.urn, children);
    setChildren(children);
  }, [standard, GET, authority, pushError, topRealm.eidURN]);

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    if (loading) return; // Don't allow clicks while loading

    if (isSearchResult) {
      // For search results, just select the standard
      setStandard(standard);
    } else if (isRule) {
      // For rules, just select them instead of trying to expand
      setStandard(standard);
    } else {
      // For hierarchy view, handle expansion for non-rule items
      if (!children) {
        updateChildren();
      } else {
        setChildren(null);
      }
    }
  }

  const isRule = standard.levelTitle == "Rule §";

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    e.preventDefault();
    setStandard(standard);
  }

  return (
    <>
      <div className={`${standard.text?.en_US !== undefined && "tooltip"}`}>
        {standard.text?.en_US !== undefined && (
          <div className="tooltip-content text-start">
            {standard.text?.en_US || "No Content"}
          </div>
        )}
        <div
          className={`flex flex-row items-center gap-1 p-1 px-2 border border-neutral-400 rounded-lg transition-colors ${
            loading
              ? "cursor-wait opacity-70"
              : "cursor-pointer hover:bg-base-200"
          } ${isSearchResult ? "bg-blue-50 border-blue-300" : ""} ${
            standard.urn === currentStandard?.urn
              ? "bg-primary-100 border-primary-500"
              : ""
          }`}
          onClick={handleClick}
        >
          {(isRule || isSearchResult) && (
            <div className="tooltip" data-tip={"Select this standard"}>
              <input
                name="standard"
                type="radio"
                className="radio radio-primary"
                value={standard.urn}
                checked={standard.urn === currentStandard?.urn}
                onChange={handleChange}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          )}

          {!isSearchResult &&
            new Array(depth + (isRule ? -1 : 0))
              .fill(null)
              .map((_, i) => <div className="w-4" key={i} />)}

          {!isSearchResult && (
            <div className="btn btn-sm btn-square btn-ghost pointer-events-none">
              <FaChevronUp
                className={`transition-transform ${children ? "rotate-180" : "rotate-0"}`}
              />
            </div>
          )}

          <span className="font-bold">{standard.levelTitle}</span>
          <span>[{standard.formattedSectionNumber}]</span>
          <span>{standard.sectionName}</span>

          {isSearchResult && (
            <span className="ml-auto text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
              Search Result
            </span>
          )}
        </div>
      </div>

      {!isSearchResult &&
        (loading ? (
          <div className="mx-auto my-5 loading loading-spinner" />
        ) : (
          children &&
          children.map((child, index) => (
            <StandardEntry standard={child} key={index} depth={depth + 1} />
          ))
        ))}
    </>
  );
}
