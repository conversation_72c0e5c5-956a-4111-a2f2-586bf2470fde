import { Outlet } from "react-router-dom";
import SideMenu from "./_components/SideMenu";
import { LORProvider, useLORContext } from "./_components/LORContext";
import LORSelector from "./_components/LORSelector";
import LoadingOverlay from "./_components/LoadingOverlay";

function LORContent() {
  const { isSwitching } = useLORContext();

  return (
    <>
      <div className="w-full h-full flex flex-col overflow-hidden">
        {/* Top bar with LOR selector */}
        <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-3 relative">
          {/* Left side - LOR Selector */}
          <div className="flex items-center">
            <LORSelector />
          </div>

          {/* Absolutely positioned center title */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <h1 className="text-lg font-semibold text-gray-900">
              Learning Object Repository
            </h1>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-row overflow-hidden">
          <SideMenu />
          <div className="grow basis-0 overflow-hidden">
            <Outlet />
          </div>
        </div>
      </div>

      {/* Loading overlay during LOR switching */}
      <LoadingOverlay isVisible={isSwitching} />
    </>
  );
}

export function Component() {
  return (
    <LORProvider>
      <LORContent />
    </LORProvider>
  );
}
