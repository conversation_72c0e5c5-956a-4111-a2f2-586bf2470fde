import { Mouse<PERSON><PERSON>, useEffect, useRef, useState } from "react";
import { HttpMethod, useAPIRequest } from "@/util/API.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { Editor } from "@monaco-editor/react";
import { extensionUtil } from "@/root/narp/app/asset-manager/_components/details/extensionUtil.ts";
import Group from "@/root/narp/app/asset-manager/_components/details/Group.tsx";
import Entry from "@/root/narp/app/asset-manager/_components/details/Entry.tsx";
import { FaSave } from "react-icons/fa";
import { FaTrash } from "react-icons/fa6";
import { useNavigate, useParams } from "react-router-dom";
import { FileNode } from "@/root/narp/app/asset-manager/_components/types.ts";
import path from "path-browserify";

export default function FileDetails({ node }: { node: FileNode }) {
  return (
    <div className="min-h-full min-w-full flex flex-col gap-3">
      <Group label="Metadata">
        <Entry
          label="Name"
          value={`${node.userBasename}.${node.userExtension}`}
          copyable
        />
        <Entry label="Version" value={node.version.toString()} />
        <Entry label="Container Path" value={node.containerPath} copyable />
        <Entry
          label="Path"
          value={`${node.userPath}/${node.userBasename}.${node.userExtension}`}
          copyable
        />
        <Entry label="EID" value={node.userEID} copyable />
        <Entry label="Realm EID" value={node.realmEID} copyable />
        <Entry label="Physical Size" value={`${node.physicalSizeInBytes} B`} />
        <Entry
          label="Created At"
          value={new Date(node.createdTimestamp).toLocaleString()}
        />
        <Entry
          label="Updated At"
          value={new Date(node.updatedTimestamp).toLocaleString()}
        />
      </Group>

      <Group label="Raw">
        <pre>{JSON.stringify(node, null, 2)}</pre>
      </Group>

      <Content node={node} />

      <Group label="Danger">
        <DeleteField node={node} />
      </Group>
    </div>
  );
}

function Content({ node }: { node: FileNode }) {
  const { realmEIDURN, zone } = useParams();
  const realmEID = realmEIDURN?.replace("urn:ayode:realm-eid:", "");
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const request = useAPIRequest();

  const [content, setContent] = useState<string | null>(null);

  async function updateContent() {
    if (!realmEIDURN || !realmEID || !zone)
      return pushError("missing asset parameters");

    const [response, error] = await request(
      `/v1/assets/${realmEID}/${zone}${node.userPath}/${node.userBasename}.${node.userExtension}@${node.version}`,
      HttpMethod.GET,
      {
        assertedRealm: topRealm,
        params: [{ name: "encoding", value: "base64" }],
      },
    );

    if (!response) return pushError("missing auth token");
    if (error) return pushError(error.clientErrorDetail.userMessage);

    setContent(atob(await response.text()));
  }

  useEffect(() => {
    updateContent();
  }, []);

  if (content === null) {
    return (
      <div>
        <div className="font-bold text-xl">Content</div>
        <div className="h-32 flex items-center justify-center p-5">
          <div className="loading loading-spinner loading-lg" />
        </div>
      </div>
    );
  }

  function handleChange(value: string | undefined) {
    setContent(value || "");
  }

  return (
    <div className="flex flex-col gap-1">
      <div className="flex flex-row items-center gap-1">
        <div className="font-bold text-xl me-auto">Content</div>
        <SaveButton node={node} value={content} />
      </div>
      <div className="h-96 border border-neutral-300 rounded-lg overflow-hidden">
        <Editor
          path={`/${realmEID}/${zone}${node.userPath}/${node.userBasename}.${node.userExtension}`}
          value={content}
          language={extensionUtil.get(node.userExtension)}
          onChange={handleChange}
        />
      </div>
    </div>
  );
}

function SaveButton({ node, value }: { node: FileNode; value: string }) {
  const { realmEIDURN, zone } = useParams();
  const realmEID = realmEIDURN?.replace("urn:ayode:realm-eid:", "");
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const request = useAPIRequest();
  const [loading, setLoading] = useState<boolean>(false);

  const navigate = useNavigate();

  async function handleClick(e: MouseEvent) {
    e.preventDefault();
    if (!realmEIDURN || !realmEID || !zone)
      return pushError("missing asset parameters");

    setLoading(true);
    const [response, error] = await request(
      `/v1/assets/${realmEID}/${zone}${node.userPath}/${node.userBasename}.${node.userExtension}`,
      HttpMethod.POST,
      {
        assertedRealm: topRealm,
        body: value,
        headers: { ["Content-Type"]: "application/octet-stream" },
      },
    );
    setLoading(false);

    if (!response) return pushError("missing auth token");
    if (error) return pushError(error.clientErrorDetail.userMessage);

    navigate(
      `/narp/app/asset-manager/${realmEIDURN}/${zone}${node.userPath}/${node.userBasename}.${node.userExtension}`,
    );
  }

  return (
    <div className="tooltip" data-tip="Save">
      <button
        onClick={handleClick}
        className="btn btn-square btn-sm btn-outline"
      >
        {loading ? <div className="loading loading-sm" /> : <FaSave />}
      </button>
    </div>
  );
}

function DeleteField({ node }: { node: FileNode }) {
  const navigate = useNavigate();
  const { realmEIDURN, zone } = useParams();
  const realmEID = realmEIDURN?.replace("urn:ayode:realm-eid:", "");
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const request = useAPIRequest();

  const inputRef = useRef<HTMLInputElement>(null);
  const [invalid, setInvalid] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  async function handleClick(e: MouseEvent) {
    e.preventDefault();
    if (loading) return;
    if (!realmEIDURN || !realmEID || !zone)
      return pushError("missing asset parameters");

    setInvalid(false);
    if (
      inputRef.current?.value !== `${node.userBasename}.${node.userExtension}`
    )
      return setInvalid(true);

    setLoading(true);
    const [response, error] = await request(
      `/v1/assets/${realmEID}/${zone}${node.userPath}/${node.userBasename}.${node.userExtension}`,
      HttpMethod.DELETE,
      { assertedRealm: topRealm },
    );
    setLoading(false);

    if (!response) return pushError("missing auth token");
    if (error) return pushError(error.clientErrorDetail.userMessage);

    navigate(
      path.join(
        `/narp/app/asset-manager/${realmEIDURN}/${zone}`,
        node.containerPath,
        "/",
      ),
    );
  }

  return (
    <div className="flex flex-row gap-3">
      <label className="grow">
        <input
          ref={inputRef}
          placeholder="Input full name of file to delete"
          className={`input w-full ${invalid && "input-error"}`}
        />
        {invalid && (
          <p className="validator-hint text-error">
            File full name does not match
          </p>
        )}
      </label>

      <button
        disabled={loading}
        onClick={handleClick}
        className="btn btn-outline btn-error"
      >
        {loading ? (
          <div className="loading loading-spinner" />
        ) : (
          <>
            <FaTrash /> Delete File
          </>
        )}
      </button>
    </div>
  );
}
