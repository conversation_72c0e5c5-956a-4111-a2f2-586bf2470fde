import { RouteObject } from "react-router-dom";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";
import { default as RegionRoute } from "./region/Route";
import { default as SubRegionRoute } from "@/root/narp/app/planner/scopeAndSequence/create/subregion/Route";
import { default as AuthorityRoute } from "./authority/Route";
import { default as StandardRoute } from "./standard/Route";
import { default as CustomizeRoute } from "./customize/Route";
import { default as GenerateRoute } from "./generate/Route";

const Route: RouteObject = {
  path: "create",
  hydrateFallbackElement: <PageSkeleton />,
  lazy: () => import("./Component.tsx"),
  children: [
    RegionRoute,
    SubRegionRoute,
    AuthorityRoute,
    StandardRoute,
    CustomizeRoute,
    GenerateRoute,
  ],
};

export default Route;
