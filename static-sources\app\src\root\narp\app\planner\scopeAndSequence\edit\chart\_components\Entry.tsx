import { FaPlus } from "react-icons/fa6";
import { FaEdit } from "react-icons/fa";
import {
  DragData,
  DropData,
  useChartContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";
import {
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import {
  DragMoveEvent,
  useDndMonitor,
  useDraggable,
  useDroppable,
} from "@dnd-kit/core";
import { useState, useRef, FormEvent, KeyboardEvent } from "react";
import ColorHash from "color-hash";

const colorHash = new ColorHash();

export default function Entry({ topic }: { topic: Topic }) {
  const {
    topic: { get },
  } = useEditorContext();
  const { timestampToPixels, pixelsToTimestamp } = useChartContext();

  const data: DropData = {
    topic: topic,
    type: "SET_DEPENDENCY",
  };

  const { isOver, setNodeRef } = useDroppable({ id: topic.id, data: data });
  const [durationDelta, setDurationDelta] = useState<number>(0);
  const [startDelta, setStartDelta] = useState<number>(0);

  // Add whole-box dragging capability
  const wholeBoxData: DragData = {
    topic: topic,
    type: "SET_START",
  };

  const {
    attributes: wholeBoxAttributes,
    listeners: wholeBoxListeners,
    setNodeRef: setWholeBoxRef,
    isDragging,
  } = useDraggable({
    id: topic.id + "-wholebox",
    data: wholeBoxData,
  });

  useDndMonitor({
    onDragMove(event: DragMoveEvent) {
      const activeData = event.active.data?.current as DragData | undefined;

      if (!activeData || activeData.topic.id !== topic.id) return;

      if (activeData.type === "SET_DEPENDENCY") {
        return;
      } else if (activeData.type === "SET_START") {
        setStartDelta(Math.round(pixelsToTimestamp(event.delta.x)));
      } else if (activeData.type === "SET_DURATION") {
        setDurationDelta(Math.round(pixelsToTimestamp(event.delta.x)));
      }
    },
    onDragEnd() {
      setDurationDelta(0);
      setStartDelta(0);
    },
  });

  let minStartTime = 0;
  for (const dependencyID of topic.dependencies) {
    const dependency = get(dependencyID);
    if (!dependency) continue;
    if (minStartTime < dependency.start + dependency.duration)
      minStartTime = dependency.start + dependency.duration;
  }

  // Validate inputs to prevent NaN values
  const validTopicStart = isFinite(topic.start) ? topic.start : 0;
  const validTopicDuration = isFinite(topic.duration) ? topic.duration : 1;
  const validStartDelta = isFinite(startDelta) ? startDelta : 0;
  const validDurationDelta = isFinite(durationDelta) ? durationDelta : 0;

  const startOffset = timestampToPixels(
    Math.max(minStartTime, validTopicStart + validStartDelta)
  );
  const durationOffset = timestampToPixels(
    Math.max(1, validTopicDuration + validDurationDelta)
  );

  return (
    <div className="w-full border-e-2 border-e-neutral-200">
      <div
        style={{
          marginLeft: timestampToPixels(validTopicStart) + "px",
          width: timestampToPixels(validTopicDuration) + "px",
        }}
        className="relative h-16 box-content rounded-lg outline-2 -outline-offset-2 outline-neutral-400 outline-dashed"
      >
        <div
          title={`${topic.name} - Click to rename, drag to move`}
          id={`topic-element-${topic.id}`}
          style={{
            borderColor: colorHash.hex(topic.name),
            left: startOffset - timestampToPixels(topic.start) + "px",
            width: durationOffset + "px",
          }}
          className={`absolute top-0 left-0 h-16 flex flex-row items-stretch border-2 border-neutral-400 bg-white shadow-xl rounded-lg
        outline-2 outline-offset-2 ${isOver ? "outline-neutral-400" : "outline-transparent"}
        ${isDragging ? "opacity-50" : ""}
        `}
          ref={(node) => {
            setNodeRef(node);
            setWholeBoxRef(node);
          }}
          {...wholeBoxAttributes}
          {...wholeBoxListeners}
        >
          <StartHandle topic={topic} />
          <TopicNameField topic={topic} />
          <DurationHandle topic={topic} />
          <DependencyHandle topic={topic} />
        </div>
      </div>
    </div>
  );
}

function TopicNameField({ topic }: { topic: Topic }) {
  const {
    topic: { setName },
  } = useEditorContext();
  const [isEditing, setIsEditing] = useState(false);
  const [displayValue, setDisplayValue] = useState(topic.name);
  const inputRef = useRef<HTMLInputElement>(null);

  function handleDoubleClick(e: React.MouseEvent) {
    e.stopPropagation(); // Prevent drag from starting
    setIsEditing(true);
    setDisplayValue(topic.name);
    setTimeout(() => inputRef.current?.focus(), 0);
  }

  function handleInput(e: FormEvent<HTMLInputElement>) {
    setDisplayValue(e.currentTarget.value);
  }

  function handleKeyDown(e: KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter") {
      e.preventDefault();
      saveName();
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEdit();
    }
  }

  function handleBlur() {
    saveName();
  }

  function saveName() {
    if (displayValue.trim() && displayValue !== topic.name) {
      setName(topic.id, displayValue.trim());
    }
    setIsEditing(false);
  }

  function cancelEdit() {
    setDisplayValue(topic.name);
    setIsEditing(false);
  }

  if (isEditing) {
    return (
      <div
        className="flex items-center justify-start grow px-2"
        onClick={(e) => e.stopPropagation()}
      >
        <input
          ref={inputRef}
          value={displayValue}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          className="w-full bg-transparent border-none outline-none text-lg font-medium"
          placeholder="Topic name"
        />
      </div>
    );
  }

  return (
    <div
      className="flex items-center justify-start truncate grow text-lg cursor-pointer px-2 hover:bg-gray-50 transition-colors group"
      onDoubleClick={handleDoubleClick}
    >
      <span>{topic.name}</span>
      <FaEdit className="ml-2 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity text-sm" />
    </div>
  );
}

function StartHandle({ topic }: { topic: Topic }) {
  const data: DragData = {
    topic: topic,
    type: "SET_START",
  };

  const { attributes, listeners, setNodeRef } = useDraggable({
    id: topic.id + "-start",
    data: data,
  });

  return (
    <button
      type="button"
      {...attributes}
      {...listeners}
      ref={setNodeRef}
      title="Drag to change start time"
      className="shrink-0 cursor-move w-3 hover:bg-blue-100 transition-colors"
      onClick={(e) => e.stopPropagation()}
    />
  );
}

function DurationHandle({ topic }: { topic: Topic }) {
  const data: DragData = {
    topic: topic,
    type: "SET_DURATION",
  };

  const { attributes, listeners, setNodeRef } = useDraggable({
    id: topic.id + "-duration",
    data: data,
  });

  return (
    <button
      type="button"
      {...attributes}
      {...listeners}
      ref={setNodeRef}
      title="Drag to change duration"
      className="shrink-0 cursor-ew-resize w-3 hover:bg-green-100 transition-colors"
      onClick={(e) => e.stopPropagation()}
    />
  );
}

function DependencyHandle({ topic }: { topic: Topic }) {
  const data: DragData = {
    topic: topic,
    type: "SET_DEPENDENCY",
  };

  const { attributes, listeners, setNodeRef } = useDraggable({
    id: topic.id + "-dependency",
    data: data,
  });

  return (
    <button
      type="button"
      {...attributes}
      {...listeners}
      ref={setNodeRef}
      title="Drag to create dependency connections"
      className="absolute left-1/2 top-full -translate-x-1/2 z-10 flex items-center justify-center size-6 border-2 border-blue-300 rounded-full bg-blue-50 cursor-grab text-blue-300 hover:bg-blue-100 hover:border-blue-400 transition-colors"
      onClick={(e) => e.stopPropagation()}
    >
      <FaPlus />
    </button>
  );
}
