import { useConfiguration } from "@/util/configuration/ConfigurationContext.tsx";
import { useAuth } from "@/util/auth/AuthContext.tsx";
import { Realm } from "@/root/narp/app/_components/Context";

export enum HttpMethod {
  POST = "POST",
  GET = "GET",
  PUT = "PUT",
  HEAD = "HEAD",
  DELETE = "DELETE",
  CONNECT = "CONNECT",
  OPTIONS = "OPTIONS",
  TRACE = "TRACE",
  PATCH = "PATCH",
}

export interface APIErrorFrame {
  errorType: {
    code: number;
    name: string;
  };
  errorPointID: string;
  subsystem: string;
  functionName: string;
  executionRegion: string;
  userMessage: string;
}

export interface APIError {
  request: {
    requestID: string;
    uriPath: string;
    httpMethod: HttpMethod;
  };
  timestamp: string;
  clientErrorDetail: {
    username: string;
    sub: string;
    errorType: {
      code: number;
      name: string;
    };
    errorPointID: string;
    subsystem: string;
    functionName: string;
    executionRegion: string;
    userMessage: string;
    embeddedFrames: APIErrorFrame[];
  };
}

export interface RequestOptions {
  body?: BodyInit;
  assertedRealm?: Realm;
  headers?: HeadersInit;
  params?: { name: string; value: string }[];
}

export function useAPIRequest(): (
  url: string,
  method: HttpMethod,
  options?: RequestOptions,
) => Promise<[Response | null, APIError | null]> {
  const configuration = useConfiguration();
  const { getSession } = useAuth();

  if (!configuration || !getSession)
    throw new Error(
      "useRequest can only be used under a configuration and auth context!",
    );

  return async (
    url: string,
    method: HttpMethod,
    options?: RequestOptions,
  ): Promise<[Response | null, APIError | null]> => {
    const [session, error] = await getSession();
    if (error || !session)
      return [
        null,
        {
          request: {
            requestID: "",
            uriPath: "",
            httpMethod: method,
          },
          timestamp: new Date().toISOString(),
          clientErrorDetail: {
            username: "",
            sub: "",
            errorType: {
              code: 401000,
              name: "Client_MissingAuthToken",
            },
            errorPointID: "",
            subsystem: "client",
            functionName: "",
            executionRegion: "",
            userMessage: "Failed to fetch auth token",
            embeddedFrames: [],
          },
        },
      ];

    const apiUrl = new URL(url, configuration.apiBaseURL);
    if (options?.params)
      options.params.forEach((entry) => {
        apiUrl.searchParams.append(entry.name, entry.value);
      });

    const apiHeaders = new Headers(options?.headers);
    apiHeaders.append(
      "Authorization",
      `Bearer ${session.getAccessToken().getJwtToken()}`,
    );
    if (options?.assertedRealm)
      apiHeaders.append(
        "X-Ayode-Asserted-Realm-EID",
        options.assertedRealm.eidURN,
      );

    const response = await fetch(apiUrl, {
      method: method,
      headers: apiHeaders,
      body: options?.body,
    });

    if (!response.ok) {
      return [response, await response.json()];
    } else {
      return [response, null];
    }
  };
}
