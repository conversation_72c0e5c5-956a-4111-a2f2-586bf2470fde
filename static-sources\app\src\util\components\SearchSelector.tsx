import { <PERSON><PERSON><PERSON>, MouseE<PERSON>, useMemo, useRef, useState } from "react";
import Fuse from "fuse.js";

export interface Descriptor<T> {
  label: string;
  data: T;
}

export default function SearchSelector<T>({
  descriptors,
  onSelect,
}: {
  descriptors: Descriptor<T>[];
  onSelect: (selected: T) => void;
}) {
  const [searchString, setSearchString] = useState<string>("");

  const entries: Descriptor<T>[] = useMemo(() => {
    if (!searchString || searchString === "") return descriptors;

    const fuse = new Fuse<Descriptor<T>>(descriptors, {
      ignoreLocation: true,
      keys: ["label"],
    });
    return fuse.search(searchString).map((result) => result.item);
  }, [searchString, descriptors]);

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  return (
    <div className="top-full hidden group-focus-within:flex bg-white border border-neutral-400 shadow z-10 absolute bottom-full w-full h-96 flex-col rounded-lg">
      <div className="border-b border-b-neutral-400 p-1">
        <input
          placeholder="Search"
          tabIndex={0}
          onChange={handleChange}
          type="text"
          autoComplete="off"
          className="input input-xs shrink-0 w-full"
        />
      </div>

      <div className="p-1 flex flex-col gap-1 overflow-y-auto">
        {entries.map((entry, index) => (
          <SelectorEntry descriptor={entry} onSelect={onSelect} key={index} />
        ))}
      </div>
    </div>
  );
}

function SelectorEntry<T>({
  descriptor,
  onSelect,
}: {
  descriptor: Descriptor<T>;
  onSelect: (selected: T) => void;
}) {
  const buttonRef = useRef<HTMLButtonElement>(null);

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    onSelect(descriptor.data);
    buttonRef.current?.blur();
  }

  return (
    <button
      tabIndex={0}
      ref={buttonRef}
      onClick={handleClick}
      type="button"
      className="btn btn-xs px-3 overflow-hidden justify-start"
    >
      <span className="truncate">{descriptor.label}</span>
    </button>
  );
}
