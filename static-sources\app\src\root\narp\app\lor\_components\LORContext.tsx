import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from "react";
import { useRealmContext } from "@/root/narp/app/_components/Context";
import { useAPIRequest, HttpMethod } from "@/util/API";
import { usePushError } from "@/util/components/Alerts/util";

export interface LORLevel {
  id: string;
  name: string;
  description: string;
  type: "personal" | "classroom" | "school" | "district" | "state" | "national";
  realmEID: string;
  realmURN: string;
  icon?: string;
  level: number; // hierarchy level (1 = personal, 6 = national)
}

export interface LORContext {
  availableLORLevels: LORLevel[];
  currentLORLevel: LORLevel | null;
  isLoading: boolean;
  isSwitching: boolean;
  switchLORLevel: (level: LORLevel) => Promise<void>;
  refreshLORLevels: () => Promise<void>;
}

const Context = createContext<LORContext>({} as LORContext);

export function useLORContext() {
  return useContext(Context);
}

export function LORProvider({ children }: { children: ReactNode }) {
  const { currentTopRealm, fetchRealmDetails } = useRealmContext();
  const request = useAPIRequest();
  const pushError = usePushError();

  const [availableLORLevels, setAvailableLORLevels] = useState<LORLevel[]>([]);
  const [currentLORLevel, setCurrentLORLevel] = useState<LORLevel | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSwitching, setIsSwitching] = useState(false);

  // Mock data based on screenshots - in real implementation, this would come from API
  const mockLORLevels: LORLevel[] = [
    {
      id: "personal",
      name: "My Personal LOR",
      description: "Your private learning object repository",
      type: "personal",
      realmEID: currentTopRealm?.externalID || "",
      realmURN: currentTopRealm?.eidURN || "",
      level: 1,
    },
    {
      id: "classroom",
      name: "Ms. Johnson's 3rd Grade",
      description: "Classroom-level repository for Grade 3",
      type: "classroom",
      realmEID: currentTopRealm?.externalID || "",
      realmURN: currentTopRealm?.eidURN || "",
      level: 2,
    },
    {
      id: "school",
      name: "Allen High School LOR",
      description: "School-wide learning object repository",
      type: "school",
      realmEID: currentTopRealm?.externalID || "",
      realmURN: currentTopRealm?.eidURN || "",
      level: 3,
    },
    {
      id: "district",
      name: "Allen District LOR",
      description: "District-level repository serving multiple schools",
      type: "district",
      realmEID: currentTopRealm?.externalID || "",
      realmURN: currentTopRealm?.eidURN || "",
      level: 4,
    },
    {
      id: "state",
      name: "Texas State LOR",
      description: "State-wide educational resource repository",
      type: "state",
      realmEID: currentTopRealm?.externalID || "",
      realmURN: currentTopRealm?.eidURN || "",
      level: 5,
    },
    {
      id: "national",
      name: "National Education LOR",
      description: "National repository of education resources",
      type: "national",
      realmEID: currentTopRealm?.externalID || "",
      realmURN: currentTopRealm?.eidURN || "",
      level: 6,
    },
  ];

  async function fetchLORLevels() {
    setIsLoading(true);
    try {
      // API call to fetch available LOR levels
      // Expected endpoint: GET /v1/lor/levels/{realmEID}
      // Expected response: { levels: LORLevel[] }
      const [response, error] = await request(
        `/v1/lor/levels/${currentTopRealm.externalID}`,
        HttpMethod.GET,
        { assertedRealm: currentTopRealm }
      );

      if (error) {
        console.warn("API not implemented yet, using mock data:", error);
        // Fallback to mock data when API is not ready
        setAvailableLORLevels(mockLORLevels);
        if (!currentLORLevel && mockLORLevels.length > 0) {
          setCurrentLORLevel(mockLORLevels[0]);
        }
        return;
      }

      if (!response || !response.ok) {
        throw new Error("Failed to fetch LOR levels from API");
      }

      const data = await response.json();
      setAvailableLORLevels(data.levels || []);

      // Set default LOR level if none is currently selected
      if (!currentLORLevel && data.levels && data.levels.length > 0) {
        setCurrentLORLevel(data.levels[0]);
      }
    } catch (error) {
      console.warn("API error, falling back to mock data:", error);
      // Fallback to mock data on error
      setAvailableLORLevels(mockLORLevels);
      if (!currentLORLevel && mockLORLevels.length > 0) {
        setCurrentLORLevel(mockLORLevels[0]);
      }
    } finally {
      setIsLoading(false);
    }
  }

  async function switchLORLevel(level: LORLevel) {
    if (currentLORLevel?.id === level.id) return;

    setIsSwitching(true);
    try {
      // API call to switch LOR level
      // Expected endpoint: POST /v1/lor/switch
      // Expected body: { targetLORLevel: string, realmEID: string }
      // Expected response: { success: boolean, currentLevel: LORLevel }
      const [response, error] = await request(
        `/v1/lor/switch`,
        HttpMethod.POST,
        {
          assertedRealm: currentTopRealm,
          body: JSON.stringify({
            targetLORLevel: level.id,
            realmEID: currentTopRealm.externalID,
          }),
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (error) {
        console.warn(
          "LOR switch API not implemented yet, using mock behavior:",
          error
        );
        // Simulate API delay for mock behavior
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setCurrentLORLevel(level);
        return;
      }

      if (!response || !response.ok) {
        throw new Error("Failed to switch LOR level");
      }

      const data = await response.json();
      if (data.success) {
        setCurrentLORLevel(data.currentLevel || level);
      } else {
        throw new Error("LOR level switch was not successful");
      }
    } catch (error) {
      console.warn("API error during LOR switch, using mock behavior:", error);
      // Fallback behavior: simulate the switch locally
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setCurrentLORLevel(level);
    } finally {
      setIsSwitching(false);
    }
  }

  async function refreshLORLevels() {
    await fetchLORLevels();
  }

  useEffect(() => {
    if (currentTopRealm) {
      fetchLORLevels();
    }
  }, [currentTopRealm]);

  return (
    <Context.Provider
      value={{
        availableLORLevels,
        currentLORLevel,
        isLoading,
        isSwitching,
        switchLORLevel,
        refreshLORLevels,
      }}
    >
      {children}
    </Context.Provider>
  );
}
