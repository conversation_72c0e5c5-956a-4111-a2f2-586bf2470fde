import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import BreadCrumbs from "@/util/components/bread-crumbs/BreadCrumbs.tsx";
import Crumb from "@/util/components/bread-crumbs/Crumb.tsx";
import { LuCalendarCheck } from "react-icons/lu";
import NavBar from "@/root/narp/app/planner/scopeAndSequence/edit/_components/NavBar.tsx";
import { useParams } from "react-router-dom";
import { Provider } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Provider.tsx";
import { ActionBar } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/ActionBar.tsx";
import Chart from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Chart.tsx";
import DndHandler from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/DndHandler.tsx";
import { FaInfoCircle, FaMousePointer, FaPlus } from "react-icons/fa";
import { useState } from "react";

export function Component() {
  const { id } = useParams();
  const {
    outline: { title },
  } = useEditorContext();

  const [showGuide, setShowGuide] = useState(false);

  return (
    <Provider>
      <DndHandler>
        <div className="size-full overflow-y-auto overflow-x-auto flex flex-col py-5 gap-5">
          <div className="flex flex-col gap-3 px-5">
            <BreadCrumbs>
              <Crumb
                icon={<LuCalendarCheck className="size-4" />}
                label="Planner"
                to="/narp/app/planner"
              />
              <Crumb
                label={title}
                to={`/narp/app/planner/scopeAndSequence/edit/${id}`}
              />
              <Crumb
                label="Chart"
                base
                active={false}
                to={`/narp/app/planner/scopeAndSequence/edit/${id}/chart`}
              />
            </BreadCrumbs>
            <NavBar />
          </div>

          {/* User Guidance Section */}
          <div className="px-5">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <FaInfoCircle className="text-blue-600" />
                  <h3 className="text-lg font-semibold text-blue-800">
                    Timeline Chart Guide
                  </h3>
                </div>
                <button
                  onClick={() => setShowGuide(!showGuide)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {showGuide ? "Hide Guide" : "Show Guide"}
                </button>
              </div>

              <p className="text-blue-700 mb-3">
                Organize your curriculum topics on a visual timeline. Each topic
                box represents a learning unit with customizable timing and
                dependencies.
              </p>

              {showGuide && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-blue-800 flex items-center gap-2">
                      <FaMousePointer /> Basic Actions
                    </h4>
                    <ul className="space-y-1 text-blue-700">
                      <li>
                        • <strong>Double-click topic name</strong> to rename
                      </li>
                      <li>
                        • <strong>Drag entire box</strong> to move in time
                      </li>
                      <li>
                        • <strong>Drag left edge</strong> to change start time
                      </li>
                      <li>
                        • <strong>Drag right edge</strong> to change duration
                      </li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-blue-800 flex items-center gap-2">
                      <FaPlus /> Dependencies
                    </h4>
                    <ul className="space-y-1 text-blue-700">
                      <li>
                        • <strong>Drag blue + button</strong> to another topic
                        to create dependencies
                      </li>
                      <li>
                        • <strong>Dependencies ensure</strong> topics start
                        after prerequisites
                      </li>
                      <li>
                        • <strong>Arrows show</strong> the dependency flow
                      </li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>

          <ActionBar />

          <Chart />
        </div>
      </DndHandler>
    </Provider>
  );
}
