import {
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FiEdit3 } from "react-icons/fi";
import { FaSave } from "react-icons/fa";
import {
  ChangeEvent,
  FocusEvent,
  MouseEvent,
  useEffect,
  useRef,
  useState,
} from "react";

export function NotesSection({ topic }: { topic: Topic }) {
  const {
    topic: { setNotes },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState<string>(topic.notes);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  function updateSize() {
    if (textareaRef.current) {
      textareaRef.current.style.height = 0 + "px";
      textareaRef.current.style.height =
        textareaRef.current.scrollHeight + "px";
    }
  }

  useEffect(() => {
    setDisplayValue(topic.notes);
  }, [topic.notes]);

  useEffect(() => {
    updateSize();
  }, [displayValue]);

  function handleChange(e: ChangeEvent<HTMLTextAreaElement>) {
    updateSize();
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setNotes(topic.id, displayValue);
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget === textareaRef.current ||
      e.relatedTarget === buttonRef.current
    )
      return;
    setDisplayValue(topic.notes);
  }

  return (
    <div>
      <div className="text-xl mb-3 font-bold">Notes</div>

      <label className="flex flex-row gap-3 border border-neutral-300 p-3 rounded-lg">
        <FiEdit3 className="size-6 shrink-0 text-neutral-600" />

        <textarea
          tabIndex={0}
          ref={textareaRef}
          autoComplete="off"
          onChange={handleChange}
          onBlur={handleBlur}
          value={displayValue}
          className="grow resize-none p-0 overflow-hidden min-h-6 outline-none"
        />

        <div className="tooltip" data-tip="Confirm Edit">
          <button
            tabIndex={0}
            onBlur={handleBlur}
            onClick={handleClick}
            ref={buttonRef}
            disabled={displayValue === topic.notes}
            className="btn btn-lg btn-square btn-primary"
          >
            <FaSave />
          </button>
        </div>
      </label>
    </div>
  );
}
