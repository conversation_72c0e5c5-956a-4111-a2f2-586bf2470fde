import {
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import TopicEntry from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/TopicEntry.tsx";
import { MouseEvent, useMemo } from "react";
import { FaPlus } from "react-icons/fa6";
import { useLocation } from "react-router-dom";
import useSessionState from "@/util/hooks/useSessionState.tsx";
import { useDroppable } from "@dnd-kit/core";
import { DropData } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";

export function Entries() {
  const {
    topic: { entries, add },
  } = useEditorContext();
  const location = useLocation();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    add();
  }

  const [collapsedTopics, setCollapsedTopics] = useSessionState<EntityID[]>(
    location.pathname + ":collapsed",
    []
  );
  const collapsedSet = useMemo(() => {
    return new Set(collapsedTopics);
  }, [collapsedTopics]);

  return (
    <div className="flex flex-col px-5">
      {/* Drop zone at the top */}
      <TopicDropZone index={0} />

      {entries.map((topic, index) => (
        <div key={topic.id}>
          <TopicEntry
            topic={topic}
            index={index}
            collapsed={collapsedSet.has(topic.id)}
            toggleCollapsed={() => {
              if (collapsedSet.has(topic.id)) {
                collapsedSet.delete(topic.id);
              } else {
                collapsedSet.add(topic.id);
              }
              setCollapsedTopics([...collapsedSet]);
            }}
          />
          {/* Drop zone after each topic */}
          <TopicDropZone index={index + 1} />
        </div>
      ))}

      <button
        onClick={handleClick}
        className="btn btn-lg btn-success w-full mt-4"
      >
        <span>Add New Topic</span> <FaPlus />
      </button>
    </div>
  );
}

function TopicDropZone({ index }: { index: number }) {
  const dropData: DropData = {
    type: "TOPIC",
    index: index,
  };

  const { isOver, setNodeRef } = useDroppable({
    id: `topic-drop-zone-${index}`,
    data: dropData,
  });

  return (
    <div
      ref={setNodeRef}
      className={`transition-all duration-200 ${
        isOver
          ? "h-16 bg-blue-100 border-2 border-dashed border-blue-400 rounded-lg flex items-center justify-center mb-2"
          : "h-2"
      }`}
    >
      {isOver && (
        <div className="text-blue-600 font-medium text-sm flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          Drop topic here to reorder
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
        </div>
      )}
    </div>
  );
}
