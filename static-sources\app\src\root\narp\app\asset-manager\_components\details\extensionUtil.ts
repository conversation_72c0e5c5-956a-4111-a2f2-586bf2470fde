export const extensionUtil = new Map<string, string>(
  Object.entries({
    _coffee: "coffeescript",
    _js: "javascript",
    adp: "tcl",
    al: "perl",
    ant: "xml",
    aw: "php",
    axml: "xml",
    bash: "shell",
    bats: "shell",
    bones: "javascript",
    boot: "clojure",
    builder: "ruby",
    bzl: "python",
    c: "c",
    "c++": "cpp",
    cake: "coffeescript",
    cats: "c",
    cc: "cpp",
    ccxml: "xml",
    cfg: "ini",
    cgi: "shell",
    cjsx: "coffeescript",
    cl2: "clojure",
    clixml: "xml",
    clj: "clojure",
    cljc: "clojure",
    "cljs.hl": "clojure",
    cljs: "clojure",
    cljscm: "clojure",
    cljx: "clojure",
    coffee: "coffeescript",
    command: "shell",
    cp: "cpp",
    cpp: "cpp",
    cproject: "xml",
    cql: "sql",
    csl: "xml",
    cson: "coffeescript",
    csproj: "xml",
    ct: "xml",
    ctp: "php",
    cxx: "cpp",
    ddl: "sql",
    dfm: "pascal",
    dita: "xml",
    ditamap: "xml",
    ditaval: "xml",
    "dll.config": "xml",
    dotsettings: "xml",
    dpr: "pascal",
    ecl: "ecl",
    eclxml: "ecl",
    es: "javascript",
    es6: "javascript",
    ex: "elixir",
    exs: "elixir",
    fcgi: "shell",
    filters: "xml",
    frag: "javascript",
    fsproj: "xml",
    fxml: "xml",
    gemspec: "ruby",
    geojson: "json",
    glade: "xml",
    gml: "xml",
    god: "ruby",
    grxml: "xml",
    gs: "javascript",
    gyp: "python",
    h: "cpp",
    "h++": "cpp",
    handlebars: "handlebars",
    hbs: "handlebars",
    hcl: "hcl",
    hh: "cpp",
    hic: "clojure",
    hpp: "cpp",
    htm: "html",
    "html.hl": "html",
    html: "html",
    hxx: "cpp",
    iced: "coffeescript",
    idc: "c",
    iml: "xml",
    inc: "sql",
    ini: "ini",
    inl: "cpp",
    ipp: "cpp",
    irbrc: "ruby",
    ivy: "xml",
    j2: "python",
    jake: "javascript",
    jbuilder: "ruby",
    jelly: "xml",
    jinja: "python",
    jinja2: "python",
    js: "javascript",
    jsb: "javascript",
    jscad: "javascript",
    jsfl: "javascript",
    jsm: "javascript",
    json: "json",
    jsproj: "xml",
    jss: "javascript",
    kml: "xml",
    ksh: "shell",
    kt: "kotlin",
    ktm: "kotlin",
    kts: "kotlin",
    launch: "xml",
    lmi: "python",
    lock: "json",
    lpr: "pascal",
    lua: "lua",
    markdown: "markdown",
    md: "markdown",
    mdpolicy: "xml",
    mkd: "markdown",
    mkdn: "markdown",
    mkdown: "markdown",
    mm: "xml",
    mod: "xml",
    mspec: "ruby",
    mustache: "python",
    mxml: "xml",
    njs: "javascript",
    nproj: "xml",
    nse: "lua",
    nuspec: "xml",
    odd: "xml",
    osm: "xml",
    pac: "javascript",
    pas: "pascal",
    pd_lua: "lua",
    perl: "perl",
    ph: "perl",
    php: "php",
    php3: "php",
    php4: "php",
    php5: "php",
    phps: "php",
    phpt: "php",
    pl: "perl",
    plist: "xml",
    pluginspec: "xml",
    plx: "perl",
    pm: "perl",
    pod: "perl",
    podspec: "ruby",
    pp: "pascal",
    prc: "sql",
    prefs: "ini",
    pro: "ini",
    properties: "ini",
    props: "xml",
    ps1: "powershell",
    ps1xml: "xml",
    psc1: "xml",
    psd1: "powershell",
    psgi: "perl",
    psm1: "powershell",
    pt: "xml",
    py: "python",
    pyde: "python",
    pyp: "python",
    pyt: "python",
    pyw: "python",
    r: "r",
    rabl: "ruby",
    rake: "ruby",
    rb: "ruby",
    rbuild: "ruby",
    rbw: "ruby",
    rbx: "ruby",
    rbxs: "lua",
    rd: "r",
    rdf: "xml",
    reek: "yaml",
    "rest.txt": "restructuredtext",
    rest: "restructuredtext",
    ron: "markdown",
    rpy: "python",
    rq: "sparql",
    "rs.in": "rust",
    rs: "rust",
    rss: "xml",
    "rst.txt": "restructuredtext",
    rst: "restructuredtext",
    rsx: "r",
    ru: "ruby",
    ruby: "ruby",
    rviz: "yaml",
    sbt: "scala",
    sc: "scala",
    scala: "scala",
    scm: "scheme",
    scxml: "xml",
    "sh.in": "shell",
    sh: "shell",
    sjs: "javascript",
    sld: "scheme",
    sls: "scheme",
    sparql: "sparql",
    sps: "scheme",
    sql: "sql",
    srdf: "xml",
    ss: "scheme",
    ssjs: "javascript",
    st: "html",
    storyboard: "xml",
    sttheme: "xml",
    sublime_metrics: "javascript",
    sublime_session: "javascript",
    "sublime-build": "javascript",
    "sublime-commands": "javascript",
    "sublime-completions": "javascript",
    "sublime-keymap": "javascript",
    "sublime-macro": "javascript",
    "sublime-menu": "javascript",
    "sublime-mousemap": "javascript",
    "sublime-project": "javascript",
    "sublime-settings": "javascript",
    "sublime-snippet": "xml",
    "sublime-syntax": "yaml",
    "sublime-theme": "javascript",
    "sublime-workspace": "javascript",
    sv: "systemverilog",
    svh: "systemverilog",
    syntax: "yaml",
    t: "perl",
    tab: "sql",
    tac: "python",
    targets: "xml",
    tcc: "cpp",
    tcl: "tcl",
    tf: "hcl",
    thor: "ruby",
    tm: "tcl",
    tmcommand: "xml",
    tml: "xml",
    tmlanguage: "xml",
    tmpreferences: "xml",
    tmsnippet: "xml",
    tmtheme: "xml",
    tmux: "shell",
    tool: "shell",
    topojson: "json",
    tpp: "cpp",
    ts: "typescript",
    tsx: "typescript",
    udf: "sql",
    ui: "xml",
    urdf: "xml",
    ux: "xml",
    v: "verilog",
    vbproj: "xml",
    vcxproj: "xml",
    veo: "verilog",
    vh: "systemverilog",
    viw: "sql",
    vssettings: "xml",
    vxml: "xml",
    w: "c",
    watchr: "ruby",
    wlua: "lua",
    wsdl: "xml",
    wsf: "xml",
    wsgi: "python",
    wxi: "xml",
    wxl: "xml",
    wxs: "xml",
    x3d: "xml",
    xacro: "xml",
    xaml: "xml",
    xht: "html",
    xhtml: "html",
    xib: "xml",
    xlf: "xml",
    xliff: "xml",
    xmi: "xml",
    "xml.dist": "xml",
    xml: "xml",
    xproj: "xml",
    xpy: "python",
    xsd: "xml",
    xsjs: "javascript",
    xsjslib: "javascript",
    xul: "xml",
    "yaml-tmlanguage": "yaml",
    yaml: "yaml",
    yml: "yaml",
    zcml: "xml",
    zsh: "shell",
  }),
);

// export const imageExtensions = new Set([
//   "webp",
//   "svg",
//   "png",
//   "jpg",
//   "jpeg",
//   "jfif",
//   "pjpeg",
//   "pjp",
//   "gif",
//   "avif",
//   "apng",
// ]);
