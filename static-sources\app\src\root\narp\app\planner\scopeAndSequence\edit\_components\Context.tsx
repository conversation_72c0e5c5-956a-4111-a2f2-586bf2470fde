import { createContext, useContext } from "react";
import { GradeLevel } from "@/util/standards.ts";
import { SerializedDocument } from "@/root/narp/app/planner/_util/plannerDocuments/ver2.ts";
import { Operation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/operation.ts";
import {
  GoalAddOperation,
  GoalRemoveOperation,
  GoalSetDescriptionOperation,
  GoalSetParentOperation,
  GoalSetPriorityOperation,
  GoalSetTypeOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/goal.ts";
import {
  TopicAddOperation,
  TopicMoveOperation,
  TopicRemoveOperation,
  TopicSetEssentialQuestionsOperation,
  TopicSetEvidenceOfLearningOperation,
  TopicSetNameOperation,
  TopicSetNotesOperation,
  TopicSetResourcesOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/topic.ts";
import {
  TermAddOperation,
  TermRemoveOperation,
  TermSetCategoryOperation,
  TermSetDefinitionOperation,
  TermSetParentOperation,
  TermSetWordOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/term.ts";
import {
  OutlineAddKeywordOperation,
  OutlineRemoveKeywordOperation,
  OutlineSetAbstractOperation,
  OutlineSetGradeLevelOperation,
  OutlineSetTitleOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/outline.ts";
import {
  ActivityAddOperation,
  ActivityRemoveOperation,
  ActivitySetDeliverablesOperation,
  ActivitySetDescriptionOperation,
  ActivitySetDurationOperation,
  ActivitySetParentOperation,
  ActivitySetResourcesOperation,
  ActivitySetTitleOperation,
  ActivitySetTypeOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/activity.ts";

export enum SaveState {
  SAVED = "saved",
  SAVING = "saving",
  NOT_SAVED = "not saved",
  LOADING = "loading",
}

export type EntityID = number;

export enum LexicalCategory {
  NOUN = "noun",
  VERB = "verb",
  ADJECTIVE = "adjective",
  ADVERB = "adverb",
  PRONOUN = "pronoun",
  PREPOSITION = "preposition",
  CONJUNCTION = "conjunction",
  INTERJECTION = "interjection",
}

export interface Term {
  id: EntityID;
  parentTopic: EntityID | null;
  word: string;
  category: LexicalCategory;
  definition: string;
}

export enum GoalPriority {
  OPTIONAL = "optional",
  NICE_TO_HAVE = "nice to have",
  MUST_HAVE = "must have",
}

export interface Goal {
  id: EntityID;
  parentTopic: EntityID | null;
  type: string | null;
  description: string;
  priority: GoalPriority;
}

export enum ActivityType {
  CREATION = "creation",
  ANIMATION = "animation",
  RESEARCH = "research",
  PRESENTATION = "presentation",
  ASSESSMENT = "assessment",
  COLLABORATION = "collaboration",
}

export interface Activity {
  id: EntityID;
  parentTopic: EntityID | null;
  title: string;
  type: ActivityType;
  description: string;
  duration: string;
  resources: string[];
  deliverables: string[];
}

export interface Topic {
  id: EntityID;
  name: string;

  notes: string;
  essentialQuestions: string;
  resources: string;
  evidenceOfLearning: string;

  duration: number;
  start: number;
  dependencies: EntityID[];
}

export interface Context {
  serialize: () => SerializedDocument;

  pushOperation: (operation: Operation) => void;
  _operationLimit: number;
  undo: () => void;
  redo: () => void;

  cloud: {
    state: SaveState;
    save: () => void;
  };

  outline: {
    title: string;
    keywords: string[];
    abstract: string;
    gradeLevel: GradeLevel;

    setTitle: (value: string) => OutlineSetTitleOperation | null;
    setAbstract: (value: string) => OutlineSetAbstractOperation | null;
    setGradeLevel: (value: GradeLevel) => OutlineSetGradeLevelOperation | null;

    addKeyword: (value: string) => OutlineAddKeywordOperation | null;
    removeKeyword: (index: number) => OutlineRemoveKeywordOperation | null;
  };

  term: {
    entries: Term[];
    get: (id: EntityID) => Term | null;

    add: (term?: Term, index?: number) => TermAddOperation | null;
    remove: (id: EntityID) => TermRemoveOperation | null;

    setParentTopic: (
      id: EntityID,
      topic: EntityID | null,
    ) => TermSetParentOperation | null;
    setWord: (id: EntityID, value: string) => TermSetWordOperation | null;
    setCategory: (
      id: EntityID,
      value: LexicalCategory,
    ) => TermSetCategoryOperation | null;
    setDefinition: (
      id: EntityID,
      value: string,
    ) => TermSetDefinitionOperation | null;
  };

  goal: {
    entries: Goal[];
    get: (id: EntityID) => Goal | null;

    add: (goal?: Goal, index?: number) => GoalAddOperation | null;
    remove: (id: EntityID) => GoalRemoveOperation | null;

    setParentTopic: (
      id: EntityID,
      topic: EntityID | null,
    ) => GoalSetParentOperation | null;
    setType: (
      id: EntityID,
      value: string | null,
    ) => GoalSetTypeOperation | null;
    setDescription: (
      id: EntityID,
      value: string,
    ) => GoalSetDescriptionOperation | null;
    setPriority: (
      id: EntityID,
      value: GoalPriority,
    ) => GoalSetPriorityOperation | null;
  };

  activity: {
    entries: Activity[];
    get: (id: EntityID) => Activity | null;

    add: (activity?: Activity, index?: number) => ActivityAddOperation | null;
    remove: (id: EntityID) => ActivityRemoveOperation | null;

    setParentTopic: (
      id: EntityID,
      topic: EntityID | null,
    ) => ActivitySetParentOperation | null;
    setTitle: (id: EntityID, value: string) => ActivitySetTitleOperation | null;
    setType: (
      id: EntityID,
      value: ActivityType,
    ) => ActivitySetTypeOperation | null;
    setDescription: (
      id: EntityID,
      value: string,
    ) => ActivitySetDescriptionOperation | null;
    setDuration: (
      id: EntityID,
      value: string,
    ) => ActivitySetDurationOperation | null;
    setResources: (
      id: EntityID,
      value: string[],
    ) => ActivitySetResourcesOperation | null;
    setDeliverables: (
      id: EntityID,
      value: string[],
    ) => ActivitySetDeliverablesOperation | null;
  };

  topic: {
    entries: Topic[];
    get: (id: EntityID) => Topic | null;
    getTerms: (id: EntityID) => Term[];
    getGoals: (id: EntityID) => Goal[];
    getDependants: (id: EntityID) => Topic[];
    getActivities: (id: EntityID) => Activity[];

    add: () => TopicAddOperation | null;
    remove: (id: EntityID) => TopicRemoveOperation | null;
    move: (fromIndex: number, toIndex: number) => TopicMoveOperation | null;

    setName: (id: EntityID, value: string) => TopicSetNameOperation | null;
    setNotes: (id: EntityID, value: string) => TopicSetNotesOperation | null;
    setEssentialQuestions: (
      id: EntityID,
      value: string,
    ) => TopicSetEssentialQuestionsOperation | null;
    setResources: (
      id: EntityID,
      value: string,
    ) => TopicSetResourcesOperation | null;
    setEvidenceOfLearning: (
      id: EntityID,
      value: string,
    ) => TopicSetEvidenceOfLearningOperation | null;

    setDuration: (id: EntityID, value: number) => Operation | null;
    setStart: (id: EntityID, value: number) => Operation | null;
    addDependency: (id: EntityID, target: EntityID) => Operation | null;
    removeDependency: (id: EntityID, target: EntityID) => Operation | null;
  };
}

export const Context = createContext<Context>({} as Context);

export function useEditorContext(): Context {
  return useContext(Context);
}
