import { useAuth } from "@/util/auth/AuthContext.tsx";
import { useConfiguration } from "@/util/configuration/ConfigurationContext.tsx";
import { APIResult } from "@/util/api/types.ts";

export default function useDELETE() {
  const configuration = useConfiguration();
  const { getSession } = useAuth();

  return async (
    route: string,
    options?: {
      headers?: HeadersInit;
      params?: [string, string][];
      assertedRealmEidUrn: string;
    },
  ): Promise<APIResult> => {
    const [session, error] = await getSession();

    if (error || !session)
      return {
        data: null,
        error: null,
        response: new Response(null, {
          status: 401,
          statusText: "Unauthorized",
        }),
      };

    const url = new URL(route, configuration.apiBaseURL);
    if (options?.params)
      options.params.forEach(([key, value]) =>
        url.searchParams.append(key, value),
      );

    const headers = new Headers(options?.headers);
    if (options?.assertedRealmEidUrn)
      headers.append("X-Ayode-Asserted-Realm-EID", options.assertedRealmEidUrn);
    headers.append(
      "Authorization",
      `Bearer ${session.getAccessToken().getJwtToken()}`,
    );

    const response = await fetch(url, {
      method: "DELETE",
      headers: headers,
    });

    if (!response.ok) {
      return {
        data: null,
        error: await response.json(),
        response: response,
      };
    } else {
      return {
        data: await response.blob(),
        error: null,
        response: response,
      };
    }
  };
}
