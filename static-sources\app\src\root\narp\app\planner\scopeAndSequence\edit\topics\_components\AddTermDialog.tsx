import { FormEvent, RefObject, useImperativeHandle, useRef } from "react";
import { LexicalCategory } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaXmark } from "react-icons/fa6";

export interface AddTermDialogRef {
  close: () => void;
  open: () => void;
  dialog: RefObject<HTMLDialogElement>;
}

export default function AddTermDialog({
  ref,
  onClose,
  onOpen,
  onSubmit,
}: {
  ref: RefObject<AddTermDialogRef>;
  onOpen?: () => void;
  onClose?: () => void;
  onSubmit?: (
    word: string,
    category: LexicalCategory,
    definition: string,
  ) => void;
}) {
  const dialogRef = useRef<HTMLDialogElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  function open() {
    dialogRef.current?.showModal();
    formRef.current?.reset();
    onOpen && onOpen();
  }

  function close() {
    dialogRef.current?.close();
    onClose && onClose();
  }

  function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const word = data.get("word")?.toString();
    const category = data.get("category")?.toString();
    const definition = data.get("definition")?.toString();

    if (!word || !category || !definition) return;

    onSubmit && onSubmit(word, category as LexicalCategory, definition);
    close();
  }

  useImperativeHandle(ref, () => {
    return {
      open: open,
      close: close,
      dialog: dialogRef,
    };
  });

  return (
    <dialog
      ref={dialogRef}
      className="not-open:hidden min-w-md m-auto bg-white border border-neutral-400 rounded-lg shadow overflow-visible"
      onClose={(e) => {
        e.preventDefault();
        close();
      }}
    >
      <form
        ref={formRef}
        onSubmit={handleSubmit}
        className="size-full flex flex-col gap-5 items-center p-5"
      >
        <div className="w-full flex flex-row items-center gap-3">
          <div className="grow basis-0">
            <div className="tooltip" data-tip={"Cancel"}>
              <button
                type="button"
                onClick={close}
                className="btn btn-error btn-square shrink-0"
              >
                <FaXmark />
              </button>
            </div>
          </div>
          <div className="text-xl font-bold">Add New Term</div>
          <div className="grow basis-0"></div>
        </div>

        <label className="floating-label w-full">
          <span className="font-bold">Word</span>
          <input
            required
            autoFocus
            defaultValue=""
            name="word"
            type="text"
            autoComplete="off"
            className="input input-lg w-full"
          />
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Lexical Category</span>
          <select
            name="category"
            autoComplete="off"
            className="select select-lg w-full"
            defaultValue={LexicalCategory.NOUN}
          >
            <option>{LexicalCategory.NOUN}</option>
            <option>{LexicalCategory.PRONOUN}</option>
            <option>{LexicalCategory.VERB}</option>
            <option>{LexicalCategory.ADVERB}</option>
            <option>{LexicalCategory.ADJECTIVE}</option>
            <option>{LexicalCategory.PREPOSITION}</option>
            <option>{LexicalCategory.INTERJECTION}</option>
            <option>{LexicalCategory.CONJUNCTION}</option>
          </select>
        </label>

        <label className="floating-label w-full">
          <span className="font-bold">Definition</span>
          <textarea
            required
            defaultValue=""
            name="definition"
            autoComplete="off"
            className="textarea textarea-lg w-full"
          />
        </label>

        <button type="submit" className="btn btn-primary w-full">
          Create
        </button>
      </form>
    </dialog>
  );
}
