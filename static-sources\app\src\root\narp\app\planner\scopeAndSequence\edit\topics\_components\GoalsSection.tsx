import {
  Goal,
  GoalPriority,
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { MouseEvent, ReactNode, useMemo, useRef } from "react";
import {
  DragData,
  DropData,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";
import { useDraggable, useDroppable } from "@dnd-kit/core";
import { FaGripLinesVertical, FaPlus, FaXmark } from "react-icons/fa6";
import { LuGoal } from "react-icons/lu";
import SearchSelector from "@/util/components/SearchSelector.tsx";
import AddGoalDialog, {
  AddGoalDialogRef,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/AddGoalDialog.tsx";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";

function getPriorityChip(priority: GoalPriority): ReactNode {
  return (
    <div
      className={`font-bold px-3 p-1 rounded-full shrink-0 ${priority === GoalPriority.OPTIONAL
        ? "bg-teal-100 text-teal-700"
        : priority === GoalPriority.NICE_TO_HAVE
          ? "bg-indigo-100 text-indigo-700"
          : priority === GoalPriority.MUST_HAVE
            ? "bg-primary-100 text-primary-700"
            : "Unknown Priority"
        }`}
    >
      {priority === GoalPriority.OPTIONAL
        ? "Optional"
        : priority === GoalPriority.NICE_TO_HAVE
          ? "Nice-to-have"
          : priority === GoalPriority.MUST_HAVE
            ? "Must Have"
            : "Unknown Priority"}
    </div>
  );
}

type Priority = (typeof GoalPriority)[keyof typeof GoalPriority];

const priorityScale: {
  [priority in Priority]: number;
} = {
  [GoalPriority.OPTIONAL]: 0,
  [GoalPriority.NICE_TO_HAVE]: 1,
  [GoalPriority.MUST_HAVE]: 2,
};

function sortGoals(a: Goal, b: Goal) {
  if (a.type === null && b.type !== null) return 1;
  if (a.type !== null && b.type === null) return -1;

  if (a.type !== null && b.type !== null) {
    if (a.type < b.type) return 1;
    if (a.type > b.type) return -1;
  }

  const aScale = priorityScale[a.priority]!;
  const bScale = priorityScale[b.priority]!;

  if (aScale < bScale) return 1;
  if (aScale > bScale) return -1;

  if (a.description > b.description) {
    return 1;
  } else {
    return -1;
  }
}

export default function GoalsSection({ topic }: { topic: Topic }) {
  const {
    goal: { entries, setParentTopic, add },
    topic: { getGoals },
  } = useEditorContext();

  const goals = useMemo(
    () => getGoals(topic.id).sort(sortGoals),
    [topic.id, getGoals],
  );

  const dropData: DropData = {
    type: "GOAL",
    topic: topic,
  };

  const { isOver, setNodeRef } = useDroppable({
    id: topic.id + ":goals",
    data: dropData,
  });

  function handleSelect(selected: Goal) {
    setParentTopic(selected.id, topic.id);
  }

  function handleDialogSubmit(
    type: string,
    priority: GoalPriority,
    description: string,
  ) {
    const newID = getNonCollidingID(entries.map((entry) => entry.id));

    const newGoal: Goal = {
      id: newID,
      parentTopic: topic.id,
      type: type,
      description: description,
      priority: priority,
    };

    add(newGoal);
  }

  const dialogRef = useRef<AddGoalDialogRef>(null);

  return (
    <div className="flex flex-col gap-3">
      <AddGoalDialog onSubmit={handleDialogSubmit} ref={dialogRef} />

      <div className="text-xl font-bold">Goals</div>

      <div
        ref={setNodeRef}
        className={`min-h-4 flex flex-col gap-3 rounded-lg outline-2 outline-offset-2 ${isOver ? "outline-primary-400" : "outline-transparent"}`}
      >
        {goals.map((goal, index) => (
          <GoalEntry goal={goal} key={index} />
        ))}
      </div>

      <div className="flex flex-row gap-3 w-full">
        <div className="group relative tooltip w-full" data-tip="Add Goal">
          <button
            tabIndex={0}
            type="button"
            className="btn btn-sm w-full btn-success btn-outline"
          >
            <FaPlus />
          </button>

          <div className="absolute top-full w-full">
            <SearchSelector
              descriptors={entries
                .filter((entry) => entry.parentTopic !== topic.id)
                .sort(sortGoals)
                .map((entry) => {
                  return {
                    label:
                      (entry.type || "No Type") +
                      " : " +
                      entry.priority +
                      " : " +
                      entry.description,
                    data: entry,
                  };
                })}
              onSelect={handleSelect}
            />
          </div>
        </div>

        <div className="tooltip grow w-full" data-tip="Add New Term">
          <button
            type="button"
            onClick={() => dialogRef.current?.open()}
            className="btn btn-sm w-full btn-primary btn-outline"
          >
            <FaPlus />
          </button>
        </div>
      </div>
    </div>
  );
}

function GoalEntry({ goal }: { goal: Goal }) {
  const {
    goal: { setParentTopic },
  } = useEditorContext();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setParentTopic(goal.id, null);
  }

  const dragData: DragData = {
    type: "GOAL",
    goal: goal,
  };

  const { isDragging, attributes, listeners, setNodeRef } = useDraggable({
    id: goal.id + ":goal",
    data: dragData,
  });

  return (
    <div
      ref={setNodeRef}
      className={`flex flex-col md:flex-row gap-3 items-center p-3 border-1 border-neutral-300 rounded-lg ${isDragging ? "invisible" : "visible"}`}
    >
      <div className="w-full md:w-auto items-center flex flex-row gap-3">
        <div {...attributes} {...listeners} className="h-full cursor-grab">
          <FaGripLinesVertical className="shrink-0 text-neutral-600" />
        </div>

        <div>
          <LuGoal className="size-6 shrink-0 text-neutral-600" />
        </div>
      </div>

      <div className="flex flex-col grow w-full md:w-[calc(100%-280px)]">
        <div className="font-bold">{goal.type || "No Type"}</div>
        <div>{goal.description}</div>
      </div>
      <div className="w-full md:w-auto items-center flex flex-row gap-3 justify-between">
        {getPriorityChip(goal.priority)}

        <div
          onClick={handleClick}
          className="tooltip"
          data-tip="Remove From Topic"
        >
          <button tabIndex={0} type="button" className="btn btn-lg btn-square ">
            <FaXmark />
          </button>
        </div>
      </div>
    </div>
  );
}
