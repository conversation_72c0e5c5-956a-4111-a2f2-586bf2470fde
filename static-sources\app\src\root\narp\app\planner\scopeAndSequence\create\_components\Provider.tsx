import {
  Authority,
  Context,
  Region,
  Standard,
  Subregion,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import { ReactNode, useState } from "react";
import { SerializedDocument } from "@/root/narp/app/planner/_util/plannerDocuments/ver2.ts";

export default function Provider({ children }: { children: ReactNode }) {
  const [region, setRegion] = useState<Region | null>(null);
  const [subregion, setSubregion] = useState<Subregion | null>(null);
  const [authority, setAuthority] = useState<Authority | null>(null);
  const [standard, setStandard] = useState<Standard | null>(null);
  const [doc, setDoc] = useState<SerializedDocument | null>(null);
  const [blockCount, setBlockCount] = useState<number | null>(null);

  return (
    <Context.Provider
      value={{
        region,
        setRegion,
        subregion,
        setSubregion,
        authority,
        setAuthority,
        standard,
        setStandard,
        doc,
        setDoc,
        blockCount,
        setBlockCount,
      }}
    >
      {children}
    </Context.Provider>
  );
}
