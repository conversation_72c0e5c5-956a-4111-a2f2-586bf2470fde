import {
  LexicalCategory,
  Term,
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import {
  DragData,
  DropData,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";
import { useDraggable, useDroppable } from "@dnd-kit/core";
import { FaGripLinesVertical, FaPlus, FaXmark } from "react-icons/fa6";
import { HiOutlineLightBulb } from "react-icons/hi";
import SearchSelector from "@/util/components/SearchSelector.tsx";
import { MouseEvent, useMemo, useRef } from "react";
import { getLexicalColor } from "@/root/narp/app/planner/_util/getLexicalColor.ts";
import AddTermDialog, {
  AddTermDialogRef,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/AddTermDialog.tsx";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";

export function TermsSection({ topic }: { topic: Topic }) {
  const {
    term: { setParentTopic, entries, add },
    topic: { getTerms },
  } = useEditorContext();

  const terms = useMemo(() => {
    return getTerms(topic.id).sort((a, b) => (a.word > b.word ? 1 : -1));
  }, [getTerms, topic.id]);

  const dropData: DropData = {
    type: "TERM",
    topic: topic,
  };

  const { isOver, setNodeRef } = useDroppable({
    id: topic.id + ":terms",
    data: dropData,
  });

  function handleSelect(selected: Term) {
    setParentTopic(selected.id, topic.id);
  }

  const dialogRef = useRef<AddTermDialogRef>(null);

  function handleDialogSubmit(
    word: string,
    category: LexicalCategory,
    definition: string,
  ) {
    const newID = getNonCollidingID(entries.map((entry) => entry.id));

    const newTerm: Term = {
      id: newID,
      parentTopic: topic.id,
      word: word,
      category: category,
      definition: definition,
    };

    add(newTerm);
  }

  return (
    <div className="flex flex-col gap-3">
      <AddTermDialog onSubmit={handleDialogSubmit} ref={dialogRef} />

      <div className="text-xl font-bold">Vocabulary</div>

      <div
        ref={setNodeRef}
        className={`min-h-4 mb-3 gap-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 rounded-lg outline-2 outline-offset-2 ${isOver ? "outline-primary-400" : "outline-transparent"}`}
      >
        {terms.map((term, index) => (
          <TermEntry term={term} key={index} />
        ))}
      </div>

      <div className="flex flex-row w-full gap-3">
        <div className="group relative tooltip grow" data-tip="Add Term">
          <button
            tabIndex={0}
            type="button"
            className="btn btn-sm w-full btn-success btn-outline"
          >
            <FaPlus />
          </button>

          <div className="absolute top-full w-full">
            <SearchSelector
              descriptors={entries
                .filter((entry) => entry.parentTopic !== topic.id)
                .map((term) => {
                  return {
                    label: term.word + " : " + term.definition,
                    data: term,
                  };
                })}
              onSelect={handleSelect}
            />
          </div>
        </div>

        <div className="tooltip grow" data-tip="Add New Term">
          <button
            type="button"
            onClick={() => dialogRef.current?.open()}
            className="btn btn-sm w-full btn-primary btn-outline"
          >
            <FaPlus />
          </button>
        </div>
      </div>
    </div>
  );
}

function TermEntry({ term }: { term: Term }) {
  const {
    term: { setParentTopic },
  } = useEditorContext();

  const dragData: DragData = {
    type: "TERM",
    term: term,
  };

  const { isDragging, attributes, listeners, setNodeRef } = useDraggable({
    id: term.id + ":term",
    data: dragData,
  });

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setParentTopic(term.id, null);
  }

  return (
    <div
      ref={setNodeRef}
      className={`flex flex-row p-3 gap-3 bg-white border border-neutral-300 rounded-lg ${isDragging ? "invisible" : "visible"}`}
    >
      <div
        {...listeners}
        {...attributes}
        className="h-full flex items-center justify-center cursor-grab"
      >
        <FaGripLinesVertical className="text-neutral-600" />
      </div>

      <HiOutlineLightBulb className="size-6 shrink-0 text-neutral-600" />

      <div className="flex flex-col gap-1 grow">
        <div className="font-bold">{term.word}</div>
        <div
          style={{ backgroundColor: getLexicalColor(term.category) }}
          className="px-1 rounded size-fit"
        >
          {term.category}
        </div>
        <div>{term.definition}</div>
      </div>

      <div className="tooltip" data-tip="Remove From Topic">
        <button
          type="button"
          onClick={handleClick}
          className="btn btn-square btn-lg"
        >
          <FaXmark />
        </button>
      </div>
    </div>
  );
}
