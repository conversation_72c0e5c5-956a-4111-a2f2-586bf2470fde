import {
  Filter,
  Search,
  List,
  Grid3X3,
  Settings,
  Download,
  Trash2,
  Edit,
  X,
  Check,
  Image,
  Video,
  File,
} from "lucide-react";
import { useState, useMemo } from "react";

interface FileItem {
  id: string;
  name: string;
  type: "image" | "video" | "document";
  subject: string;
  grade: string;
  tags: string[];
  createdDate: string;
  selected?: boolean;
}

const mockFiles: FileItem[] = [
  {
    id: "1",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
  },
  {
    id: "2",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
  },
  {
    id: "3",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
  },
  {
    id: "4",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
  },
  {
    id: "5",
    name: "English Lab Manual - Grade 4.mp4",
    type: "video",
    subject: "English",
    grade: "4",
    tags: ["laboratory", "experiment"],
    createdDate: "June 8, 2025",
  },
  {
    id: "6",
    name: "English Lab Manual - Grade 4.mp4",
    type: "video",
    subject: "English",
    grade: "4",
    tags: ["laboratory", "experiment"],
    createdDate: "June 8, 2025",
  },
  {
    id: "7",
    name: "English Lab Manual - Grade 4.mp4",
    type: "video",
    subject: "English",
    grade: "4",
    tags: ["laboratory", "experiment"],
    createdDate: "June 8, 2025",
  },
];

export function Component() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("All Subjects");
  const [selectedGrade, setSelectedGrade] = useState("All Grades");
  const [selectedState, setSelectedState] = useState("All States");
  const [selectedCountry, setSelectedCountry] = useState("All Countries");
  const [viewMode, setViewMode] = useState<"list" | "grid">("grid");
  const [files, setFiles] = useState(mockFiles);
  const [showMetadataModal, setShowMetadataModal] = useState(false);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Filter files based on current filters and search
  const filteredFiles = useMemo(() => {
    return files.filter((file) => {
      const matchesSearch =
        searchTerm === "" ||
        file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        file.tags.some((tag) =>
          tag.toLowerCase().includes(searchTerm.toLowerCase())
        );

      const matchesSubject =
        selectedSubject === "All Subjects" || file.subject === selectedSubject;
      const matchesGrade =
        selectedGrade === "All Grades" || file.grade === selectedGrade;

      return matchesSearch && matchesSubject && matchesGrade;
    });
  }, [files, searchTerm, selectedSubject, selectedGrade]);

  const selectedFiles = filteredFiles.filter((f) => f.selected);
  const selectedCount = selectedFiles.length;

  const quickFilters = ["Mathematics", "Grade 5", "Experiments"];

  const handleSelectFile = (fileId: string) => {
    setFiles(
      files.map((f) => (f.id === fileId ? { ...f, selected: !f.selected } : f))
    );
  };

  const handleSelectAll = () => {
    const allSelected = selectedCount === filteredFiles.length;
    setFiles(
      files.map((f) => {
        if (filteredFiles.find((ff) => ff.id === f.id)) {
          return { ...f, selected: !allSelected };
        }
        return f;
      })
    );
  };

  const handleClearSelection = () => {
    setFiles(files.map((f) => ({ ...f, selected: false })));
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case "image":
        return <Image className="w-5 h-5 text-primary-500" />;
      case "video":
        return <Video className="w-5 h-5 text-red-500" />;
      default:
        return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="w-full h-full flex flex-col bg-neutral-50">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-neutral-900">
              Advanced Repository
            </h1>
            <p className="text-sm text-neutral-600 mt-1">
              Comprehensive content management with advanced organization tools.
            </p>
          </div>
          <button className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-md hover:bg-neutral-50 transition-colors">
            <Settings className="w-4 h-4" />
            Organization Tools
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white border-b border-neutral-200 px-6 py-4">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-neutral-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search files, subjects, authors, tags..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Quick Filters and Filters Row */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-neutral-600">Quick filters:</span>
              {quickFilters.map((filter) => (
                <button
                  key={filter}
                  className="px-3 py-1 text-sm bg-primary-50 text-primary-700 rounded-full hover:bg-primary-100 border border-primary-200 transition-colors"
                >
                  {filter}
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-md hover:bg-neutral-50 transition-colors"
            >
              <Filter className="w-3 h-3" />
              Filters
            </button>
          </div>

          {/* Filter Dropdowns */}
          {showFilters && (
            <div className="flex items-center gap-4 p-4 bg-neutral-50 rounded-lg border border-neutral-200">
              <span className="flex items-center gap-2 text-sm text-neutral-600 font-medium">
                <Filter className="w-3 h-3" />
                Filters:
              </span>
              <select
                className="px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none bg-white transition-colors"
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
              >
                <option>All Subjects</option>
                <option>Foreign Language</option>
                <option>English</option>
                <option>Mathematics</option>
              </select>
              <select
                className="px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none bg-white transition-colors"
                value={selectedGrade}
                onChange={(e) => setSelectedGrade(e.target.value)}
              >
                <option>All Grades</option>
                <option>Grade 4</option>
                <option>Grade 5</option>
                <option>Grade 8</option>
              </select>
              <select
                className="px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none bg-white transition-colors"
                value={selectedState}
                onChange={(e) => setSelectedState(e.target.value)}
              >
                <option>All States</option>
                <option>Texas</option>
                <option>California</option>
              </select>
              <select
                className="px-3 py-2 border border-neutral-300 rounded-md text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none bg-white transition-colors"
                value={selectedCountry}
                onChange={(e) => setSelectedCountry(e.target.value)}
              >
                <option>All Countries</option>
                <option>USA</option>
                <option>Canada</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Toolbar */}
      <div className="bg-white border-b border-neutral-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center border border-neutral-300 rounded-md overflow-hidden">
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 transition-colors ${viewMode === "list" ? "bg-primary-600 text-white" : "bg-white text-neutral-600 hover:bg-neutral-50"}`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 transition-colors ${viewMode === "grid" ? "bg-primary-600 text-white" : "bg-white text-neutral-600 hover:bg-neutral-50"}`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
            </div>
            <span className="text-sm text-neutral-600">
              {filteredFiles.length} of {files.length} files
            </span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleSelectAll}
              className="px-3 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-md hover:bg-neutral-50 transition-colors"
            >
              Select All
            </button>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors shadow-sm"
              onClick={() => setShowDownloadModal(true)}
            >
              <Download className="w-4 h-4" />
              Download Selected
            </button>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-6 overflow-auto">
        {viewMode === "grid" ? (
          /* Grid View */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredFiles.map((file) => (
              <div
                key={file.id}
                className={`bg-white rounded-lg border p-4 hover:shadow-md transition-all duration-200 ${
                  file.selected
                    ? "border-primary-500 ring-2 ring-primary-500 ring-opacity-20 bg-primary-50/30"
                    : "border-neutral-200 hover:border-neutral-300"
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={file.selected || false}
                      onChange={() => handleSelectFile(file.id)}
                      className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500 transition-colors"
                    />
                    {getFileIcon(file.type)}
                  </div>
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => setShowMetadataModal(true)}
                      className="p-1 text-neutral-400 hover:text-primary-600 transition-colors"
                    >
                      <Edit className="w-3 h-3" />
                    </button>
                    <button className="p-1.5 text-neutral-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors">
                      <Download className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-neutral-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>

                <h3 className="font-medium text-neutral-900 mb-2 text-sm leading-tight">
                  {file.name}
                </h3>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs text-neutral-600">
                    <span>{file.subject}</span>
                    <span>•</span>
                    <span>{file.grade}</span>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {file.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                    <span className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs">
                      +2
                    </span>
                  </div>

                  <div className="text-xs text-neutral-500">
                    Created {file.createdDate}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* List View */
          <div className="bg-white rounded-lg border border-neutral-200 overflow-hidden">
            <div className="grid grid-cols-12 gap-4 p-4 bg-neutral-50 border-b border-neutral-200 text-sm font-medium text-neutral-700">
              <div className="col-span-1">
                <input
                  type="checkbox"
                  checked={
                    selectedCount === filteredFiles.length &&
                    filteredFiles.length > 0
                  }
                  onChange={handleSelectAll}
                  className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
              <div className="col-span-5">Name</div>
              <div className="col-span-2">Subject</div>
              <div className="col-span-1">Grade</div>
              <div className="col-span-2">Created</div>
              <div className="col-span-1">Actions</div>
            </div>

            {filteredFiles.map((file) => (
              <div
                key={file.id}
                className={`grid grid-cols-12 gap-4 p-4 border-b border-neutral-100 hover:bg-neutral-50 transition-colors ${
                  file.selected ? "bg-[#F4F4FF] border-primary-200" : ""
                }`}
              >
                <div className="col-span-1">
                  <input
                    type="checkbox"
                    checked={file.selected || false}
                    onChange={() => handleSelectFile(file.id)}
                    className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>
                <div className="col-span-5 flex items-center gap-3">
                  {getFileIcon(file.type)}
                  <span className="text-sm font-medium text-neutral-900 truncate">
                    {file.name}
                  </span>
                </div>
                <div className="col-span-2 text-sm text-neutral-600">
                  {file.subject}
                </div>
                <div className="col-span-1 text-sm text-neutral-600">
                  {file.grade}
                </div>
                <div className="col-span-2 text-sm text-neutral-500">
                  {file.createdDate}
                </div>
                <div className="col-span-1 flex items-center gap-1">
                  <button
                    onClick={() => setShowMetadataModal(true)}
                    className="p-1 text-neutral-400 hover:text-primary-600 transition-colors"
                  >
                    <Edit className="w-3 h-3" />
                  </button>
                  <button className="p-1.5 text-neutral-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors">
                    <Download className="w-4 h-4" />
                  </button>
                  <button className="p-1 text-neutral-400 hover:text-red-600 transition-colors">
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            ))}

            {filteredFiles.length === 0 && (
              <div className="p-12 text-center text-neutral-500">
                <p className="text-lg">No files found</p>
                <p className="text-sm">Try adjusting your search or filters</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selection Action Bar */}
      {selectedCount > 0 && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white border border-neutral-200 rounded-lg shadow-lg px-6 py-3">
          <div className="flex items-center gap-4">
            <span className="font-medium text-sm text-neutral-700">
              {selectedCount} selected
            </span>
            <button
              onClick={handleClearSelection}
              className="flex items-center gap-1 text-sm text-neutral-600 hover:text-neutral-800 transition-colors"
            >
              <X className="w-3 h-3" />
              Clear
            </button>
            <button
              onClick={handleSelectAll}
              className="flex items-center gap-1 text-sm text-neutral-600 hover:text-neutral-800 transition-colors"
            >
              <Check className="w-3 h-3" />
              Select All
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors">
              Smart Organize
            </button>
            <button
              onClick={() => setShowDownloadModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors shadow-sm"
            >
              <Download className="w-4 h-4" />
              Download
            </button>
          </div>
        </div>
      )}

      {/* Edit Metadata Modal */}
      {showMetadataModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-neutral-900">
                Edit metadata
              </h2>
              <button
                onClick={() => setShowMetadataModal(false)}
                className="text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Subject
                  </label>
                  <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                    <option>History</option>
                    <option>Mathematics</option>
                    <option>English</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Grade Level
                  </label>
                  <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                    <option>Grade 2</option>
                    <option>Grade 3</option>
                    <option>Grade 4</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    State
                  </label>
                  <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                    <option>Texas</option>
                    <option>California</option>
                    <option>Florida</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Country
                  </label>
                  <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                    <option>USA</option>
                    <option>Canada</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    School
                  </label>
                  <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                    <option>Allen High School</option>
                    <option>Other School</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Author
                  </label>
                  <input
                    type="text"
                    defaultValue="Dr. David"
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Description
                </label>
                <textarea
                  defaultValue="Worksheet designed for history instruction at grade 2 level"
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none resize-none transition-colors"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Tag
                </label>
                <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                  <option>Tag</option>
                  <option>Resource</option>
                  <option>Material</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setShowMetadataModal(false)}
                className="px-4 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-md hover:bg-neutral-50 transition-colors"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors">
                Done
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Download Format Modal */}
      {showDownloadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-neutral-900">
                Download Format
              </h2>
              <button
                onClick={() => setShowDownloadModal(false)}
                className="text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Download Format
                </label>
                <select className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 outline-none transition-colors">
                  <option>PDF</option>
                  <option>ZIP</option>
                  <option>Original Format</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Selected files:
                </label>
                <div className="space-y-1 text-sm text-neutral-600">
                  {selectedFiles.map((file) => (
                    <div key={file.id} className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-primary-400 rounded-full"></span>
                      {file.name}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setShowDownloadModal(false)}
                className="px-4 py-2 text-sm text-neutral-600 hover:text-neutral-800 border border-neutral-300 rounded-md hover:bg-neutral-50 transition-colors"
              >
                Cancel
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors shadow-sm">
                <Download className="w-4 h-4" />
                Download
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
