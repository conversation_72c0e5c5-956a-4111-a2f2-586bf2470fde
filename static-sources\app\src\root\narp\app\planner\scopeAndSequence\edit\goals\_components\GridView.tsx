import {
  EntityID,
  Goal,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaPlus, FaTrash } from "react-icons/fa6";
import { Dispatch, MouseEvent, SetStateAction, useState } from "react";
import { FaEdit, FaCog } from "react-icons/fa";
import {
  getPriorityBackgroundColor,
  getPriorityContentColor,
} from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/getPriorityColors.ts";
import titleize from "titleize";
import { LuGoal } from "react-icons/lu";

export default function GridView({
  entries,
  setEditID,
}: {
  entries: Goal[];
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    goal: { add },
  } = useEditorContext();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    add();
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 px-5 shrink-0 gap-3">
      {entries.map((goal, index) => (
        <Entry goal={goal} key={index} setEditID={setEditID} />
      ))}
      <button
        onClick={handleClick}
        type="button"
        className="min-h-32 size-full btn btn-lg btn-success btn-outline size-full rounded-lg"
      >
        <span>Add New Goal</span>
        <FaPlus />
      </button>
    </div>
  );
}

function Entry({
  goal,
  setEditID,
}: {
  goal: Goal;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    goal: { remove },
  } = useEditorContext();

  const [removeTimeout, setRemoveTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  function handleRemoveClick(e: MouseEvent) {
    e.preventDefault();
    if (removeTimeout !== null) {
      remove(goal.id);
      clearTimeout(removeTimeout);
      setRemoveTimeout(null);
    } else {
      setRemoveTimeout(
        setTimeout(() => {
          setRemoveTimeout(null);
        }, 1000)
      );
    }
  }

  function handleEditClick(e: MouseEvent) {
    e.preventDefault();
    setEditID(goal.id);
  }

  return (
    <div className="p-3 border border-neutral-300 rounded-lg flex flex-row gap-3">
      <LuGoal className="size-6 shrink-0" />

      <div className="flex flex-col grow gap-3">
        <div className="flex flex-row gap-3 items-center">
          <span className="text-lg font-bold">
            {goal.type === null ? "No Type" : goal.type}
          </span>
          <div className="grow" />
          <div className="flex flex-row gap-1 items-center">
            <div className="tooltip" data-tip="Edit">
              <button
                type="button"
                onClick={handleEditClick}
                className="btn btn-square btn-sm text-base hover:btn-primary focus-visible:btn-primary hover:text-white focus-visible:text-white flex items-center gap-1"
              >
                <FaCog className="mr-1" />
                <FaEdit />
              </button>
            </div>

            <div
              className="tooltip"
              data-tip={removeTimeout ? "Click Again To Confirm" : "Remove"}
            >
              <button
                type="button"
                onClick={handleRemoveClick}
                className="btn btn-square btn-sm text-base hover:btn-error focus-visible:btn-error hover:text-white focus-visible:text-white"
              >
                <FaTrash />
              </button>
            </div>
          </div>
        </div>

        <div className="flex flex-row gap-3">
          <div
            style={{
              backgroundColor: getPriorityBackgroundColor(goal.priority),
              color: getPriorityContentColor(goal.priority),
            }}
            className={`bg-neutral-200 rounded-full p-0.5 px-2 font-bold`}
          >
            {titleize(goal.priority)}
          </div>
        </div>

        <div>{goal.description}</div>
      </div>
    </div>
  );
}
