import {
  Activity,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { ChangeEvent } from "react";

export default function TitleField({ activity }: { activity: Activity }) {
  const {
    activity: { setTitle },
  } = useEditorContext();

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    setTitle(activity.id, e.target.value);
  }

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">Title</label>
      <input
        type="text"
        value={activity.title}
        onChange={handleChange}
        className="input input-bordered w-full"
        placeholder="Enter activity title"
      />
    </div>
  );
}
