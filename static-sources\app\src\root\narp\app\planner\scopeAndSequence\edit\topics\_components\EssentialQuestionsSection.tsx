import {
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaSave } from "react-icons/fa";
import {
  ChangeEvent,
  FocusEvent,
  MouseEvent,
  useEffect,
  useRef,
  useState,
} from "react";
import usePOST from "@/util/api/usePOST.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { APIEnvelope } from "@/util/api/types.ts";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { FaQuestion, FaWandMagicSparkles } from "react-icons/fa6";

export function EssentialQuestionsSection({ topic }: { topic: Topic }) {
  const {
    outline: { gradeLevel, keywords },
    topic: { setEssentialQuestions, getGoals, getTerms },
  } = useEditorContext();
  const topRealm = useTopRealm();
  const POST = usePOST();
  const pushError = usePushError();

  const [generating, setGenerating] = useState<boolean>(false);
  const [displayValue, setDisplayValue] = useState<string>(
    topic.essentialQuestions,
  );

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    setDisplayValue(topic.essentialQuestions);
  }, [topic.essentialQuestions]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 0 + "px";
      textareaRef.current.style.height =
        textareaRef.current.scrollHeight + "px";
    }
  }, [displayValue]);

  function handleChange(e: ChangeEvent<HTMLTextAreaElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    setEssentialQuestions(topic.id, displayValue);
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget === textareaRef.current ||
      e.relatedTarget === buttonRef.current
    )
      return;
    setDisplayValue(topic.essentialQuestions);
  }

  async function handleGenerate(e: MouseEvent) {
    e.preventDefault();
    if (generating) return;

    const payload = {
      realmEID: topRealm.externalID,
      subjects: {
        topics: [
          ...getGoals(topic.id).map((entry) => entry.description),
          ...getTerms(topic.id).map((entry) => entry.word),
          ...keywords,
        ],
        target: {
          age: gradeLevel + 5,
          domain: topic.name,
        },
      },
    };

    setGenerating(true);
    const { response, data, error } = await POST(
      "/v1/ai/curricula/essential-questions/generate",
      { assertedRealmEidUrn: topRealm.eidURN, body: JSON.stringify(payload) },
    );
    setGenerating(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    const envelope: APIEnvelope<{
      questions: [{ en_US: string; es_ES: string; fr_FR: string }];
    }> = JSON.parse(await data.text());

    setEssentialQuestions(
      topic.id,
      envelope.data.questions
        .map((entry, index) => index + 1 + ". " + entry.en_US)
        .join("\n"),
    );
  }

  return (
    <div>
      <div className="text-xl mb-3 font-bold">Essential Questions</div>

      <label className="flex flex-row gap-3 border border-neutral-300 p-3 rounded-lg">
        <FaQuestion className="size-6 shrink-0 text-neutral-600" />

        <textarea
          tabIndex={0}
          ref={textareaRef}
          autoComplete="off"
          onChange={handleChange}
          onBlur={handleBlur}
          value={displayValue}
          disabled={generating}
          className="grow resize-none p-0 overflow-hidden min-h-6 outline-none"
        />

        <div className="flex flex-col gap-3">
          <div className="tooltip" data-tip="Confirm Edit">
            <button
              tabIndex={0}
              onBlur={handleBlur}
              onClick={handleClick}
              ref={buttonRef}
              disabled={generating || displayValue === topic.essentialQuestions}
              className="btn btn-lg btn-square btn-primary"
            >
              <FaSave />
            </button>
          </div>

          <div className="tooltip">
            <div className="tooltip-content">
              Generate Essential <br /> Questions
            </div>
            <button
              tabIndex={0}
              onClick={handleGenerate}
              disabled={generating}
              className={`btn btn-lg btn-square btn-primary ${generating && "animate-bounce"}`}
            >
              {generating ? (
                <div className="loading loading-bars loading-xl" />
              ) : (
                <FaWandMagicSparkles />
              )}
            </button>
          </div>
        </div>
      </label>
    </div>
  );
}
