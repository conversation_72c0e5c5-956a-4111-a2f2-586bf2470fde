import { RouteObject } from "react-router-dom";
import { default as IndexRoute } from "./_index/Route";
import { default as ForgotRoute } from "@/root/narp/auth/reset/Route";
import { default as MFARoute } from "./mfa/Route.tsx";
import { default as ResetRoute } from "@/root/narp/auth/forgot/Route.tsx";
import { default as SignUpRoute } from "@/root/narp/auth/signup/Route.tsx";
import { default as VerifyRoute } from "@/root/narp/auth/verify/Route.tsx";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";

const Route: RouteObject = {
  path: "auth",
  hydrateFallbackElement: <PageSkeleton />,
  lazy: () => import("./Component"),
  children: [
    IndexRoute,
    ForgotRoute,
    MFARoute,
    ResetRoute,
    SignUpRoute,
    VerifyRoute,
  ],
};

export default Route;
