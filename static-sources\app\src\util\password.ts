// valid characters
export const charactersRegex =
  /^[A-Za-z0-9^$*.[\]{}()?"!@#%&/\\,><':;|_~`=+\- ]{8,256}/;
// no leading
export const whitespaceRegex = /^(?!\\s+)(?!.*\s+$)/;
// contains at least 1 number
export const numberRegex = /^(?=.*[0-9])/;
// contains at least 1 special character
export const specialRegex = /^(?=.*[\^$*.[\]{}()?"!@#%&/\\,><':;|_~`=+\- ])/;
// contains at lease 1 uppercase letter
export const uppercaseRegex = /^(?=.*[A-Z])/;
// contains at lease 1 lowercase letter
export const lowercaseRegex = /^(?=.*[a-z])/;

export function check(value: string) {
  return (
    charactersRegex.test(value) &&
    whitespaceRegex.test(value) &&
    numberRegex.test(value) &&
    specialRegex.test(value) &&
    uppercaseRegex.test(value) &&
    lowercaseRegex.test(value)
  );
}

export function test(value: string) {
  const charactersRegexCheck = charactersRegex.test(value);
  const whitespaceRegexCheck = whitespaceRegex.test(value);
  const numberRegexCheck = numberRegex.test(value);
  const specialRegexCheck = specialRegex.test(value);
  const uppercaseRegexCheck = uppercaseRegex.test(value);
  const lowercaseRegexCheck = lowercaseRegex.test(value);

  const valid =
    charactersRegexCheck &&
    whitespaceRegexCheck &&
    numberRegexCheck &&
    specialRegexCheck &&
    uppercaseRegexCheck &&
    lowercaseRegexCheck;

  return {
    valid: valid,
    charactersRegex: charactersRegexCheck,
    whitespaceRegex: whitespaceRegexCheck,
    numberRegex: numberRegexCheck,
    specialRegex: specialRegexCheck,
    uppercaseRegex: uppercaseRegexCheck,
    lowercaseRegex: lowercaseRegexCheck,
  };
}
