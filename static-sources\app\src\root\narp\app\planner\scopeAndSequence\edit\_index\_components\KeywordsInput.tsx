import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaPlus, FaXmark } from "react-icons/fa6";
import {
  FocusEvent,
  FormEvent,
  KeyboardEvent,
  MouseEvent,
  useRef,
  useState,
} from "react";

export default function KeywordsInput() {
  const {
    outline: { keywords, addKeyword },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState("");

  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleKeyDown(e: KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter") {
      e.preventDefault();
      addKeywordAction();
    }
  }

  function handleBlur(e: FocusEvent) {
    // Only clear if focus goes outside the entire keyword input area
    if (
      e.relatedTarget === buttonRef.current ||
      e.relatedTarget === inputRef.current
    )
      return;
    // Don't revert the input value - keep what user typed
  }

  function addKeywordAction() {
    if (displayValue == "" || !displayValue) return;
    addKeyword(displayValue);
    setDisplayValue("");
    // Focus back to input for easy multiple keyword entry
    inputRef.current?.focus();
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    addKeywordAction();
  }

  return (
    <div className="flex flex-col gap-1">
      <span className="font-bold">Keywords</span>
      <div className="flex flex-col w-full max-w-screen-md border border-neutral-300 rounded">
        <div className="flex flex-row flex-wrap gap-1 min-h-12 border-b border-b-neutral-300 p-1">
          {keywords.map((keyword, index) => (
            <KeywordEntry keyword={keyword} index={index} key={index} />
          ))}
        </div>
        <div className="flex flex-row gap-1 p-1">
          <input
            tabIndex={0}
            ref={inputRef}
            onBlur={handleBlur}
            onInput={handleInput}
            onKeyDown={handleKeyDown}
            value={displayValue}
            type="text"
            autoComplete="off"
            placeholder="New Keyword (Press Enter to add)"
            className="input input-lg grow"
          />
          <div className="tooltip" data-tip="Add Keyword (Enter)">
            <button
              tabIndex={0}
              ref={buttonRef}
              type="button"
              onBlur={handleBlur}
              onClick={handleClick}
              className="btn btn-lg btn-square btn-primary btn-outline"
            >
              <FaPlus />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function KeywordEntry({ keyword, index }: { keyword: string; index: number }) {
  const {
    outline: { removeKeyword },
  } = useEditorContext();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    removeKeyword(index);
  }

  return (
    <div className="size-fit flex flex-row p-1 ps-3 gap-1 border border-neutral-300 rounded-full group">
      <span>{keyword}</span>
      <button
        tabIndex={0}
        type="button"
        onClick={handleClick}
        className="btn btn-ghost btn-circle btn-xs"
      >
        <FaXmark />
      </button>
    </div>
  );
}
