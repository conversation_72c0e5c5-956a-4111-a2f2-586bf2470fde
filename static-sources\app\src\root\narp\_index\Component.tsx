import { useAuth } from "@/util/auth/AuthContext.tsx";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";

export function Component() {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    isAuthenticated().then((authenticated) => {
      authenticated ? navigate("/narp/app") : navigate("/narp/auth");
    });
  }, []);

  return <PageSkeleton />;
}
