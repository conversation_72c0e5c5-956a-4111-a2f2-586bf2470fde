import {
  Home,
  Upload,
  Database,
  Download,
  Bar<PERSON>hart,
  Folder,
  Users,
  Settings,
} from "lucide-react";
import { NavLink } from "react-router-dom";

export default function SideMenu() {
  const menuItems = [
    {
      icon: <Home size={20} />,
      label: "Dashboard",
      path: "/narp/app/lor/dashboard",
    },
    {
      icon: <Upload size={20} />,
      label: "Upload & Import",
      path: "/narp/app/lor/import",
    },
    {
      icon: <Database size={20} />,
      label: "Repository",
      path: "/narp/app/lor/repository",
    },
    {
      icon: <Download size={20} />,
      label: "Export",
      path: "/narp/app/lor/export",
    },
    {
      icon: <BarChart size={20} />,
      label: "Analytics",
      path: "/narp/app/lor/analytics",
    },
    {
      icon: <Folder size={20} />,
      label: "Organization",
      path: "/narp/app/lor/organization",
    },
    {
      icon: <Users size={20} />,
      label: "User Management",
      path: "/narp/app/lor/users",
    },
    {
      icon: <Settings size={20} />,
      label: "Accessibility",
      path: "/narp/app/lor/accessibility",
    },
  ];

  return (
    <div className="w-64 h-full bg-white border-r border-neutral-200">
      <nav className="flex flex-col pt-6">
        {menuItems.map((item, i) => (
          <NavLink
            key={i}
            to={item.path}
            className={({ isActive }) =>
              [
                "flex items-center gap-3 h-12 px-6 transition-all",
                isActive
                  ? "bg-primary-50 text-primary-700 font-semibold border-r-2 border-primary-600"
                  : "text-neutral-700 hover:bg-neutral-100",
              ].join(" ")
            }
          >
            {item.icon}
            <span className="text-base">{item.label}</span>
          </NavLink>
        ))}
      </nav>
    </div>
  );
}
