import { NavLink, useNavigate } from "react-router-dom";
import {
  <PERSON>a<PERSON>ser,
  FaQuestionCircle,
  FaTimes,
  FaCheckCircle,
  FaUserShield,
  FaGlobe,
} from "react-icons/fa";
import { FaEnvelope, Fa<PERSON><PERSON> } from "react-icons/fa6";
import { FormEvent, useState } from "react";
import { check } from "@/util/password.ts";
import { SignUpStatus, useAuth } from "@/util/auth/AuthContext.tsx";

export function Component() {
  const { signUp } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [showHelp, setShowHelp] = useState<boolean>(false);

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const username = data.get("username")?.toString();
    const email = data.get("email")?.toString();
    const password = data.get("password")?.toString();
    const passwordConfirm = data.get("passwordConfirm")?.toString();

    if (
      username === undefined ||
      password === undefined ||
      passwordConfirm === undefined ||
      email === undefined
    )
      return setError(new Error("Missing fields"));
    if (password !== passwordConfirm)
      setError(new Error("Password does not match with Confirm"));
    if (!check(password))
      setError(
        new Error("Password does not meet minimum security requirements"),
      );

    setLoading(true);
    const [status, error] = await signUp(username, password, email);
    setLoading(false);

    if (error) return setError(error);

    switch (status) {
      case SignUpStatus.Success:
        return navigate("/narp/auth");
      case SignUpStatus.Error:
        return setError(error);
      case SignUpStatus.ConfirmRequired:
        return navigate(`/narp/auth/verify/${username}`);
      default:
        return setError(new Error("Invalid signup status"));
    }
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5">
      <form
        onSubmit={handleSubmit}
        className="max-w-xs w-full p-5 flex flex-col gap-3"
      >
        <input hidden readOnly name={"redirect"} value={"true"} />
        <div className="flex items-center justify-center gap-2 text-xl font-bold my-3">
          <span>Register</span>
          <div className="relative tooltip" data-tip={"Help"}>
            <button
              type="button"
              onClick={() => setShowHelp(true)}
              className="btn btn-ghost btn-xs btn-circle text-base-content/60 hover:text-primary hover:bg-primary/10 transition-all duration-300 hover:scale-110"
              title="How to register with AYODE"
            >
              <FaQuestionCircle className="text-sm" />
            </button>
          </div>
        </div>
        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Username</span>
            <FaUser className={"text-base-content/20"} />
            <input
              type={"text"}
              required
              name={"username"}
              autoComplete={"username"}
              placeholder={"Username"}
            />
          </label>
          <p className="hidden validator-hint">Required</p>
        </fieldset>
        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Email</span>
            <FaEnvelope className={"text-base-content/20"} />
            <input
              type={"email"}
              required
              name={"email"}
              autoComplete={"email"}
              placeholder={"Email"}
            />
          </label>
          <p className="hidden validator-hint">Enter valid email address</p>
        </fieldset>
        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Password</span>
            <FaKey className={"text-base-content/20"} />
            <input
              type={"password"}
              required
              name={"password"}
              autoComplete={"new-password"}
              placeholder={"Password"}
              minLength={8}
              pattern={`/^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[$^*.[]{}()?"!@#%&/\\,><':;|_~\`=+- ])[A-Za-z0-9$^*.[]{}()?"!@#%&/\\,><':;|_~\`=+- ]{6,256}$/`}
              title={
                "Must be more than 8 characters, less than 255 characters, including number, lowercase letter, uppercase letter, and special character"
              }
            />
          </label>
          <p className="hidden validator-hint">
            Must be more than 8 characters including,
            <br />
            At least one special character
            <br />
            At least one lowercase letter
            <br />
            At least one uppercase letter
          </p>
        </fieldset>
        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Confirm Password</span>
            <FaKey className={"text-base-content/20"} />
            <input
              type={"password"}
              required
              name={"passwordConfirm"}
              autoComplete={"new-password"}
              placeholder={"Confirm Password"}
            />
          </label>
          <p className={"hidden validator-hint"}>Required</p>
        </fieldset>
        <button
          disabled={loading}
          type="submit"
          className={`btn ${error ? "btn-error" : "btn-primary"}`}
        >
          {loading ? <div className="loading" /> : <>Register</>}
        </button>
        {error ? (
          <div className="text-error text-center">{error?.message}</div>
        ) : (
          <></>
        )}
        <div className="divider my-0">OR</div>{" "}
        <NavLink className="btn btn-secondary no-animation" to="/narp/auth">
          Back To Sign In
        </NavLink>
      </form>

      {showHelp && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 animate-fadeIn">
          <div className="bg-base-100 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto animate-slideInUp">
            <div className="sticky top-0 bg-gradient-to-r from-primary to-secondary p-6 text-white rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl">
                    <FaUserShield className="text-2xl" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">
                      How to Register with AYODE
                    </h2>
                    <p className="text-white/90">
                      Your step-by-step guide to getting started
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowHelp(false)}
                  className="btn btn-ghost btn-sm btn-circle text-white hover:bg-white/20"
                >
                  <FaTimes className="text-lg" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                {" "}
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    1
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Visit AYODE Academy
                    </h3>
                    <p className="text-base-content/80 mb-2">
                      Go to{" "}
                      <span className="font-mono bg-base-200 px-2 py-1 rounded">
                        codermerlin.academy
                      </span>
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Click Register
                    </h3>
                    <p className="text-base-content/80">
                      Find and click the Register option (you're here now!)
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    3
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Fill Registration Form
                    </h3>
                    <div className="text-base-content/80 space-y-2">
                      <div className="flex items-center gap-2">
                        <FaUser className="text-primary" />
                        <span>
                          Enter your desired <strong>username</strong>
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FaEnvelope className="text-primary" />
                        <span>
                          Enter your <strong>school email address</strong>
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FaKey className="text-primary" />
                        <span>
                          Create a strong <strong>password</strong>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    4
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Confirm Password
                    </h3>
                    <p className="text-base-content/80">
                      Re-enter your password to make sure it matches
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    5
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Email Verification
                    </h3>
                    <p className="text-base-content/80">
                      Check your email for a confirmation code and enter it when
                      prompted
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    6
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Sign In</h3>
                    <p className="text-base-content/80">
                      After verification, you'll be redirected to login. Enter
                      your username and password
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    7
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Multi-Factor Authentication
                    </h3>
                    <div className="text-base-content/80">
                      <p className="mb-2">Enter the code from your MFA app:</p>
                      <div className="flex items-center gap-2 text-sm">
                        <FaUserShield className="text-primary" />
                        <span>Duo, Authenticator, or similar apps</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    8
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">
                      Choose Your Realm
                    </h3>
                    <div className="flex items-center gap-2 text-base-content/80">
                      <FaGlobe className="text-primary" />
                      <span>Select your desired learning environment</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-4 p-4 bg-primary/5 rounded-xl border-l-4 border-primary">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                    9
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1 flex items-center gap-2">
                      <span>You're Ready to Start Creating!</span>
                      <FaCheckCircle className="text-primary" />
                    </h3>
                    <p className="text-base-content/80">
                      Welcome to the AYODE community! Begin your coding journey.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-6 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl">
                <h3 className="font-semibold text-lg mb-3 text-center">
                  Need Help?
                </h3>
                <div className="space-y-3 text-center">
                  <p className="text-base-content/80">
                    If you encounter any issues during registration, we're here
                    to help!
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <a
                      href="mailto:<EMAIL>"
                      className="btn btn-primary btn-sm"
                    >
                      <FaEnvelope />
                      Email Support
                    </a>
                    <a
                      href="https://discourse-apollo.www.codermerlin.academy/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-secondary btn-sm"
                    >
                      <FaGlobe />
                      Discourse Forum
                    </a>
                  </div>
                  <p className="text-sm text-base-content/60">
                    You can log in to the forum using the AYODE account you just
                    created!
                  </p>
                </div>
              </div>

              {/* Close Button */}
              <div className="flex justify-center mt-6">
                <button
                  onClick={() => setShowHelp(false)}
                  className="btn btn-primary"
                >
                  Got It! Let's Register
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
