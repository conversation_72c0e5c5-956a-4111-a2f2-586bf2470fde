import { createContext, useContext } from "react";
import { Topic } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export interface Context {
  scale: number;
  maxScale: number;
  minScale: number;
  setScale: (value: number) => void;

  timestampToPixels: (timestamp: number) => number;
  pixelsToTimestamp: (pixels: number) => number;

  zoomIn: () => void;
  zoomOut: () => void;
}

export interface DragData {
  type: "SET_START" | "SET_DURATION" | "SET_DEPENDENCY";
  topic: Topic;
}

export interface DropData {
  type: "SET_DEPENDENCY";
  topic: Topic;
}

export const Context = createContext<Context>({} as Context);

export function useChartContext() {
  return useContext(Context);
}
