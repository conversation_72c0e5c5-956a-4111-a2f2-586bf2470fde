import * as pdfjsLib from "pdfjs-dist";

// Configure PDF.js worker (back in public directory - standard approach)
pdfjsLib.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";

export const parsePDF = async (file: File): Promise<string> => {
  try {
    console.log("PDF.js version:", pdfjsLib.version);

    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    console.log("PDF file size:", file.size);
    console.log("ArrayBuffer length:", arrayBuffer.byteLength);
    console.log("Uint8Array length:", uint8Array.length);

    // Create loading task with browser-optimized options
    const loadingTask = pdfjsLib.getDocument({
      data: uint8Array,
      useSystemFonts: false,
      isEvalSupported: false,
      disableAutoFetch: true,
      disableStream: true,
      disableRange: true,
      useWorkerFetch: false,
    });

    console.log("PDF loading task created successfully");

    const pdf = await loadingTask.promise;
    console.log("PDF loaded successfully, pages:", pdf.numPages);

    let fullText = "";
    let hasValidText = false;
    let processedPages = 0;

    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Extract text items with improved handling
        const textItems: string[] = [];

        if (
          textContent &&
          textContent.items &&
          Array.isArray(textContent.items)
        ) {
          textContent.items.forEach((item) => {
            if (item && typeof item === "object" && "str" in item && item.str) {
              // Clean up the text and handle encoding properly
              let text = String(item.str);

              // Remove problematic characters and fix encoding
              text = text
                .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, "") // Control characters
                .replace(/\ufeff/g, "") // BOM
                .replace(/\u00a0/g, " ") // Non-breaking space
                .replace(/[\u2000-\u200F]/g, " ") // Unicode spaces
                .replace(/[\u2028-\u2029]/g, "\n") // Line separators
                .trim();

              if (
                text &&
                text.length > 0 &&
                text !== " " &&
                !/^[\s\u00a0]*$/.test(text)
              ) {
                textItems.push(text);
                hasValidText = true;
              }
            }
          });
        }

        // Join text items with proper spacing
        if (textItems.length > 0) {
          const pageText = textItems.join(" ").replace(/\s+/g, " ").trim();
          if (pageText && pageText.length > 0) {
            fullText += `\n\n## Page ${pageNum}\n\n${pageText}`;
            processedPages++;
          }
        }
      } catch (pageError) {
        console.warn(`Error processing page ${pageNum}:`, pageError);
        // Continue processing other pages
      }
    }

    // Clean up the full text
    fullText = fullText.trim();

    if (!hasValidText || fullText.length === 0) {
      return `📄 PDF Processing Complete (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Pages: ${pdf.numPages} (${processedPages} processed successfully)
Parsing Method: PDF.js (Browser)

⚠️ **No readable text found in this PDF**

**Possible reasons:**
- This is a scanned PDF (images only) - try the OCR feature instead
- Text is embedded as graphics/images rather than actual text
- The PDF uses complex formatting that makes text extraction difficult

---
✅ PDF file is valid and was processed successfully
⚠️ No extractable text found - this is common with image-based PDFs`;
    }

    return `📄 PDF Content Extracted Successfully (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Pages: ${pdf.numPages} (${processedPages} with text)
Parsing Method: PDF.js (Browser)

${fullText}

---
✅ Successfully extracted text using Mozilla PDF.js
✅ Content processed and cleaned
✅ No external dependencies required for processing`;
  } catch (error) {
    console.error("PDF parsing error:", error);
    return `❌ PDF Parsing Failed (Bulk Import)

File: ${file.name}
Error: ${error instanceof Error ? error.message : "Unknown error"}

This PDF may have:
• Password protection or security restrictions
• Corrupted or malformed structure
• Complex embedded content that requires specialized handling

Recommended alternatives:
• Try a different PDF file
• Convert to a different format first
• Use server-side processing for complex documents`;
  }
};

export const parseDocx = async (file: File): Promise<string> => {
  try {
    console.log("📝 Attempting DOCX parsing...");
    const mammothModule = await import("mammoth");
    console.log("Mammoth module imported:", !!mammothModule);
    const mammoth = mammothModule.default;
    console.log("Mammoth default export:", !!mammoth);

    const arrayBuffer = await file.arrayBuffer();
    console.log(
      "DOCX file size:",
      file.size,
      "ArrayBuffer length:",
      arrayBuffer.byteLength
    );

    // Extract raw text
    const result = await mammoth.extractRawText({ arrayBuffer });
    const text = result.value.trim();

    if (!text || text.length < 10) {
      throw new Error("No readable text found in document");
    }

    return `📝 Word Document Content Extracted (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Parsing Method: mammoth.js (Client-side)
Text Length: ${text.length} characters

${text}

---
✅ Successfully extracted using mammoth.js
✅ Reliable DOCX text extraction
✅ Preserves document structure and content`;
  } catch (error) {
    console.error("DOCX parsing error:", error);
    throw new Error(
      `Failed to parse DOCX: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

// New function to handle legacy DOC files
export const parseDoc = async (file: File): Promise<string> => {
  return `📝 Legacy Word Document (.doc) - Bulk Import

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Format: Microsoft Word 97-2003 Document

⚠️ **Legacy .doc format detected**

Unfortunately, .doc files (Word 97-2003 format) require server-side processing due to their complex binary structure. This format cannot be reliably parsed in the browser.

**Recommended solutions:**
• Convert to .docx format using Microsoft Word or Google Docs
• Save as PDF and re-upload
• Use online conversion tools to convert .doc to .docx

**Alternative formats supported:**
• .docx (Word 2007+)
• .pdf (Portable Document Format)
• .txt (Plain text)

---
⚠️ File format not supported for client-side processing
💡 Convert to a supported format for text extraction`;
};

export const parsePowerPoint = async (file: File): Promise<string> => {
  try {
    console.log("📊 Attempting PowerPoint parsing...");
    const JSZip = (await import("jszip")).default;

    const arrayBuffer = await file.arrayBuffer();
    console.log(
      "PPTX file size:",
      file.size,
      "ArrayBuffer length:",
      arrayBuffer.byteLength
    );

    const zip = await JSZip.loadAsync(arrayBuffer);

    let extractedText = "";
    let slideCount = 0;

    // Look for slide files in the PPTX structure
    const slideFiles = Object.keys(zip.files)
      .filter((filename) => filename.match(/ppt\/slides\/slide\d+\.xml/))
      .sort((a, b) => {
        const aNum = parseInt(a.match(/slide(\d+)\.xml/)?.[1] || "0");
        const bNum = parseInt(b.match(/slide(\d+)\.xml/)?.[1] || "0");
        return aNum - bNum;
      });

    for (const slideFile of slideFiles) {
      slideCount++;

      try {
        const slideXml = await zip.files[slideFile].async("text");

        // Enhanced text extraction from XML
        const textElements: string[] = [];

        // Extract text from <a:t> elements (main text content)
        const textMatches = slideXml.match(/<a:t[^>]*>(.*?)<\/a:t>/g) || [];
        textMatches.forEach((match) => {
          const text = match
            .replace(/<[^>]*>/g, "") // Remove XML tags
            .replace(/&lt;/g, "<") // Decode HTML entities
            .replace(/&gt;/g, ">")
            .replace(/&amp;/g, "&")
            .replace(/&quot;/g, '"')
            .replace(/&#(\d+);/g, (_match, dec) => String.fromCharCode(dec)) // Decode numeric entities
            .trim();

          if (text && text.length > 0 && !textElements.includes(text)) {
            textElements.push(text);
          }
        });

        if (textElements.length > 0) {
          const slideContent = textElements.join("\n").trim();
          extractedText += `## Slide ${slideCount}\n\n${slideContent}\n\n`;
        } else {
          // If no text found, note it
          extractedText += `## Slide ${slideCount}\n\n[No text content detected]\n\n`;
        }
      } catch (slideError) {
        console.warn(`Error processing slide ${slideCount}:`, slideError);
        extractedText += `## Slide ${slideCount}\n\n[Error processing slide: ${
          slideError instanceof Error ? slideError.message : "Unknown error"
        }]\n\n`;
      }
    }

    extractedText = extractedText.trim();

    if (!extractedText || extractedText.length < 20) {
      return `📊 PowerPoint File Processed (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Parsing Method: JSZip XML extraction (Client-side)
Slides found: ${slideCount}

⚠️ **No readable text found in this PowerPoint file**

**Possible reasons:**
- Slides contain only images, charts, or graphics
- Text is embedded in complex shapes or SmartArt
- The presentation uses advanced formatting that makes text extraction difficult
- This is an older .PPT file (only .PPTX is supported)

---
✅ PowerPoint structure analyzed successfully
⚠️ No extractable text found - try PDF conversion for better results`;
    }

    return `📊 PowerPoint Content Extracted Successfully (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Parsing Method: JSZip XML extraction (Client-side)
Slides processed: ${slideCount}
Text Length: ${extractedText.length} characters

${extractedText}

---
✅ Successfully extracted text using manual XML parsing
✅ Processed entirely in your browser
✅ Pure JavaScript solution - no external dependencies
💡 For better results with complex presentations, try converting to PDF first`;
  } catch (error) {
    console.error("PowerPoint parsing error:", error);
    throw new Error(
      `Failed to parse PowerPoint: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

// New function to handle legacy PPT files
export const parsePpt = async (file: File): Promise<string> => {
  return `📊 Legacy PowerPoint (.ppt) - Bulk Import

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Format: Microsoft PowerPoint 97-2003 Presentation

⚠️ **Legacy .ppt format detected**

Unfortunately, .ppt files (PowerPoint 97-2003 format) require server-side processing due to their complex binary structure. This format cannot be reliably parsed in the browser.

**Recommended solutions:**
• Convert to .pptx format using Microsoft PowerPoint or Google Slides
• Save as PDF and re-upload
• Export slides as images and use OCR

**Alternative formats supported:**
• .pptx (PowerPoint 2007+)
• .pdf (Portable Document Format)
• Images (for OCR text extraction)

---
⚠️ File format not supported for client-side processing
💡 Convert to a supported format for text extraction`;
};

export const parseImage = async (file: File): Promise<string> => {
  try {
    console.log("🖼️ Attempting image OCR...");
    const tesseractModule = await import("tesseract.js");
    console.log("Tesseract module imported:", !!tesseractModule);
    const Tesseract = tesseractModule.default;
    console.log("Tesseract default export:", !!Tesseract);

    const fileUrl = URL.createObjectURL(file);
    console.log("Image file URL created:", !!fileUrl);

    const {
      data: { text },
    } = await Tesseract.recognize(fileUrl, "eng");

    // Clean up the object URL
    URL.revokeObjectURL(fileUrl);

    const cleanText = text.trim();

    if (!cleanText || cleanText.length < 5) {
      throw new Error("No readable text detected in image");
    }

    return `🖼️ Image Text Extraction (OCR) - Bulk Import

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
OCR Engine: Tesseract.js (Client-side)
Detected Text Length: ${cleanText.length} characters

${cleanText}

---
✅ Text successfully extracted using Tesseract.js OCR
✅ Processed entirely in your browser
✅ Supports 100+ languages

Note: OCR accuracy depends on image quality, text clarity, and font size.`;
  } catch (error) {
    console.error("OCR error:", error);
    throw new Error(
      `Failed to parse image: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

export const parseTextFile = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const text = reader.result as string;
      resolve(`📄 Text File Content (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Character Count: ${text.length}

${text}

---
✅ Plain text file loaded successfully`);
    };
    reader.onerror = () => {
      reject(new Error(`Failed to read text file: ${file.name}`));
    };
    reader.readAsText(file);
  });
};

// New function to handle JSON files with formatting
export const parseJsonFile = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      try {
        const text = reader.result as string;
        
        // Try to parse and format JSON
        let formattedContent = text;
        let isValidJson = false;
        
        try {
          const jsonData = JSON.parse(text);
          formattedContent = JSON.stringify(jsonData, null, 2);
          isValidJson = true;
        } catch (jsonError) {
          // If JSON parsing fails, treat as plain text
          formattedContent = text;
        }

        resolve(`📄 JSON File Content (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Character Count: ${text.length}
Format: ${isValidJson ? "Valid JSON" : "Plain text (Invalid JSON)"}

${formattedContent}

---
${isValidJson ? "✅ Valid JSON structure detected and formatted" : "⚠️ Invalid JSON format - displayed as plain text"}
✅ File content loaded successfully`);
      } catch (error) {
        reject(new Error(`Failed to read JSON file: ${file.name}`));
      }
    };
    reader.onerror = () => {
      reject(new Error(`Failed to read JSON file: ${file.name}`));
    };
    reader.readAsText(file);
  });
};

// New function to handle CSV files
export const parseCsvFile = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      try {
        const text = reader.result as string;
        const lines = text.split('\n');
        const rowCount = lines.length;
        const columnCount = lines[0] ? lines[0].split(',').length : 0;

        resolve(`📊 CSV File Content (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Rows: ${rowCount}
Columns: ${columnCount}
Character Count: ${text.length}

${text}

---
✅ CSV data loaded successfully
📊 Detected ${rowCount} rows and ${columnCount} columns`);
      } catch (error) {
        reject(new Error(`Failed to read CSV file: ${file.name}`));
      }
    };
    reader.onerror = () => {
      reject(new Error(`Failed to read CSV file: ${file.name}`));
    };
    reader.readAsText(file);
  });
};

// New function to handle Markdown files
export const parseMarkdownFile = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const text = reader.result as string;
      
      // Count markdown elements
      const headingCount = (text.match(/^#+\s/gm) || []).length;
      const linkCount = (text.match(/\[.*?\]\(.*?\)/g) || []).length;
      const codeBlockCount = (text.match(/```[\s\S]*?```/g) || []).length;

      resolve(`📝 Markdown File Content (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Character Count: ${text.length}
Headings: ${headingCount}
Links: ${linkCount}
Code Blocks: ${codeBlockCount}

${text}

---
✅ Markdown content loaded successfully
📝 Document structure analyzed and preserved`);
    };
    reader.onerror = () => {
      reject(new Error(`Failed to read Markdown file: ${file.name}`));
    };
    reader.readAsText(file);
  });
};

// New function to handle RTF files (basic text extraction)
export const parseRtfFile = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      try {
        const text = reader.result as string;
        
        // Basic RTF text extraction (remove RTF control codes)
        let cleanText = text
          .replace(/\\[a-z]+\d*\s?/g, '') // Remove RTF control words
          .replace(/[{}]/g, '') // Remove braces
          .replace(/\\\\/g, '\\') // Fix escaped backslashes
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();

        if (!cleanText || cleanText.length < 10) {
          // If cleaning failed, show raw content
          cleanText = text;
        }

        resolve(`📝 RTF File Content (Bulk Import)

File: ${file.name} (${(file.size / 1024).toFixed(1)} KB)
Format: Rich Text Format
Character Count: ${cleanText.length}

${cleanText}

---
✅ RTF content processed (basic text extraction)
⚠️ Formatting and advanced features not preserved
💡 For better results, convert to .docx or .pdf format`);
      } catch (error) {
        reject(new Error(`Failed to parse RTF file: ${file.name}`));
      }
    };
    reader.onerror = () => {
      reject(new Error(`Failed to read RTF file: ${file.name}`));
    };
    reader.readAsText(file);
  });
}; 