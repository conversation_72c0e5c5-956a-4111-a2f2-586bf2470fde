import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FormEvent } from "react";
import { GradeLevel } from "@/util/standards.ts";

export default function GradeLevelInput() {
  const {
    outline: { gradeLevel, setGradeLevel },
  } = useEditorContext();

  function handleInput(e: FormEvent<HTMLSelectElement>) {
    e.preventDefault();
    setGradeLevel(parseInt(e.currentTarget.value) as GradeLevel);
  }

  return (
    <label className="flex flex-col gap-1">
      <span className="font-bold">Grade Level</span>
      <select
        onInput={handleInput}
        value={gradeLevel}
        className="select select-lg w-full max-w-screen-md"
      >
        <option defaultChecked hidden>
          Select Grade Level
        </option>
        <option value={GradeLevel.KINDERGARTEN}>Kindergarten</option>
        <option value={GradeLevel.FIRST_GRADE}>First Grade</option>
        <option value={GradeLevel.SECOND_GRADE}>Second Grade</option>
        <option value={GradeLevel.THIRD_GRADE}>Third Grade</option>
        <option value={GradeLevel.FOURTH_GRADE}>Fourth Grade</option>
        <option value={GradeLevel.FIFTH_GRADE}>Fifth Grade</option>
        <option value={GradeLevel.SIXTH_GRADE}>Sixth Grade</option>
        <option value={GradeLevel.SEVENTH_GRADE}>Seventh Grade</option>
        <option value={GradeLevel.EIGHTH_GRADE}>Eighth Grade</option>
        <option value={GradeLevel.NINTH_GRADE}>Ninth Grade</option>
        <option value={GradeLevel.TENTH_GRADE}>Tenth Grade</option>
        <option value={GradeLevel.ELEVENTH_GRADE}>Eleventh Grade</option>
        <option value={GradeLevel.TWELFTH_GRADE}>Twelfth Grade</option>
      </select>
    </label>
  );
}
