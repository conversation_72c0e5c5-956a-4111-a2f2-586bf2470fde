import {
  Goal,
  GoalPriority,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FormEvent } from "react";
import titleize from "titleize";

export default function PriorityField({ goal }: { goal: Goal }) {
  const {
    goal: { setPriority },
  } = useEditorContext();

  function handleInput(e: FormEvent<HTMLSelectElement>) {
    e.preventDefault();
    if (
      !goal ||
      e.currentTarget.value == "" ||
      !e.currentTarget.value ||
      e.currentTarget.value === goal.priority
    )
      return;

    setPriority(goal.id, e.currentTarget.value as GoalPriority);
  }

  return (
    <div className="flex flex-col gap-1">
      <div className="font-bold">Priority</div>
      <select
        tabIndex={0}
        className="select select-lg w-full"
        autoComplete="off"
        onInput={handleInput}
        value={goal?.priority}
      >
        <option value="" defaultChecked hidden>
          Select Category
        </option>
        <option value={GoalPriority.OPTIONAL}>
          {titleize(GoalPriority.OPTIONAL)}
        </option>
        <option value={GoalPriority.NICE_TO_HAVE}>
          {titleize(GoalPriority.NICE_TO_HAVE)}
        </option>
        <option value={GoalPriority.MUST_HAVE}>
          {titleize(GoalPriority.MUST_HAVE)}
        </option>
      </select>
    </div>
  );
}
