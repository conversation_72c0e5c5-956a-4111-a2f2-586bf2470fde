import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import BreadCrumbs from "@/util/components/bread-crumbs/BreadCrumbs.tsx";
import Crumb from "@/util/components/bread-crumbs/Crumb.tsx";
import { LuCalendarCheck } from "react-icons/lu";
import { useParams } from "react-router-dom";
import NavBar from "@/root/narp/app/planner/scopeAndSequence/edit/_components/NavBar.tsx";
import TitleInput from "@/root/narp/app/planner/scopeAndSequence/edit/_index/_components/TitleInput.tsx";
import AbstractInput from "@/root/narp/app/planner/scopeAndSequence/edit/_index/_components/AbstractInput.tsx";
import GradeLevelInput from "@/root/narp/app/planner/scopeAndSequence/edit/_index/_components/GradeLevelInput.tsx";
import KeywordsInput from "@/root/narp/app/planner/scopeAndSequence/edit/_index/_components/KeywordsInput.tsx";

export function Component() {
  const { id } = useParams();

  const {
    outline: { title },
  } = useEditorContext();

  return (
    <div className="size-full overflow-y-auto overflow-x-hidden flex flex-col">
      <div className="flex flex-col gap-3 p-5">
        <BreadCrumbs>
          <Crumb
            icon={<LuCalendarCheck className="size-4" />}
            label="Planner"
            to="/narp/app/planner"
          />
          <Crumb
            label={title}
            base
            active={false}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}`}
          />
        </BreadCrumbs>
        <NavBar />
      </div>

      <div className="px-5 pb-5 flex flex-col gap-3">
        <TitleInput />
        <AbstractInput />
        <GradeLevelInput />
        <KeywordsInput />
      </div>
    </div>
  );
}
