import {
  ChangeEvent,
  FormEvent,
  MouseEvent,
  useCallback,
  useEffect,
  useState,
} from "react";
import {
  Standard,
  useCreateScopeSequenceContext,
} from "@/root/narp/app/planner/scopeAndSequence/create/_components/Context.tsx";
import { Navigate, useNavigate } from "react-router-dom";
import useGET from "@/util/api/useGET.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";

export function Component() {
  const topRealm = useTopRealm();
  const GET = useGET();
  const { authority, standard, setStandard } = useCreateScopeSequenceContext();
  const pushError = usePushError();

  const [loading, setLoading] = useState<boolean>(true);

  const updateRootStandards = useCallback(async () => {
    if (!standard || !authority) return;

    setLoading(true);
    const { response, error, data } = await GET(
      `/v1/standards/${authority.eidURN}/${standard.urn}`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
        params: [["expansionDepth", "5"]],
      }
    );
    setLoading(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    const envelope: APIEnvelope<Standard[]> = JSON.parse(await data.text());
    setStandard(envelope.data.at(0) || null);
  }, [GET, authority, pushError, topRealm.eidURN, standard, setStandard]);

  useEffect(() => {
    updateRootStandards();
  }, []);

  if (!standard || !authority) return <Navigate to={"../standard"} />;

  if (loading)
    return (
      <div className="grow flex items-center justify-center">
        <div className="loading loading-spinner loading-xl" />
      </div>
    );

  return <Content />;
}

function Content() {
  const { blockCount, setBlockCount } = useCreateScopeSequenceContext();
  const navigate = useNavigate();

  const [continuable, setContinuable] = useState<boolean>(false);

  useEffect(() => {
    setContinuable(!!blockCount && blockCount > 0);
  }, [blockCount]);

  function handleToNext(e: MouseEvent | FormEvent) {
    e.preventDefault();
    if (continuable) {
      navigate("../generate");
    }
  }

  function handleToPrevious(e: MouseEvent) {
    e.preventDefault();
    navigate("../standard");
  }

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    const blockCount = parseInt(e.currentTarget.value);
    if (isNaN(blockCount)) return;

    setBlockCount(blockCount);
  }

  function handleSubmit(e: FormEvent) {
    e.preventDefault();
    handleToNext(e); // Navigate to next page on form submit
  }

  function handleKeyDown(e: React.KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter" && continuable) {
      e.preventDefault();
      navigate("../generate");
    }
  }

  return (
    <>
      <div className="flex flex-row justify-between px-5">
        <div className="flex flex-col gap-1">
          <div className="text-xl font-bold">Customize Your Plan</div>
          <div>
            Customize parameters for your plan. Press Enter to continue.
          </div>
        </div>
      </div>

      <form
        onSubmit={handleSubmit}
        className="flex flex-col gap-3 px-5 mb-auto"
      >
        <label className="floating-label">
          <span>Topic Count</span>
          <input
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            value={blockCount === null ? 0 : blockCount}
            required
            type="number"
            step={1}
            min={1}
            max={24}
            name="blockCount"
            className="input input-lg"
            placeholder="Enter number of topics (1-24)"
            autoFocus
          />
        </label>
      </form>

      <div className="bg-base-100 shadow-lg shadow-black border-t border-t-base-content/20 p-5 gap-3 flex flex-row justify-end">
        <button
          onClick={handleToPrevious}
          type="button"
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Back
        </button>

        <button
          onClick={handleToNext}
          type="button"
          disabled={!continuable}
          className="px-5 py-2 rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-800 cursor-pointer
       disabled:bg-neutral-200 disabled:text-neutral-500 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </>
  );
}
