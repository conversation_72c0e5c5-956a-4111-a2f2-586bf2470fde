import {
  Active,
  DragOverlay,
  DragStartEvent,
  Modifier,
  useDndMonitor,
} from "@dnd-kit/core";
import { ReactNode, useState } from "react";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  FaChevronDown,
  FaDice,
  FaGripLinesVertical,
  FaTrash,
  FaWandMagicSparkles,
  FaXmark,
} from "react-icons/fa6";
import { FaEdit, FaSave } from "react-icons/fa";
import {
  Activity,
  Goal,
  GoalPriority,
  Term,
  Topic,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { DragData } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";
import { LuGoal } from "react-icons/lu";
import { HiOutlineLightBulb } from "react-icons/hi";
import { getLexicalColor } from "@/root/narp/app/planner/_util/getLexicalColor.ts";

export default function Overlay() {
  const [modifiers, setModifiers] = useState<Modifier[]>([]);
  const [active, setActive] = useState<Active | null>(null);

  useDndMonitor({
    onDragStart(event: DragStartEvent) {
      setActive(event.active);

      const dragData = event.active.data.current as DragData | undefined;
      if (!dragData) return;

      if (dragData.type === "TOPIC") {
        setModifiers([restrictToVerticalAxis]);
      } else {
        setModifiers([]);
      }
    },
    onDragEnd() {
      setActive(null);
    },
  });

  const dragData = active?.data.current as DragData | undefined;

  return (
    <DragOverlay modifiers={modifiers} dropAnimation={null}>
      {dragData && active && (
        <div className="rotate-3">
          {dragData.type === "TOPIC" ? (
            <TopicOverlay topic={dragData.topic} active={active} />
          ) : dragData.type === "GOAL" ? (
            <GoalOverlay goal={dragData.goal} active={active} />
          ) : dragData.type === "ACTIVITY" ? (
            <ActivityOverlay activity={dragData.activity} active={active} />
          ) : dragData.type === "TERM" ? (
            <TermOverlay term={dragData.term} active={active} />
          ) : (
            <></>
          )}
        </div>
      )}
    </DragOverlay>
  );
}

function TopicOverlay({ topic, active }: { topic: Topic; active: Active }) {
  return (
    <div
      style={{
        width: active.rect.current.initial?.width,
        height: active.rect.current.initial?.height,
      }}
      className="border-2 border-primary-200 rounded-lg pointer-events-none"
    >
      <div className="flex flex-row items-center p-5 bg-primary-050 gap-3 rounded-lg">
        <div className="w-4 flex items-center justify-center text-xl cursor-grab">
          <FaGripLinesVertical className="shrink-0" />
        </div>

        <div className="grow flex flex-row gap-3">
          <div className="btn btn-lg btn-square btn-primary">
            <FaWandMagicSparkles />
          </div>

          <input
            className="input input-lg grow font-bold"
            readOnly
            value={topic.name}
          />

          <div className="tooltip" data-tip={"Save Edit"}>
            <div className="btn btn-lg btn-square btn-primary disabled">
              <FaSave />
            </div>
          </div>
        </div>

        <div className="tooltip" data-tip="Collapse">
          <div className="btn btn-lg btn-square">
            <FaChevronDown />
          </div>
        </div>
        <div className="btn btn-lg btn-square btn-outline btn-error">
          <FaTrash />
        </div>
      </div>
    </div>
  );
}

function getPriorityChip(priority: GoalPriority): ReactNode {
  return (
    <div
      className={`font-bold px-3 p-1 rounded-full shrink-0 ${priority === GoalPriority.OPTIONAL
          ? "bg-teal-100 text-teal-700"
          : priority === GoalPriority.NICE_TO_HAVE
            ? "bg-indigo-100 text-indigo-700"
            : priority === GoalPriority.MUST_HAVE
              ? "bg-primary-100 text-primary-700"
              : "Unknown Priority"
        }`}
    >
      {priority === GoalPriority.OPTIONAL
        ? "Optional"
        : priority === GoalPriority.NICE_TO_HAVE
          ? "Nice-to-have"
          : priority === GoalPriority.MUST_HAVE
            ? "Must Have"
            : "Unknown Priority"}
    </div>
  );
}

function GoalOverlay({ goal, active }: { goal: Goal; active: Active }) {
  return (
    <div
      style={{
        width: active.rect.current.initial?.width,
        height: active.rect.current.initial?.height,
      }}
      className="bg-white flex flex-row gap-3 items-center p-3 border-1 border-neutral-300 rounded-lg pointer-events-none"
    >
      <div className="h-full cursor-grab">
        <FaGripLinesVertical className="shrink-0 text-neutral-600" />
      </div>

      <div>
        <LuGoal className="size-6 shrink-0 text-neutral-600" />
      </div>

      <div className="flex flex-col grow">
        <div className="font-bold">{goal.type || "No Type"}</div>
        <div>{goal.description}</div>
      </div>

      {getPriorityChip(goal.priority)}

      <div className="tooltip" data-tip="Edit">
        <div className="btn btn-lg btn-square">
          <FaEdit />
        </div>
      </div>

      <div className="tooltip" data-tip="Remove From Topic">
        <div className="btn btn-lg btn-square ">
          <FaXmark />
        </div>
      </div>
    </div>
  );
}

function ActivityOverlay({
  activity,
  active,
}: {
  activity: Activity;
  active: Active;
}) {
  return (
    <div
      style={{
        width: active.rect.current.initial?.width,
        height: active.rect.current.initial?.height,
      }}
      className={`flex flex-row p-3 gap-3 bg-white border border-neutral-300 rounded-lg pointer-events-none`}
    >
      <div className="h-full flex items-center justify-center cursor-grab">
        <FaGripLinesVertical className="text-neutral-600" />
      </div>

      <FaDice className="size-6 shrink-0 text-neutral-600" />

      <div className="flex flex-col grow">
        <div className="font-bold text-lg">{activity.title} :</div>
        <div>🕒 {activity.duration}</div>
        <div>{activity.description}</div>

        <div className="flex flex-col mt-3">
          <div className="font-bold">Deliverables</div>
          <ol className="list list-disc list-inside">
            {activity.deliverables.length > 0 ? (
              activity.deliverables.map((deliverable) => <li>{deliverable}</li>)
            ) : (
              <span>No Deliverables</span>
            )}
          </ol>
        </div>

        <div className="flex flex-col mt-3">
          <div className="font-bold">Resources</div>
          <ol className="list list-disc list-inside">
            {activity.resources.length > 0 ? (
              activity.resources.map((resource) => <li>{resource}</li>)
            ) : (
              <span>No Activities</span>
            )}
          </ol>
        </div>
      </div>

      <div className="tooltip" data-tip="Remove From Topic">
        <div className="btn btn-square">
          <FaXmark />
        </div>
      </div>
    </div>
  );
}

function TermOverlay({ term, active }: { term: Term; active: Active }) {
  return (
    <div
      style={{
        width: active.rect.current.initial?.width,
        height: active.rect.current.initial?.height,
      }}
      className={`pointer-events-none flex flex-row p-3 gap-3 bg-white border border-neutral-300 rounded-lg`}
    >
      <div className="h-full flex items-center justify-center cursor-grab">
        <FaGripLinesVertical className="text-neutral-600" />
      </div>

      <HiOutlineLightBulb className="size-6 shrink-0 text-neutral-600" />

      <div className="flex flex-col gap-1">
        <div className="font-bold">{term.word}</div>
        <div
          style={{ backgroundColor: getLexicalColor(term.category) }}
          className="px-1 rounded size-fit"
        >
          {term.category}
        </div>
        <div>{term.definition}</div>
      </div>
    </div>
  );
}
