import { FocusEvent, FormEvent, useRef, useState } from "react";
import {
  Term,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FaEdit } from "react-icons/fa";

export default function WordField({
  term,
  generating,
}: {
  term: Term;
  generating: boolean;
}) {
  const {
    term: { setWord },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState<string>(term?.word || "");

  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    if (!term) return;
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick() {
    if (!term || displayValue === term.word) return;
    setWord(term.id, displayValue);
  }

  function handleBlur(e: FocusEvent) {
    if (
      !term ||
      e.relatedTarget == buttonRef.current ||
      e.relatedTarget == inputRef.current
    )
      return;
    setDisplayValue(term.word);
  }

  return (
    <div className="flex flex-col">
      <div className="font-bold">Word</div>

      <div className="flex flex-row gap-3">
        <input
          disabled={generating}
          ref={inputRef}
          onBlur={handleBlur}
          onInput={handleInput}
          value={displayValue}
          autoComplete="off"
          className="input input-lg grow"
        />
        <div className="tooltip" data-tip="Confirm Edit">
          <button
            tabIndex={0}
            ref={buttonRef}
            onBlur={handleBlur}
            onClick={handleClick}
            disabled={generating || term?.word === displayValue}
            className={`btn btn-lg btn-square btn-primary`}
          >
            <FaEdit />
          </button>
        </div>
      </div>
    </div>
  );
}
