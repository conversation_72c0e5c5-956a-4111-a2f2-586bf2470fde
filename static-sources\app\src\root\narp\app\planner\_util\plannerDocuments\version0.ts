export enum GoalType {
  KnowledgeGoal,
  SkillGoal,
}

export enum GoalPriority {
  Optional,
  NiceToHave,
  MustHave,
}

export enum GradeLevel {
  PreKindergarten = -1,
  Kindergarten = 0,
  FirstGrade = 1,
  SecondGrade = 2,
  ThirdGrade = 3,
  FourthGrade = 4,
  FifthGrade = 5,
  SixthGrade = 6,
  SeventhGrade = 7,
  EightGrade = 8,
  NinthGrade = 9,
  TenthGrade = 10,
  EleventhGrade = 11,
  TwelfthGrade = 12,
}

export interface Goal {
  type: GoalType;
  description: string;
  priority: GoalPriority;
}

export interface Topic {
  name: string;
  goals: Goal[];
}

export interface ScopeAndSequenceDocument {
  manifest: undefined;
  title: string;
  keywords: string[];
  abstract: string;
  grade: GradeLevel;
  topics: Topic[];
}
