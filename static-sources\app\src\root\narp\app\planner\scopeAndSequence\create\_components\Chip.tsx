import { FaChevronRight } from "react-icons/fa6";
import { ReactNode } from "react";

export interface Chip {
  label: string;
  active: boolean;
  index: number;
}

export function Chips({ children }: { children: ReactNode }) {
  return <div className="flex flex-row items-center gap-5 overflow-auto">{children}</div>;
}

export function Chip({
  label,
  index,
  base = false,
  active = false,
}: {
  label: string;
  index: number;
  base?: boolean;
  active?: boolean;
}) {
  return (
    <>
      <div className="shrink-0 flex flex-row items-center gap-2">
        <div
          className={`${active ? "bg-slate-900 text-gray-50" : "bg-neutral-200 text-neutral-500"} rounded-full size-6 flex items-center justify-center text-center`}
        >
          {index}
        </div>
        <div>{label}</div>
      </div>
      {!base && <FaChevronRight className="shrink-0 text-neutral-500" />}
    </>
  );
}
