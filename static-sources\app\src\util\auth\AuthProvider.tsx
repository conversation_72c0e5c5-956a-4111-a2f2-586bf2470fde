import { <PERSON>actN<PERSON>, useMemo, useState } from "react";
import {
  AuthContext,
  MFAStatus,
  SignInStatus,
  SignUpStatus,
} from "@/util/auth/AuthContext.tsx";
import {
  AuthenticationDetails,
  CognitoUser,
  CognitoUserAttribute,
  CognitoUserPool,
  CognitoUserSession,
  ISignUpResult,
} from "amazon-cognito-identity-js";
import { useConfiguration } from "@/util/configuration/ConfigurationContext.tsx";

export function AuthProvider({ children }: { children: ReactNode }) {
  const configuration = useConfiguration();
  const [user, setUser] = useState<CognitoUser | null>(null);
  const userPool = useMemo(() => {
    return new CognitoUserPool({
      UserPoolId: configuration.userPoolID,
      ClientId: configuration.userPoolClientWebAppID,
      Storage: configuration.cookieStorage,
    });
  }, [configuration]);

  async function signIn(
    username: string,
    password: string,
  ): Promise<[SignInStatus, Error | null]> {
    if (!configuration)
      return [SignInStatus.Error, new Error("missing configuration")];

    const user = new CognitoUser({
      Username: username,
      Pool: userPool,
      Storage: configuration.cookieStorage,
    });
    setUser(user);

    return new Promise((resolve) => {
      const details: AuthenticationDetails = new AuthenticationDetails({
        Username: username,
        Password: password,
      });
      user.authenticateUser(details, {
        onSuccess: (session, userConfirmationNecessary) => {
          if (userConfirmationNecessary)
            return resolve([SignInStatus.ConfirmRequired, null]);

          user.refreshSession(session.getRefreshToken(), (error?: Error) => {
            if (error) return resolve([SignInStatus.Error, error]);
            return resolve([SignInStatus.Success, null]);
          });
        },
        onFailure: (error: Error) => {
          return resolve([SignInStatus.Error, error]);
        },
        newPasswordRequired: () => {
          return resolve([SignInStatus.NewPassword, null]);
        },
        mfaRequired: () => {
          return resolve([SignInStatus.MfaTotp, null]);
        },
        totpRequired: () => {
          return resolve([SignInStatus.MfaTotp, null]);
        },
        mfaSetup: () => {
          return resolve([SignInStatus.MfaTotpSetup, null]);
        },
        selectMFAType: () => {
          return resolve([SignInStatus.MfaSelect, null]);
        },
      });
    });
  }

  async function signUp(
    username: string,
    password: string,
    email: string,
    phone?: string,
  ): Promise<[SignUpStatus, Error | null]> {
    const attributes: CognitoUserAttribute[] = [];
    attributes.push(
      new CognitoUserAttribute({
        Name: "email",
        Value: email,
      }),
    );

    if (phone !== undefined) {
      attributes.push(
        new CognitoUserAttribute({
          Name: "phone_number",
          Value: phone,
        }),
      );
    }

    return new Promise((resolve) => {
      userPool.signUp(
        username,
        password,
        attributes,
        [],
        (err?: Error, result?: ISignUpResult) => {
          if (err) return resolve([SignUpStatus.Error, err]);
          if (!result) throw new Error("Missing Sign Up Result");

          setUser(result.user);
          if (!result.userConfirmed)
            return resolve([SignUpStatus.ConfirmRequired, null]);
          return resolve([SignUpStatus.Success, null]);
        },
      );
    });
  }

  async function verifyAccount(
    code: string,
    username?: string,
  ): Promise<Error | null> {
    if (!configuration) return new Error("missing configuration");

    let curUser = user;
    if (username) {
      curUser = new CognitoUser({
        Username: username,
        Pool: userPool,
        Storage: configuration.cookieStorage,
      });
    }
    if (!curUser) return new Error("missing user");
    setUser(curUser);

    return new Promise((resolve) => {
      curUser.confirmRegistration(code, true, (err?: Error) => {
        resolve(err ? err : null);
      });
    });
  }

  async function associateSoftware(): Promise<[string, null] | [null, Error]> {
    return new Promise((resolve) => {
      if (!user) return resolve([null, new Error("missing user")]);
      user.associateSoftwareToken({
        associateSecretCode: (secret) => {
          return resolve([secret, null]);
        },
        onFailure: (err: Error) => {
          return resolve([null, err]);
        },
      });
    });
  }

  async function verifySoftware(code: string): Promise<Error | null> {
    return new Promise((resolve) => {
      if (!user) return resolve(new Error("missing user"));
      user.verifySoftwareToken(code, "TOTP_Device", {
        onSuccess: (session) => {
          user.refreshSession(session.getRefreshToken(), (err: Error) => {
            return resolve(err);
          });
        },
        onFailure: (err) => resolve(err),
      });
    });
  }

  async function mfa(
    type: "TOTP" | "SMS",
    code: string,
  ): Promise<[MFAStatus, Error | null]> {
    return new Promise((resolve) => {
      if (!user) return [MFAStatus.Error, new Error("missing user")];
      user.sendMFACode(
        code,
        {
          onSuccess: (session, userConfirmationNecessary) => {
            if (userConfirmationNecessary)
              return resolve([MFAStatus.ConfirmRequired, null]);

            user.refreshSession(session.getRefreshToken(), (err) => {
              if (err) return resolve([MFAStatus.Error, err]);
              return resolve([MFAStatus.Success, null]);
            });
          },
          onFailure: (err: Error) => resolve([MFAStatus.Error, err]),
        },
        type == "TOTP" ? "SOFTWARE_TOKEN_MFA" : undefined,
      );
    });
  }

  async function mfaTOTP(code: string): Promise<[MFAStatus, Error | null]> {
    return mfa("TOTP", code);
  }

  async function mfaSMS(code: string): Promise<[MFAStatus, Error | null]> {
    return mfa("SMS", code);
  }

  async function requestPasswordReset(
    username?: string,
  ): Promise<Error | null> {
    if (!configuration) return new Error("missing configuration");

    let curUser = user;
    if (username) {
      curUser = new CognitoUser({
        Username: username,
        Pool: userPool,
        Storage: configuration.cookieStorage,
      });
    }
    if (!curUser) return new Error("missing user");
    setUser(curUser);

    return new Promise((resolve) => {
      if (!user) return resolve(new Error("missing user"));
      user.forgotPassword({
        onSuccess: () => resolve(null),
        onFailure: (err) => resolve(err),
      });
    });
  }

  async function confirmPasswordReset(
    code: string,
    password: string,
    username?: string,
  ): Promise<Error | null> {
    if (!configuration) return new Error("missing configuration");

    let curUser = user;
    if (username) {
      curUser = new CognitoUser({
        Username: username,
        Pool: userPool,
        Storage: configuration.cookieStorage,
      });
    }
    if (!curUser) return new Error("missing user");
    setUser(curUser);

    return new Promise((resolve) => {
      curUser.confirmPassword(code, password, {
        onSuccess: () => resolve(null),
        onFailure: (err) => resolve(err),
      });
    });
  }

  async function signOut() {
    if (!user) return;
    user.signOut();
    setUser(null);
  }

  async function getSession(): Promise<
    [CognitoUserSession, null] | [null, Error]
  > {
    return new Promise((resolve) => {
      const currentUser = userPool.getCurrentUser();
      if (!currentUser)
        return resolve([null, new Error("missing authenticated user")]);
      setUser(currentUser);

      currentUser.getSession(
        (error: Error | null, session: CognitoUserSession | null) => {
          if (error) return resolve([null, error]);
          if (!session)
            return resolve([null, new Error("failed to get session")]);

          if (!session.isValid()) {
            // session expired
            currentUser.refreshSession(
              session.getRefreshToken(),
              (err: Error | null) => {
                if (err) return resolve([null, err]);
                resolve(getSession()); // retry getting session
              },
            );
          } else {
            resolve([session, null]);
          }
        },
      );
    });
  }

  async function isAuthenticated(): Promise<boolean> {
    const [session, error] = await getSession();
    if (error) return false;
    return session != null && session.isValid();
  }

  function getUsername(): string | null {
    return user!.getUsername() || null;
  }

  return (
    <AuthContext.Provider
      value={{
        userPool: userPool,
        isAuthenticated: isAuthenticated,
        signIn: signIn,
        signUp: signUp,
        verifyAccount: verifyAccount,
        associateSoftware: associateSoftware,
        verifySoftware: verifySoftware,
        mfa: mfa,
        mfaTOTP: mfaTOTP,
        mfaSMS: mfaSMS,
        requestPasswordReset: requestPasswordReset,
        confirmPasswordReset: confirmPasswordReset,
        signOut: signOut,
        getSession: getSession,
        getUsername: getUsername,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
