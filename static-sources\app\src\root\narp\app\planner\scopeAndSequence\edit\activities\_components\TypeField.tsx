import {
  Activity,
  ActivityType,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { ChangeEvent } from "react";
import titleize from "titleize";

export default function TypeField({ activity }: { activity: Activity }) {
  const {
    activity: { setType },
  } = useEditorContext();

  function handleChange(e: ChangeEvent<HTMLSelectElement>) {
    setType(activity.id, e.target.value as ActivityType);
  }

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">Type</label>
      <select
        value={activity.type}
        onChange={handleChange}
        className="select select-bordered w-full"
      >
        {Object.values(ActivityType).map((type) => (
          <option key={type} value={type}>
            {titleize(type)}
          </option>
        ))}
      </select>
    </div>
  );
}
