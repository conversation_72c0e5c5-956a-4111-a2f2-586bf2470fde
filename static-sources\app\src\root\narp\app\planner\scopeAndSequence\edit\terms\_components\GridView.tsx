import {
  Entity<PERSON>,
  Term,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { HiOutlineLightBulb } from "react-icons/hi";
import { FaPlus, FaTrash } from "react-icons/fa6";
import { Dispatch, MouseEvent, SetStateAction, useState } from "react";
import { FaEdit } from "react-icons/fa";
import { getLexicalColor } from "@/root/narp/app/planner/_util/getLexicalColor.ts";

export default function GridView({
  entries,
  setEditID,
}: {
  entries: Term[];
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    term: { add },
  } = useEditorContext();

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    add();
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 px-5 shrink-0 gap-3">
      {entries.map((term, index) => (
        <Entry term={term} key={index} setEditID={setEditID} />
      ))}
      <button
        onClick={handleClick}
        type="button"
        className="min-h-32 size-full btn btn-lg btn-success btn-outline rounded-lg"
      >
        <span>Add New Term</span>
        <FaPlus />
      </button>
    </div>
  );
}

function Entry({
  term,
  setEditID,
}: {
  term: Term;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    term: { remove },
  } = useEditorContext();

  const [removeTimeout, setRemoveTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );

  function handleRemoveClick(e: MouseEvent) {
    e.preventDefault();
    if (removeTimeout !== null) {
      remove(term.id);
      clearTimeout(removeTimeout);
      setRemoveTimeout(null);
    } else {
      setRemoveTimeout(
        setTimeout(() => {
          setRemoveTimeout(null);
        }, 1000),
      );
    }
  }

  function handleEditClick(e: MouseEvent) {
    e.preventDefault();
    setEditID(term.id);
  }

  return (
    <div className="p-3 border border-neutral-300 rounded-lg flex flex-row gap-3">
      <HiOutlineLightBulb className="size-6 shrink-0" />

      <div className="flex flex-col grow gap-3">
        <div className="flex flex-row gap-3 items-center">
          <span className="grow text-lg font-bold">{term.word}</span>

          <div className="flex flex-row gap-1">
            <div className="tooltip" data-tip="Edit">
              <button
                type="button"
                onClick={handleEditClick}
                className="btn btn-square btn-sm text-base hover:btn-primary focus-visible:btn-primary hover:text-white focus-visible:text-white"
              >
                <FaEdit />
              </button>
            </div>

            <div
              className="tooltip"
              data-tip={removeTimeout ? "Click Again To Confirm" : "Remove"}
            >
              <button
                type="button"
                onClick={handleRemoveClick}
                className="btn btn-square btn-sm text-base hover:btn-error focus-visible:btn-error hover:text-white focus-visible:text-white"
              >
                <FaTrash />
              </button>
            </div>
          </div>
        </div>

        <div className="flex flex-row gap-3">
          {/*<div className="bg-neutral-200 rounded px-1">/ˈkiˌfɹeɪm/</div>*/}
          <div
            style={{ backgroundColor: getLexicalColor(term.category) }}
            className={`rounded px-1 h-6`}
          >
            {term.category}
          </div>
        </div>

        <div>{term.definition}</div>
      </div>
    </div>
  );
}
