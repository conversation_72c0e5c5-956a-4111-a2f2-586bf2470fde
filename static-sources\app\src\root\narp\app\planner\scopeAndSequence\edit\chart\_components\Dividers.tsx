import { useChartContext } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";
import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { useMemo } from "react";

export default function Dividers() {
  const {
    topic: { entries },
  } = useEditorContext();
  const { scale, timestampToPixels } = useChartContext();

  const fullDuration = useMemo(() => {
    let value = 0;
    for (const entry of entries) {
      if (entry.start + entry.duration > value)
        value = entry.start + entry.duration;
    }
    return value;
  }, [entries]);

  const stepOffset = 1 / scale;

  // Safely calculate array length to prevent Invalid array length errors
  const getValidArrayLength = () => {
    if (!isFinite(fullDuration) || fullDuration < 0) return 0;
    if (!isFinite(stepOffset) || stepOffset <= 0) return 0;

    const length = Math.floor(fullDuration / stepOffset) + 1;

    // Prevent extremely large arrays (cap at 10000 for performance)
    if (!isFinite(length) || length < 0 || length > 10000) return 0;

    return length;
  };

  const arrayLength = getValidArrayLength();

  return (
    <div className="absolute -z-20 size-full flex flex-row">
      {arrayLength > 0 &&
        new Array(arrayLength)
          .fill(null)
          .map((_, index) => (
            <div
              key={index}
              style={{ width: timestampToPixels(stepOffset) + "px" }}
              className={`relative border-e-2 border-e-neutral-200 shrink-0  ${index % 2 === 0 && "bg-neutral-50"}`}
            />
          ))}
    </div>
  );
}
