import {
  Activity,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { useState } from "react";
import { FaPlus, FaTrash } from "react-icons/fa6";

export default function ResourcesField({ activity }: { activity: Activity }) {
  const {
    activity: { setResources },
  } = useEditorContext();

  const [newResource, setNewResource] = useState("");

  function handleAddResource() {
    if (newResource.trim()) {
      setResources(activity.id, [...activity.resources, newResource.trim()]);
      setNewResource("");
    }
  }

  function handleRemoveResource(index: number) {
    const updated = activity.resources.filter((_, i) => i !== index);
    setResources(activity.id, updated);
  }

  function handleKeyPress(e: React.KeyboardEvent) {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddResource();
    }
  }

  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium text-gray-700">Resources</label>

      <div className="flex gap-2">
        <input
          type="text"
          value={newResource}
          onChange={(e) => setNewResource(e.target.value)}
          onKeyPress={handleKeyPress}
          className="input input-bordered flex-1"
          placeholder="Add a resource..."
        />
        <button
          type="button"
          onClick={handleAddResource}
          disabled={!newResource.trim()}
          className="btn btn-square btn-primary"
        >
          <FaPlus />
        </button>
      </div>

      {activity.resources.length > 0 && (
        <div className="flex flex-col gap-2 max-h-32 overflow-y-auto">
          {activity.resources.map((resource, index) => (
            <div
              key={index}
              className="flex items-center gap-2 p-2 bg-gray-50 rounded"
            >
              <span className="flex-1 text-sm">{resource}</span>
              <button
                type="button"
                onClick={() => handleRemoveResource(index)}
                className="btn btn-square btn-xs btn-error btn-outline"
              >
                <FaTrash />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
