import {
  LexicalCategory,
  Term,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { FormEvent } from "react";

export default function LexicalCategoryField({
  term,
  generating,
}: {
  term: Term;
  generating: boolean;
}) {
  const {
    term: { setCategory },
  } = useEditorContext();

  function handleInput(e: FormEvent<HTMLSelectElement>) {
    e.preventDefault();
    if (
      !term ||
      e.currentTarget.value == "" ||
      !e.currentTarget.value ||
      e.currentTarget.value === term.category
    )
      return;

    setCategory(term.id, e.currentTarget.value as LexicalCategory);
  }

  return (
    <div className="flex flex-col gap-1">
      <div className="font-bold">Lexical Category</div>
      <select
        disabled={generating}
        className="select select-lg w-full"
        autoComplete="off"
        onInput={handleInput}
        value={term?.category}
      >
        <option value="" defaultChecked hidden>
          Select Category
        </option>
        <option value={LexicalCategory.NOUN}>noun</option>
        <option value={LexicalCategory.VERB}>verb</option>
        <option value={LexicalCategory.ADJECTIVE}>adjective</option>
        <option value={LexicalCategory.ADVERB}>adverb</option>
        <option value={LexicalCategory.CONJUNCTION}>conjunction</option>
        <option value={LexicalCategory.INTERJECTION}>interjection</option>
        <option value={LexicalCategory.PREPOSITION}>preposition</option>
        <option value={LexicalCategory.PRONOUN}>pronoun</option>
      </select>
    </div>
  );
}
