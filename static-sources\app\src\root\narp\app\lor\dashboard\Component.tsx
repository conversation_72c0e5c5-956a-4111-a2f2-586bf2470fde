import {
  Plus,
  List,
  Grid3X3,
  Image,
  Video,
  File,
  Edit,
  Download,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  T<PERSON>dingUp,
  <PERSON>,
  Clock,
  CheckCircle,

} from "lucide-react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTopRealm } from "../../_components/Context";
import { usePushError, usePushSuccess } from "@/util/components/Alerts/util";
import useGET from "@/util/api/useGET";
import useDELETE from "@/util/api/useDELETE";
import usePOST from "@/util/api/usePOST";

interface UploadItem {
  id: string;
  name: string;
  type: "image" | "video" | "document";
  subject: string;
  grade: string;
  tags: string[];
  createdDate: string;
  author: string;
  size: string;
}

interface DashboardStats {
  totalItems: number;
  totalSize: string;
  thisWeekUploads: number;
  activeUsers: number;
}

const mockUploads: UploadItem[] = [
  {
    id: "1",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "2",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "3",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "4",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "5",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "6",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
];

const mockStats: DashboardStats = {
  totalItems: 1247,
  totalSize: "2.8 GB",
  thisWeekUploads: 23,
  activeUsers: 87,
};

export function Component() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("grid");
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStats>(mockStats);
  const navigate = useNavigate();

  // API hooks
  const topRealm = useTopRealm();
  const GET = useGET();
  const DELETE = useDELETE();
  const POST = usePOST();
  const pushError = usePushError();
  const pushSuccess = usePushSuccess();

  const handleUploadNew = () => {
    navigate("../import");
  };


  const handleContextAnalytics = () => {
    navigate("../analytics");

  // TODO: Implement with real API - Replace with actual API call to fetch dashboard statistics
  // Expected endpoint: GET /v1/lor/stats/${topRealm.externalID}
  const fetchStats = async () => {
    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // TODO: Replace with actual API call:
      // const { response, data, error } = await GET(
      //   `/v1/lor/stats/${topRealm.externalID}`,
      //   {
      //     assertedRealmEidUrn: topRealm.eidURN,
      //   }
      // );
      //
      // if (!response.ok) {
      //   pushError(error?.clientErrorDetail?.userMessage || 'Failed to fetch statistics');
      //   return;
      // }
      //
      // const statsData = JSON.parse(await data.text());
      // setStats(statsData);

      // Using mock data for now
      setStats(mockStats);
    } catch (error) {
      pushError("Failed to fetch dashboard statistics");
    }
  };

  // TODO: Implement with real API - Replace with actual API call to edit LOR item
  // Expected endpoint: PUT /v1/lor/items/${topRealm.externalID}/${itemId}
  const handleEdit = async (itemId: string) => {
    try {
      // TODO: For now, redirect to import page with edit mode
      // In future, this should redirect to a dedicated edit page with item data
      // navigate(`../edit/${itemId}`);

      // Temporary implementation - redirect to import page
      navigate("../import", { state: { editMode: true, itemId } });
      pushSuccess("Redirecting to edit mode...");
    } catch (error) {
      pushError("Failed to open edit mode");
    }
  };

  // TODO: Implement with real API - Replace with actual API call to download LOR item
  // Expected endpoint: GET /v1/lor/items/${topRealm.externalID}/${itemId}/download
  const handleDownload = async (itemId: string, filename: string) => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call:
      // const { response, data, error } = await GET(
      //   `/v1/lor/items/${topRealm.externalID}/${itemId}/download`,
      //   {
      //     assertedRealmEidUrn: topRealm.eidURN,
      //   }
      // );
      //
      // if (!response.ok) {
      //   pushError(error?.clientErrorDetail?.userMessage || 'Failed to download file');
      //   return;
      // }
      //
      // // Create download link
      // const blob = await data.blob();
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = filename;
      // document.body.appendChild(a);
      // a.click();
      // window.URL.revokeObjectURL(url);
      // document.body.removeChild(a);

      // Temporary mock implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      pushSuccess(`Download started for ${filename}`);

      // Mock download - in real implementation, this would trigger actual file download
      console.log(`Downloading file: ${filename} (ID: ${itemId})`);
    } catch (error) {
      pushError("Failed to download file");
    } finally {
      setLoading(false);
    }
  };

  // TODO: Implement with real API - Replace with actual API call to delete LOR item
  // Expected endpoint: DELETE /v1/lor/items/${topRealm.externalID}/${itemId}
  const handleDelete = async (itemId: string) => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call:
      // const { response, error } = await DELETE(
      //   `/v1/lor/items/${topRealm.externalID}/${itemId}`,
      //   {
      //     assertedRealmEidUrn: topRealm.eidURN,
      //   }
      // );
      //
      // if (!response.ok) {
      //   pushError(error?.clientErrorDetail?.userMessage || 'Failed to delete item');
      //   return;
      // }

      // Temporary mock implementation
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Remove from selectedFiles if it was selected
      setSelectedFiles((prev) => prev.filter((id) => id !== itemId));

      pushSuccess("Item deleted successfully");

      // TODO: After successful deletion, refetch the uploads list
      // await fetchUploads();
    } catch (error) {
      pushError("Failed to delete item");
    } finally {
      setLoading(false);
      setDeleteConfirmId(null);
    }
  };

  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles((prev) =>
      prev.includes(fileId)
        ? prev.filter((id) => id !== fileId)
        : [...prev, fileId]
    );
  };

  const isFileSelected = (fileId: string) => selectedFiles.includes(fileId);

  const handleSelectAll = () => {
    if (selectedFiles.length === mockUploads.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(mockUploads.map((upload) => upload.id));
    }
  };

  const isAllSelected = selectedFiles.length === mockUploads.length;
  const isIndeterminate =
    selectedFiles.length > 0 && selectedFiles.length < mockUploads.length;

  const getFileIcon = (type: string) => {
    switch (type) {
      case "image":
        return <Image className="w-5 h-5 text-primary-500" />;
      case "video":
        return <Video className="w-5 h-5 text-red-500" />;
      default:
        return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="w-full h-full flex flex-col bg-neutral-50">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200 px-6 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-neutral-900">
              Dashboard
            </h1>
            <p className="text-sm text-neutral-600 mt-1">
              Centralized hub for managing, organizing, and distributing
              educational content.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center border border-neutral-300 rounded-md overflow-hidden">
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 transition-colors ${
                  viewMode === "list"
                    ? "bg-primary-600 text-white"
                    : "bg-white text-neutral-600 hover:bg-neutral-50"
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 transition-colors ${
                  viewMode === "grid"
                    ? "bg-primary-600 text-white"
                    : "bg-white text-neutral-600 hover:bg-neutral-50"
                }`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
            </div>
            <button
              onClick={handleContextAnalytics}
              className="flex items-center gap-2 px-4 py-2 bg-neutral-100 text-neutral-700 text-sm rounded-md hover:bg-neutral-200 transition-colors shadow-sm border border-neutral-300"
            >
              <BarChart3 className="w-4 h-4" />
              Context Analytics
            </button>
            <button
              onClick={handleUploadNew}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors shadow-sm"
            >
              <Plus className="w-4 h-4" />
              Upload New
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="space-y-6">

          {/* Context Analytics Summary Section */}
          <div className="bg-white rounded-lg border border-neutral-200">
            <div className="px-6 py-4 border-b border-neutral-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-neutral-900">
                  Content Analytics Summary
                </h2>
                <button
                  onClick={handleContextAnalytics}
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  View Full Analysis →
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-700">
                        Total Words
                      </p>
                      <p className="text-xl font-bold text-blue-900">2,790</p>
                    </div>
                    <div className="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                      <File className="w-4 h-4 text-blue-600" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-700">
                        Avg Readability
                      </p>
                      <p className="text-xl font-bold text-green-900">8.4/10</p>
                    </div>
                    <div className="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
                      <BarChart3 className="w-4 h-4 text-green-600" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-700">
                        Content Tone
                      </p>
                      <p className="text-xl font-bold text-purple-900">
                        Educational
                      </p>
                    </div>
                    <div className="w-8 h-8 bg-purple-200 rounded-full flex items-center justify-center">
                      <Edit className="w-4 h-4 text-purple-600" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-yellow-700">
                        Sentiment
                      </p>
                      <p className="text-xl font-bold text-yellow-900">
                        Positive
                      </p>
                    </div>
                    <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center">
                      <Plus className="w-4 h-4 text-yellow-600" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-neutral-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-neutral-700">
                    Most Common Keywords
                  </h3>
                  <span className="text-xs text-neutral-500">Last 7 days</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {[
                    "educational",
                    "learning",
                    "students",
                    "knowledge",
                    "curriculum",
                    "assessment",
                  ].map((keyword) => (
                    <span
                      key={keyword}
                      className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium"
                    >
                      {keyword}
                    </span>
                  ))}
                  <span className="px-3 py-1 bg-neutral-200 text-neutral-600 rounded-full text-xs">
                    +12 more
                  </span>

          {/* Statistics Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-neutral-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">
                    Total Items
                  </p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {stats.totalItems.toLocaleString()}
                  </p>
                </div>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <File className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-neutral-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">
                    Total Size
                  </p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {stats.totalSize}
                  </p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-neutral-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">
                    This Week
                  </p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {stats.thisWeekUploads}
                  </p>
                </div>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-neutral-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">
                    Active Users
                  </p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {stats.activeUsers}
                  </p>
                </div>
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Users className="w-6 h-6 text-orange-600" />

                </div>
              </div>
            </div>
          </div>

          {/* Recent Uploads Section */}
          <div className="bg-white rounded-lg border border-neutral-200">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">
                Recent Uploads
              </h2>
            </div>

            <div className="p-6">
              {viewMode === "grid" ? (
                /* Grid View */
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {mockUploads.map((upload) => (
                    <div
                      key={upload.id}
                      className={`bg-white rounded-lg border p-4 transition-all duration-200 ${
                        isFileSelected(upload.id)
                          ? "border-purple-400 shadow-lg shadow-purple-200/50 ring-2 ring-purple-200"
                          : "border-neutral-200 hover:shadow-md hover:border-neutral-300"
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={isFileSelected(upload.id)}
                            onChange={() => toggleFileSelection(upload.id)}
                            className="rounded border-neutral-300 text-purple-600 focus:ring-purple-500 transition-colors"
                          />
                          {getFileIcon(upload.type)}
                        </div>
                        <div className="flex items-center gap-1">
                          <button
                            onClick={() => handleEdit(upload.id)}
                            className="p-1 text-neutral-400 hover:text-primary-600 transition-colors"
                            title="Edit item"
                          >
                            <Edit className="w-3 h-3" />
                          </button>
                          <button
                            onClick={() =>
                              handleDownload(upload.id, upload.name)
                            }
                            disabled={loading}
                            className="p-1.5 text-neutral-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors disabled:opacity-50"
                            title="Download item"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => setDeleteConfirmId(upload.id)}
                            className="p-1 text-neutral-400 hover:text-red-600 transition-colors"
                            title="Delete item"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      </div>

                      <h3 className="font-medium text-neutral-900 mb-2 text-sm leading-tight">
                        {upload.name}
                      </h3>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-xs text-neutral-600">
                          <span>{upload.subject}</span>
                          <span>•</span>
                          <span>{upload.grade}</span>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {upload.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs"
                            >
                              {tag}
                            </span>
                          ))}
                          <span className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs">
                            +2
                          </span>
                        </div>

                        <div className="text-xs text-neutral-500">
                          Created {upload.createdDate}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                /* List View */
                <div className="space-y-0 border border-neutral-200 rounded-lg overflow-hidden">
                  <div className="grid grid-cols-12 gap-4 p-4 bg-neutral-50 border-b border-neutral-200 text-sm font-medium text-neutral-700">
                    <div className="col-span-1">
                      <input
                        type="checkbox"
                        checked={isAllSelected}
                        ref={(el) => {
                          if (el) el.indeterminate = isIndeterminate;
                        }}
                        onChange={handleSelectAll}
                        className="rounded border-neutral-300 text-purple-600 focus:ring-purple-500"
                      />
                    </div>
                    <div className="col-span-5">Name</div>
                    <div className="col-span-2">Subject</div>
                    <div className="col-span-1">Grade</div>
                    <div className="col-span-2">Created</div>
                    <div className="col-span-1">Actions</div>
                  </div>

                  {mockUploads.map((upload) => (
                    <div
                      key={upload.id}
                      className={`grid grid-cols-12 gap-4 p-4 border-b transition-all duration-200 last:border-b-0 ${
                        isFileSelected(upload.id)
                          ? "bg-purple-50 border-purple-200 shadow-md shadow-purple-100/50"
                          : "border-neutral-100 hover:bg-neutral-50"
                      }`}
                    >
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={isFileSelected(upload.id)}
                          onChange={() => toggleFileSelection(upload.id)}
                          className="rounded border-neutral-300 text-purple-600 focus:ring-purple-500"
                        />
                      </div>
                      <div className="col-span-5 flex items-center gap-3">
                        {getFileIcon(upload.type)}
                        <span className="text-sm font-medium text-neutral-900 truncate">
                          {upload.name}
                        </span>
                      </div>
                      <div className="col-span-2 text-sm text-neutral-600">
                        {upload.subject}
                      </div>
                      <div className="col-span-1 text-sm text-neutral-600">
                        {upload.grade}
                      </div>
                      <div className="col-span-2 text-sm text-neutral-500">
                        {upload.createdDate}
                      </div>
                      <div className="col-span-1 flex items-center gap-1">
                        <button
                          onClick={() => handleEdit(upload.id)}
                          className="p-1 text-neutral-400 hover:text-primary-600 transition-colors"
                          title="Edit item"
                        >
                          <Edit className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => handleDownload(upload.id, upload.name)}
                          disabled={loading}
                          className="p-1.5 text-neutral-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors disabled:opacity-50"
                          title="Download item"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => setDeleteConfirmId(upload.id)}
                          className="p-1 text-neutral-400 hover:text-red-600 transition-colors"
                          title="Delete item"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirmId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-neutral-900 mb-4">
              Confirm Delete
            </h3>
            <p className="text-neutral-600 mb-6">
              Are you sure you want to delete this item? This action cannot be
              undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setDeleteConfirmId(null)}
                className="px-4 py-2 text-neutral-600 border border-neutral-300 rounded-md hover:bg-neutral-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDelete(deleteConfirmId)}
                disabled={loading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {loading ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
