import {
  Plus,
  List,
  Grid3X3,
  Image,
  Video,
  File,
  Edit,
  Download,
  Trash2,
} from "lucide-react";
import { useState } from "react";

interface UploadItem {
  id: string;
  name: string;
  type: "image" | "video" | "document";
  subject: string;
  grade: string;
  tags: string[];
  createdDate: string;
  author: string;
  size: string;
}

const mockUploads: UploadItem[] = [
  {
    id: "1",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "<PERSON><PERSON> <PERSON>",
    size: "2.4 MB",
  },
  {
    id: "2",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "<PERSON><PERSON> <PERSON>",
    size: "2.4 MB",
  },
  {
    id: "3",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "<PERSON><PERSON> <PERSON>",
    size: "2.4 MB",
  },
  {
    id: "4",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "5",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
  {
    id: "6",
    name: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    type: "image",
    subject: "Foreign Language",
    grade: "8",
    tags: ["resource", "materials"],
    createdDate: "June 8, 2025",
    author: "Dr. Martinez",
    size: "2.4 MB",
  },
];

export function Component() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("grid");

  const getFileIcon = (type: string) => {
    switch (type) {
      case "image":
        return <Image className="w-5 h-5 text-primary-500" />;
      case "video":
        return <Video className="w-5 h-5 text-red-500" />;
      default:
        return <File className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="w-full h-full flex flex-col bg-neutral-50">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200 px-6 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-neutral-900">
              Learning Object Repository (LOR)
            </h1>
            <p className="text-sm text-neutral-600 mt-1">
              Centralized hub for managing, organizing, and distributing
              educational content.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center border border-neutral-300 rounded-md overflow-hidden">
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 transition-colors ${
                  viewMode === "list"
                    ? "bg-primary-600 text-white"
                    : "bg-white text-neutral-600 hover:bg-neutral-50"
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 transition-colors ${
                  viewMode === "grid"
                    ? "bg-primary-600 text-white"
                    : "bg-white text-neutral-600 hover:bg-neutral-50"
                }`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
            </div>
            <button className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors shadow-sm">
              <Plus className="w-4 h-4" />
              Upload New
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="space-y-6">
          {/* Recent Uploads Section */}
          <div className="bg-white rounded-lg border border-neutral-200">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">
                Recent Uploads
              </h2>
            </div>

            <div className="p-6">
              {viewMode === "grid" ? (
                /* Grid View */
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {mockUploads.map((upload) => (
                    <div
                      key={upload.id}
                      className="bg-white rounded-lg border border-neutral-200 p-4 hover:shadow-md transition-all duration-200 hover:border-neutral-300"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500 transition-colors"
                          />
                          {getFileIcon(upload.type)}
                        </div>
                        <div className="flex items-center gap-1">
                          <button className="p-1 text-neutral-400 hover:text-primary-600 transition-colors">
                            <Edit className="w-3 h-3" />
                          </button>
                          <button className="p-1.5 text-neutral-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors">
                            <Download className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-neutral-400 hover:text-red-600 transition-colors">
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      </div>

                      <h3 className="font-medium text-neutral-900 mb-2 text-sm leading-tight">
                        {upload.name}
                      </h3>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-xs text-neutral-600">
                          <span>{upload.subject}</span>
                          <span>•</span>
                          <span>{upload.grade}</span>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {upload.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs"
                            >
                              {tag}
                            </span>
                          ))}
                          <span className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs">
                            +2
                          </span>
                        </div>

                        <div className="text-xs text-neutral-500">
                          Created {upload.createdDate}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                /* List View */
                <div className="space-y-0 border border-neutral-200 rounded-lg overflow-hidden">
                  <div className="grid grid-cols-12 gap-4 p-4 bg-neutral-50 border-b border-neutral-200 text-sm font-medium text-neutral-700">
                    <div className="col-span-1">
                      <input
                        type="checkbox"
                        className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                      />
                    </div>
                    <div className="col-span-5">Name</div>
                    <div className="col-span-2">Subject</div>
                    <div className="col-span-1">Grade</div>
                    <div className="col-span-2">Created</div>
                    <div className="col-span-1">Actions</div>
                  </div>

                  {mockUploads.map((upload) => (
                    <div
                      key={upload.id}
                      className="grid grid-cols-12 gap-4 p-4 border-b border-neutral-100 hover:bg-neutral-50 transition-colors last:border-b-0"
                    >
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                        />
                      </div>
                      <div className="col-span-5 flex items-center gap-3">
                        {getFileIcon(upload.type)}
                        <span className="text-sm font-medium text-neutral-900 truncate">
                          {upload.name}
                        </span>
                      </div>
                      <div className="col-span-2 text-sm text-neutral-600">
                        {upload.subject}
                      </div>
                      <div className="col-span-1 text-sm text-neutral-600">
                        {upload.grade}
                      </div>
                      <div className="col-span-2 text-sm text-neutral-500">
                        {upload.createdDate}
                      </div>
                      <div className="col-span-1 flex items-center gap-1">
                        <button className="p-1 text-neutral-400 hover:text-primary-600 transition-colors">
                          <Edit className="w-3 h-3" />
                        </button>
                        <button className="p-1.5 text-neutral-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors">
                          <Download className="w-4 h-4" />
                        </button>
                        <button className="p-1 text-neutral-400 hover:text-red-600 transition-colors">
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
