"use client";

import { useState, useCallback, useMemo } from "react";
import * as pdfjsLib from "pdfjs-dist";
import mammoth from "mammoth";

// Set the worker source
pdfjsLib.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";

interface ExtractedText {
  id: string;
  fileName: string;
  content: string;
  wordCount: number;
  charCount: number;
  format: string;
  size: number;
  extractedAt: Date;
}

interface ExtractedImage {
  id: string;
  pageNumber: number;
  imageIndex: number;
  dataUrl: string;
  width: number;
  height: number;
  format: string;
  size: number;
  extractedAt: Date;
}

interface ProcessedPdf {
  id: string;
  name: string;
  size: number;
  pageCount: number;
  extractedImages: ExtractedImage[];
  extractedTexts: ExtractedText[];
  uploadedAt: Date;
  processingComplete: boolean;
}

interface PdfImageExtractorComponentProps {
  onPdfProcessed?: (pdf: ProcessedPdf) => void;
  onImageExtracted?: (image: ExtractedImage) => void;
  onTextExtracted?: (text: ExtractedText) => void;
}

export default function PdfImageExtractorComponent({
  onPdfProcessed,
  onImageExtracted,
  onTextExtracted,
}: PdfImageExtractorComponentProps) {
  const [processedPdfs, setProcessedPdfs] = useState<ProcessedPdf[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [progress, setProgress] = useState<{ [key: string]: number }>({});
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedTexts, setSelectedTexts] = useState<string[]>([]);
  const [processingStatus, setProcessingStatus] = useState<{
    [key: string]: string;
  }>({});

  const supportedTypes = useMemo(
    () => [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
      "application/msword", // .doc
      "text/plain", // .txt
      "text/rtf", // .rtf
      "application/rtf", // .rtf
    ],
    []
  );

  const extractImagesFromPdf = useCallback(
    async (file: File) => {
      const pdfId = `pdf_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      try {
        setProcessingStatus((prev) => ({
          ...prev,
          [pdfId]: "Reading PDF file...",
        }));

        const arrayBuffer = await file.arrayBuffer();
        const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

        const processedPdf: ProcessedPdf = {
          id: pdfId,
          name: file.name,
          size: file.size,
          pageCount: pdf.numPages,
          extractedImages: [],
          extractedTexts: [],
          uploadedAt: new Date(),
          processingComplete: false,
        };

        setProcessedPdfs((prev) => [...prev, processedPdf]);

        let totalImageCount = 0;

        // Process each page to extract embedded images
        for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
          setProcessingStatus((prev) => ({
            ...prev,
            [pdfId]: `Scanning page ${pageNum} of ${pdf.numPages} for images...`,
          }));

          setProgress((prev) => ({
            ...prev,
            [pdfId]: (pageNum / pdf.numPages) * 100,
          }));

          try {
            const page = await pdf.getPage(pageNum);

            // Get operator list to find image operations
            const operatorList = await page.getOperatorList();

            // Process each operation to find images
            for (let i = 0; i < operatorList.fnArray.length; i++) {
              const fnId = operatorList.fnArray[i];
              const args = operatorList.argsArray[i];

              // Check if this operation is painting an image
              if (fnId === pdfjsLib.OPS.paintImageXObject) {
                try {
                  const imageName = args[0];

                  // Get the image object from the page
                  const imageObj = page.objs.get(imageName);

                  if (imageObj) {
                    // Wait for the image to be loaded
                    await new Promise((resolve) => {
                      if (page.objs.has(imageName)) {
                        resolve(page.objs.get(imageName));
                      } else {
                        page.objs.get(imageName, resolve);
                      }
                    });

                    const imgData = page.objs.get(imageName);

                    if (
                      imgData &&
                      imgData.data &&
                      imgData.width &&
                      imgData.height
                    ) {
                      // Create canvas to convert image data to displayable format
                      const canvas = document.createElement("canvas");
                      const ctx = canvas.getContext("2d");

                      if (ctx) {
                        canvas.width = imgData.width;
                        canvas.height = imgData.height;

                        // Create ImageData object
                        const imageData = ctx.createImageData(
                          imgData.width,
                          imgData.height
                        );

                        // Convert image data based on its format
                        if (imgData.kind === 1) {
                          // Grayscale
                          for (
                            let j = 0, k = 0;
                            j < imgData.data.length;
                            j++, k += 4
                          ) {
                            const gray = imgData.data[j];
                            imageData.data[k] = gray; // R
                            imageData.data[k + 1] = gray; // G
                            imageData.data[k + 2] = gray; // B
                            imageData.data[k + 3] = 255; // A
                          }
                        } else if (imgData.kind === 2) {
                          // RGB
                          for (
                            let j = 0, k = 0;
                            j < imgData.data.length;
                            j += 3, k += 4
                          ) {
                            imageData.data[k] = imgData.data[j]; // R
                            imageData.data[k + 1] = imgData.data[j + 1]; // G
                            imageData.data[k + 2] = imgData.data[j + 2]; // B
                            imageData.data[k + 3] = 255; // A
                          }
                        } else if (imgData.kind === 3) {
                          // RGBA
                          for (let j = 0; j < imgData.data.length; j++) {
                            imageData.data[j] = imgData.data[j];
                          }
                        }

                        // Draw the image data to canvas
                        ctx.putImageData(imageData, 0, 0);

                        // Convert to data URL
                        const dataUrl = canvas.toDataURL("image/png");

                        // Only extract if it's a reasonable size (filter out tiny images like bullets, etc.)
                        if (imgData.width >= 16 && imgData.height >= 16) {
                          const extractedImage: ExtractedImage = {
                            id: `img_${pdfId}_${pageNum}_${totalImageCount}`,
                            pageNumber: pageNum,
                            imageIndex: totalImageCount,
                            dataUrl,
                            width: imgData.width,
                            height: imgData.height,
                            format: "PNG",
                            size: dataUrl.length,
                            extractedAt: new Date(),
                          };

                          totalImageCount++;

                          // Update the processed PDF with the new image
                          setProcessedPdfs((prev) =>
                            prev.map((pdf) =>
                              pdf.id === pdfId
                                ? {
                                    ...pdf,
                                    extractedImages: [
                                      ...pdf.extractedImages,
                                      extractedImage,
                                    ],
                                  }
                                : pdf
                            )
                          );

                          onImageExtracted?.(extractedImage);
                        }
                      }
                    }
                  }
                } catch (imageError) {
                  console.warn(
                    `Error extracting image from page ${pageNum}:`,
                    imageError
                  );
                }
              }
            }
          } catch (pageError) {
            console.warn(`Error processing page ${pageNum}:`, pageError);
          }
        }

        // Mark processing as complete
        setProcessedPdfs((prev) =>
          prev.map((pdf) =>
            pdf.id === pdfId ? { ...pdf, processingComplete: true } : pdf
          )
        );

        setProcessingStatus((prev) => ({
          ...prev,
          [pdfId]: `Complete - Found ${totalImageCount} embedded images`,
        }));

        setTimeout(() => {
          setProgress((prev) => {
            const newProgress = { ...prev };
            delete newProgress[pdfId];
            return newProgress;
          });
          setProcessingStatus((prev) => {
            const newStatus = { ...prev };
            delete newStatus[pdfId];
            return newStatus;
          });
        }, 3000);

        // Find and return the final PDF
        const updatedPdfs = await new Promise<ProcessedPdf[]>((resolve) => {
          setProcessedPdfs((prev) => {
            resolve(prev);
            return prev;
          });
        });

        const finalPdf = updatedPdfs.find((p) => p.id === pdfId);
        if (finalPdf) {
          onPdfProcessed?.(finalPdf);
        }
      } catch (error) {
        console.error("Error processing PDF:", error);
        setProcessingStatus((prev) => ({
          ...prev,
          [pdfId]: "Error processing PDF",
        }));

        setTimeout(() => {
          setProgress((prev) => {
            const newProgress = { ...prev };
            delete newProgress[pdfId];
            return newProgress;
          });
          setProcessingStatus((prev) => {
            const newStatus = { ...prev };
            delete newStatus[pdfId];
            return newStatus;
          });
        }, 3000);
      }
    },
    [onPdfProcessed, onImageExtracted]
  );

  const extractTextFromDocument = useCallback(
    async (file: File) => {
      const docId = `doc_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      try {
        setProcessingStatus((prev) => ({
          ...prev,
          [docId]: "Reading document...",
        }));

        let extractedText = "";
        let format = "";

        if (file.type === "text/plain") {
          // Handle .txt files
          extractedText = await file.text();
          format = "TXT";
        } else if (
          file.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.type === "application/msword"
        ) {
          // Handle .docx and .doc files
          const arrayBuffer = await file.arrayBuffer();
          const result = await mammoth.extractRawText({ arrayBuffer });
          extractedText = result.value;
          format = file.name.endsWith(".docx") ? "DOCX" : "DOC";
        } else if (
          file.type === "text/rtf" ||
          file.type === "application/rtf"
        ) {
          // Handle .rtf files (basic text extraction)
          const text = await file.text();
          // Simple RTF text extraction (removes most RTF markup)
          extractedText = text
            .replace(/\\[a-z]+\d*\s?/gi, "") // Remove RTF commands
            .replace(/[{}]/g, "") // Remove braces
            .replace(/\s+/g, " ") // Normalize whitespace
            .trim();
          format = "RTF";
        } else if (file.type === "application/pdf") {
          // Handle PDF text extraction
          const arrayBuffer = await file.arrayBuffer();
          const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;

          let fullText = "";
          for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
            setProcessingStatus((prev) => ({
              ...prev,
              [docId]: `Extracting text from page ${pageNum} of ${pdf.numPages}...`,
            }));

            const page = await pdf.getPage(pageNum);
            const textContent = await page.getTextContent();
            const pageText = textContent.items
              .map((item) => ("str" in item ? item.str : ""))
              .join(" ");
            fullText += pageText + "\n\n";
          }
          extractedText = fullText.trim();
          format = "PDF";
        }

        if (extractedText) {
          const wordCount = extractedText
            .split(/\s+/)
            .filter((word) => word.length > 0).length;
          const charCount = extractedText.length;

          const textData: ExtractedText = {
            id: docId,
            fileName: file.name,
            content: extractedText,
            wordCount,
            charCount,
            format,
            size: file.size,
            extractedAt: new Date(),
          };

          // Create a processed PDF entry for text documents
          const processedDoc: ProcessedPdf = {
            id: docId,
            name: file.name,
            size: file.size,
            pageCount:
              format === "PDF"
                ? (await pdfjsLib.getDocument(await file.arrayBuffer()).promise)
                    .numPages
                : 1,
            extractedImages: [],
            extractedTexts: [textData],
            uploadedAt: new Date(),
            processingComplete: true,
          };

          setProcessedPdfs((prev) => [...prev, processedDoc]);
          onTextExtracted?.(textData);
          onPdfProcessed?.(processedDoc);
        }

        setProcessingStatus((prev) => ({
          ...prev,
          [docId]: `Complete - Extracted ${
            extractedText.split(/\s+/).length
          } words`,
        }));

        setTimeout(() => {
          setProcessingStatus((prev) => {
            const newStatus = { ...prev };
            delete newStatus[docId];
            return newStatus;
          });
        }, 3000);
      } catch (error) {
        console.error("Error processing document:", error);
        setProcessingStatus((prev) => ({
          ...prev,
          [docId]: "Error processing document",
        }));

        setTimeout(() => {
          setProcessingStatus((prev) => {
            const newStatus = { ...prev };
            delete newStatus[docId];
            return newStatus;
          });
        }, 3000);
      }
    },
    [onTextExtracted, onPdfProcessed]
  );

  const processFiles = useCallback(
    async (files: File[]) => {
      setIsProcessing(true);

      for (const file of files) {
        if (file.type === "application/pdf") {
          await extractImagesFromPdf(file);
        } else {
          await extractTextFromDocument(file);
        }
      }

      setIsProcessing(false);
    },
    [extractImagesFromPdf, extractTextFromDocument]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      const supportedFiles = files.filter((file) =>
        supportedTypes.includes(file.type)
      );

      if (supportedFiles.length > 0) {
        processFiles(supportedFiles);
      }
    },
    [supportedTypes, processFiles]
  );

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      const supportedFiles = files.filter((file) =>
        supportedTypes.includes(file.type)
      );

      if (supportedFiles.length > 0) {
        processFiles(supportedFiles);
      }
    },
    [supportedTypes, processFiles]
  );

  const downloadText = useCallback((text: ExtractedText) => {
    const textBlob = new Blob([text.content], { type: "text/plain" });
    const url = URL.createObjectURL(textBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${text.fileName.split(".")[0]}_extracted_text.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  const downloadImage = useCallback((image: ExtractedImage) => {
    const link = document.createElement("a");
    link.href = image.dataUrl;
    link.download = `pdf_image_page${image.pageNumber}_${image.imageIndex}_${image.width}x${image.height}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  const downloadAllSelectedImages = useCallback(() => {
    const allImages = processedPdfs.flatMap((pdf) => pdf.extractedImages);
    const imagesToDownload =
      selectedImages.length > 0
        ? allImages.filter((img) => selectedImages.includes(img.id))
        : allImages;

    imagesToDownload.forEach((image, index) => {
      setTimeout(() => {
        downloadImage(image);
      }, index * 100); // Small delay between downloads
    });
  }, [processedPdfs, selectedImages, downloadImage]);

  const downloadAllSelectedTexts = useCallback(() => {
    const allTexts = processedPdfs.flatMap((pdf) => pdf.extractedTexts);
    const textsToDownload =
      selectedTexts.length > 0
        ? allTexts.filter((text) => selectedTexts.includes(text.id))
        : allTexts;

    if (textsToDownload.length === 1) {
      downloadText(textsToDownload[0]);
    } else if (textsToDownload.length > 1) {
      // Combine all texts into one file
      const combinedText = textsToDownload
        .map((text) => `=== ${text.fileName} ===\n${text.content}\n\n`)
        .join("");

      const textBlob = new Blob([combinedText], { type: "text/plain" });
      const url = URL.createObjectURL(textBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "all_extracted_texts.txt";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }, [processedPdfs, selectedTexts, downloadText]);

  const toggleImageSelection = useCallback((imageId: string) => {
    setSelectedImages((prev) =>
      prev.includes(imageId)
        ? prev.filter((id) => id !== imageId)
        : [...prev, imageId]
    );
  }, []);

  const toggleTextSelection = useCallback((textId: string) => {
    setSelectedTexts((prev) =>
      prev.includes(textId)
        ? prev.filter((id) => id !== textId)
        : [...prev, textId]
    );
  }, []);

  const selectAllImages = useCallback(() => {
    const allImageIds = processedPdfs.flatMap((pdf) =>
      pdf.extractedImages.map((img) => img.id)
    );
    setSelectedImages(allImageIds);
  }, [processedPdfs]);

  const selectAllTexts = useCallback(() => {
    const allTextIds = processedPdfs.flatMap((pdf) =>
      pdf.extractedTexts.map((text) => text.id)
    );
    setSelectedTexts(allTextIds);
  }, [processedPdfs]);

  const clearImageSelection = useCallback(() => {
    setSelectedImages([]);
  }, []);

  const clearTextSelection = useCallback(() => {
    setSelectedTexts([]);
  }, []);

  const allImages = useMemo(
    () => processedPdfs.flatMap((pdf) => pdf.extractedImages),
    [processedPdfs]
  );

  const allTexts = useMemo(
    () => processedPdfs.flatMap((pdf) => pdf.extractedTexts),
    [processedPdfs]
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-2">
          📑 Document Processor
        </h2>
        <p className="text-slate-600 dark:text-slate-400">
          Extract embedded images from PDFs and text content from various
          document formats. Supports PDF, Word, TXT, and RTF files.
        </p>
      </div>

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
          isDragging
            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : "border-slate-300 dark:border-slate-600 hover:border-slate-400"
        } ${isProcessing ? "opacity-50 pointer-events-none" : ""}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="text-6xl mb-4">📄</div>
        <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
          Drop Files Here or Click to Upload
        </h3>
        <p className="text-slate-500 dark:text-slate-400 mb-4">
          Supports PDF (image extraction), Word documents (.docx, .doc), text
          files (.txt), and RTF files
        </p>
        <input
          type="file"
          multiple
          accept=".pdf,.docx,.doc,.txt,.rtf,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,text/plain,text/rtf,application/rtf"
          onChange={handleFileSelect}
          className="hidden"
          id="file-upload"
          disabled={isProcessing}
        />
        <label
          htmlFor="file-upload"
          className="inline-flex items-center gap-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors cursor-pointer"
        >
          📁 Select Files
        </label>
      </div>

      {/* Processing Progress */}
      {(Object.keys(progress).length > 0 ||
        Object.keys(processingStatus).length > 0) && (
        <div className="space-y-2">
          <h3 className="font-semibold text-slate-800 dark:text-white">
            Processing Files...
          </h3>
          {Object.entries(progress).map(([fileId, progressValue]) => (
            <div
              key={fileId}
              className="bg-slate-200 dark:bg-slate-700 rounded-lg p-3"
            >
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {processingStatus[fileId] || "Processing..."}
                </span>
                <span className="text-sm font-semibold text-slate-800 dark:text-white">
                  {Math.round(progressValue)}%
                </span>
              </div>
              <div className="w-full bg-slate-300 dark:bg-slate-600 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progressValue}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Extracted Images */}
      {allImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-slate-800 dark:text-white">
              Extracted Images ({allImages.length})
            </h3>
            <div className="flex gap-2">
              <button
                onClick={selectAllImages}
                className="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
              >
                Select All
              </button>
              <button
                onClick={clearImageSelection}
                className="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
              >
                Clear Selection
              </button>
              <button
                onClick={downloadAllSelectedImages}
                className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                disabled={allImages.length === 0}
              >
                📥 Download {selectedImages.length > 0 ? "Selected" : "All"}{" "}
                Images
              </button>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {allImages.map((image) => (
              <div
                key={image.id}
                className={`bg-white dark:bg-slate-700 rounded-lg shadow-md overflow-hidden border-2 transition-all ${
                  selectedImages.includes(image.id)
                    ? "border-blue-500 shadow-lg"
                    : "border-transparent"
                }`}
              >
                <div className="relative">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={image.dataUrl}
                    alt={`Extracted from page ${image.pageNumber}`}
                    className="w-full h-48 object-contain bg-slate-100 dark:bg-slate-600"
                  />
                  <button
                    onClick={() => toggleImageSelection(image.id)}
                    className={`absolute top-2 right-2 w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      selectedImages.includes(image.id)
                        ? "bg-blue-500 border-blue-500 text-white"
                        : "bg-white border-slate-300 text-slate-600"
                    }`}
                  >
                    {selectedImages.includes(image.id) && "✓"}
                  </button>
                  <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                    Page {image.pageNumber}
                  </div>
                </div>

                <div className="p-3">
                  <div className="text-xs text-slate-500 dark:text-slate-400 mb-2">
                    {image.width} × {image.height} • {image.format}
                  </div>

                  <button
                    onClick={() => downloadImage(image)}
                    className="w-full px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    📥 Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Extracted Texts */}
      {allTexts.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-slate-800 dark:text-white">
              Extracted Texts ({allTexts.length})
            </h3>
            <div className="flex gap-2">
              <button
                onClick={selectAllTexts}
                className="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
              >
                Select All
              </button>
              <button
                onClick={clearTextSelection}
                className="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
              >
                Clear Selection
              </button>
              <button
                onClick={downloadAllSelectedTexts}
                className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                disabled={allTexts.length === 0}
              >
                📥 Download {selectedTexts.length > 0 ? "Selected" : "All"}{" "}
                Texts
              </button>
            </div>
          </div>

          <div className="grid gap-4">
            {allTexts.map((text) => (
              <div
                key={text.id}
                className={`bg-white dark:bg-slate-700 rounded-lg shadow-md overflow-hidden border-2 transition-all ${
                  selectedTexts.includes(text.id)
                    ? "border-blue-500 shadow-lg"
                    : "border-transparent"
                }`}
              >
                <div className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-slate-800 dark:text-white truncate">
                        {text.fileName}
                      </h4>
                      <div className="text-xs text-slate-500 dark:text-slate-400">
                        {text.wordCount} words • {text.charCount} characters •{" "}
                        {text.format}
                      </div>
                    </div>
                    <button
                      onClick={() => toggleTextSelection(text.id)}
                      className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        selectedTexts.includes(text.id)
                          ? "bg-blue-500 border-blue-500 text-white"
                          : "bg-white border-slate-300 text-slate-600"
                      }`}
                    >
                      {selectedTexts.includes(text.id) && "✓"}
                    </button>
                  </div>

                  <div className="mb-3">
                    <div className="text-sm text-slate-600 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 p-3 rounded max-h-32 overflow-y-auto">
                      {text.content.substring(0, 300)}
                      {text.content.length > 300 && "..."}
                    </div>
                  </div>

                  <button
                    onClick={() => downloadText(text)}
                    className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    📥 Download Text
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Processed Files Summary */}
      {processedPdfs.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-slate-800 dark:text-white">
            Processed Files ({processedPdfs.length})
          </h3>

          <div className="grid gap-4">
            {processedPdfs.map((pdf) => (
              <div
                key={pdf.id}
                className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4"
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-slate-800 dark:text-white">
                      {pdf.name}
                    </h4>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {pdf.pageCount} pages •{" "}
                      {(pdf.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-500">
                      {pdf.extractedImages.length}
                    </div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">
                      images extracted
                    </div>
                  </div>
                </div>

                {pdf.extractedImages.length > 0 && (
                  <div className="flex gap-2 mt-3">
                    <button
                      onClick={() => {
                        const pdfImageIds = pdf.extractedImages.map(
                          (img) => img.id
                        );
                        setSelectedImages(pdfImageIds);
                      }}
                      className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                      Select All Images from File
                    </button>
                  </div>
                )}

                {pdf.extractedTexts.length > 0 && (
                  <div className="flex gap-2 mt-3">
                    <button
                      onClick={() => {
                        const pdfTextIds = pdf.extractedTexts.map(
                          (text) => text.id
                        );
                        setSelectedTexts(pdfTextIds);
                      }}
                      className="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                    >
                      Select All Texts from File
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Summary Stats */}
      {processedPdfs.length > 0 && (
        <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
          <h3 className="font-semibold mb-3 text-slate-800 dark:text-white">
            Summary
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-500">
                {processedPdfs.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Files Processed
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-500">
                {processedPdfs.reduce((acc, pdf) => acc + pdf.pageCount, 0)}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Total Pages
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-500">
                {allImages.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Images Extracted
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-500">
                {allTexts.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Texts Extracted
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-500">
                {selectedImages.length + selectedTexts.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Items Selected
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
