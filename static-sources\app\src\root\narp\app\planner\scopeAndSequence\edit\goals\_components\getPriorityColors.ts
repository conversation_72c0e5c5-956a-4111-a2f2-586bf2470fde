import { GoalPriority } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export function getPriorityBackgroundColor(priority: GoalPriority) {
  switch (priority) {
    case GoalPriority.OPTIONAL:
      return "#B2DFDB";
    case GoalPriority.NICE_TO_HAVE:
      return "#C5CAE9";
    case GoalPriority.MUST_HAVE:
      return "#D7C9FA";
  }
}

export function getPriorityContentColor(priority: GoalPriority) {
  switch (priority) {
    case GoalPriority.OPTIONAL:
      return "#00796B";
    case GoalPriority.NICE_TO_HAVE:
      return "#283593";
    case GoalPriority.MUST_HAVE:
      return "#31156C";
  }
}
