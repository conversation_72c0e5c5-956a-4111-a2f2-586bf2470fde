import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";

export default function NotFound() {
  const navigate = useNavigate();

  return (
    <div
      className={
        "fixed w-screen h-screen top-0 start-0 flex flex-col items-center justify-center"
      }
    >
      <div className={"font-bold text-5xl"}>Uh Oh! :(</div>
      <div>Looks like this page doesn't exist!</div>
      <button
        onClick={() => navigate(-1)}
        type={"button"}
        className={"btn btn-outline mt-5"}
      >
        Back <FaArrowLeft />
      </button>
    </div>
  );
}
