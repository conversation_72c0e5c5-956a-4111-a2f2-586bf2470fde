import { ReactNode } from "react";
import { Link } from "react-router-dom";
import { FaChevronRight } from "react-icons/fa6";

export interface BreadCrumb {
  label: string;
  icon?: ReactNode;
  to: string;
}

export default function BreadCrumbs({ crumbs }: { crumbs: BreadCrumb[] }) {
  return (
    <div className="flex flex-row gap-1.5 items-center">
      {crumbs.map((crumb, i) => {
        if (i == crumbs.length - 1) {
          return (
            <div
              key={i.toString() + "-crumb"}
              className="flex flex-row gap-1 items-center justify-center size-fit"
            >
              {crumb.icon} <span>{crumb.label}</span>
            </div>
          );
        }
        return (
          <div className="flex flex-row gap-1.5 items-center" key={i}>
            <Link
              key={i.toString() + "-crumb"}
              to={crumb.to}
              className="flex flex-row gap-1 items-center justify-center size-fit link link-info text-center md:text-left"
            >
              {crumb.icon} <span>{crumb.label}</span>
            </Link>
            <FaChevronRight
              key={i.toString() + "-divider"}
              className="size-3"
            />
          </div>
        );
      })}
    </div>
  );
}
