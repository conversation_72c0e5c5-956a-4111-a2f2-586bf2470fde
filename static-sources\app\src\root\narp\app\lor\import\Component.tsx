import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Upload,
  Link,
  FileUp,
  Settings,
  SlidersHorizontal,
  X,
  Check,
  Loader,
  ArrowLeft,
} from "lucide-react";
import { SiGoogledrive } from "react-icons/si";
import {
  parsePDF,
  parseDocx,
  parseDoc,
  parsePowerPoint,
  parsePpt,
  parseImage,
  parseTextFile,
  parseJsonFile,
  parseCsvFile,
  parseMarkdownFile,
  parseRtfFile,
} from "../../../../../util/fileParser";

// Enhanced ProcessedFile interface
type ProcessedFile = {
  id: string;
  name: string;
  type: string;
  size: number;
  content?: string;
  metadata?: {
    subject?: string;
    gradeLevel?: string;
    state?: string;
    country?: string;
    school?: string;
    author?: string;
    extractedImages?: number;
    parsingMethod?: string;
    wordCount?: number;
    hasImages?: boolean;
    importMethod?: string;
  };
  uploadedAt: Date;
  processingStage: "uploading" | "processing" | "completed" | "error";
  progress: number;
  error?: string;
};

// Enhanced BulkImportComponent with realistic processing

const BulkImportComponent = forwardRef<
  {
    handleFiles: (files: FileList | File[]) => void;
  },
  {
    onFileProcessed: (file: ProcessedFile) => void;
    onFileProgress: (
      fileId: string,
      progress: number,
      stage: ProcessedFile["processingStage"]
    ) => void;
  }
>((props, ref) => {
  const { onFileProcessed, onFileProgress } = props;

  const processFile = useCallback(
    async (file: File): Promise<void> => {
      const fileId = `${file.name}-${Date.now()}`;
      console.log("Processing file", file);
      let processedFile: ProcessedFile = {
        id: fileId,
        name: file.name,
        type: file.type,
        size: file.size,
        uploadedAt: new Date(),
        processingStage: "uploading",
        progress: 0,
        metadata: {
          importMethod: "bulk",
        },
      };

      try {
        // Stage 1: Upload simulation
        onFileProgress(fileId, 10, "uploading");
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Stage 2: Processing
        onFileProgress(fileId, 30, "processing");
        await new Promise((resolve) => setTimeout(resolve, 500));

        let content = "";
        let parsingMethod = "Unknown";

        if (
          file.type === "application/pdf" ||
          file.name.toLowerCase().endsWith(".pdf")
        ) {
          parsingMethod = "PDF Text Extraction";
          onFileProgress(fileId, 50, "processing");
          content = await parsePDF(file);
          await new Promise((resolve) => setTimeout(resolve, 800));
        } else if (
          file.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.name.toLowerCase().endsWith(".docx")
        ) {
          parsingMethod = "DOCX Text Extraction";
          onFileProgress(fileId, 50, "processing");
          content = await parseDocx(file);
          await new Promise((resolve) => setTimeout(resolve, 600));
        } else if (
          file.type === "application/msword" ||
          file.name.toLowerCase().endsWith(".doc")
        ) {
          parsingMethod = "Legacy DOC Format";
          onFileProgress(fileId, 50, "processing");
          content = await parseDoc(file);
          await new Promise((resolve) => setTimeout(resolve, 400));
        } else if (
          file.type ===
            "application/vnd.openxmlformats-officedocument.presentationml.presentation" ||
          file.name.toLowerCase().endsWith(".pptx")
        ) {
          parsingMethod = "PowerPoint Text Extraction";
          onFileProgress(fileId, 50, "processing");
          content = await parsePowerPoint(file);
          await new Promise((resolve) => setTimeout(resolve, 700));
        } else if (
          file.type === "application/vnd.ms-powerpoint" ||
          file.name.toLowerCase().endsWith(".ppt")
        ) {
          parsingMethod = "Legacy PPT Format";
          onFileProgress(fileId, 50, "processing");
          content = await parsePpt(file);
          await new Promise((resolve) => setTimeout(resolve, 400));
        } else if (file.type.startsWith("image/")) {
          parsingMethod = "OCR Text Extraction";
          onFileProgress(fileId, 60, "processing");
          content = await parseImage(file);
          await new Promise((resolve) => setTimeout(resolve, 1200));
        } else if (
          file.type === "application/json" ||
          file.name.toLowerCase().endsWith(".json")
        ) {
          parsingMethod = "JSON File";
          onFileProgress(fileId, 50, "processing");
          content = await parseJsonFile(file);
          await new Promise((resolve) => setTimeout(resolve, 300));
        } else if (
          file.type === "text/csv" ||
          file.name.toLowerCase().endsWith(".csv")
        ) {
          parsingMethod = "CSV File";
          onFileProgress(fileId, 50, "processing");
          content = await parseCsvFile(file);
          await new Promise((resolve) => setTimeout(resolve, 300));
        } else if (
          file.type === "text/markdown" ||
          file.name.toLowerCase().endsWith(".md") ||
          file.name.toLowerCase().endsWith(".markdown")
        ) {
          parsingMethod = "Markdown File";
          onFileProgress(fileId, 50, "processing");
          content = await parseMarkdownFile(file);
          await new Promise((resolve) => setTimeout(resolve, 300));
        } else if (
          file.type === "application/rtf" ||
          file.name.toLowerCase().endsWith(".rtf")
        ) {
          parsingMethod = "RTF File";
          onFileProgress(fileId, 50, "processing");
          content = await parseRtfFile(file);
          await new Promise((resolve) => setTimeout(resolve, 400));
        } else if (
          file.type.startsWith("text/") ||
          file.name.toLowerCase().endsWith(".txt") ||
          file.name.toLowerCase().endsWith(".log") ||
          file.name.toLowerCase().endsWith(".xml") ||
          file.name.toLowerCase().endsWith(".html") ||
          file.name.toLowerCase().endsWith(".css") ||
          file.name.toLowerCase().endsWith(".js") ||
          file.name.toLowerCase().endsWith(".ts") ||
          file.name.toLowerCase().endsWith(".py") ||
          file.name.toLowerCase().endsWith(".java") ||
          file.name.toLowerCase().endsWith(".cpp") ||
          file.name.toLowerCase().endsWith(".c")
        ) {
          parsingMethod = "Plain Text";
          content = await parseTextFile(file);
        } else {
          parsingMethod = "Unsupported Format";
          content = `📄 Unsupported File Format

File: ${file.name}
Type: ${file.type || "Unknown"}
Size: ${(file.size / 1024).toFixed(2)} KB

⚠️ **File format not supported for text extraction**

**Supported formats:**
• Documents: .pdf, .docx, .doc (limited), .rtf
• Presentations: .pptx, .ppt (limited)
• Text files: .txt, .json, .csv, .md, .rtf
• Images: .jpg, .png, .gif (OCR)
• Code files: .js, .ts, .py, .java, .cpp, .c, .xml, .html, .css

**Recommendations:**
• Convert to a supported format (PDF recommended)
• For .doc/.ppt files, convert to .docx/.pptx
• For images with text, ensure good quality for OCR

---
⚠️ Upload a supported file format for text extraction`;
        }

        onFileProgress(fileId, 90, "processing");
        await new Promise((resolve) => setTimeout(resolve, 200));

        processedFile = {
          ...processedFile,
          content,
          processingStage: "completed",
          progress: 100,
          metadata: {
            ...processedFile.metadata,
            parsingMethod,
            wordCount: content
              ? content.split(/\s+/).filter((word) => word.length > 0).length
              : 0,
            hasImages: false,
            extractedImages: 0,
          },
        };

        onFileProgress(fileId, 100, "completed");
        onFileProcessed(processedFile);
      } catch (error) {
        processedFile = {
          ...processedFile,
          processingStage: "error",
          progress: 0,
          error:
            error instanceof Error ? error.message : "Unknown error occurred",
          content: `Failed to process "${file.name}"\n\nError: ${error instanceof Error ? error.message : "Unknown error"}`,
        };

        onFileProgress(fileId, 0, "error");
        onFileProcessed(processedFile);
      }
    },
    [onFileProcessed, onFileProgress]
  );

  useImperativeHandle(ref, () => ({
    handleFiles(fileList: FileList | File[]) {
      const files = Array.from(fileList);
      console.log("BulkImportComponent.handleFiles called", files);
      files.forEach((file) => {
        processFile(file);
      });
    },
  }));

  return null;
});

/**
 * Enhanced UploadImportPage with comprehensive functionality
 */
export default function UploadImportPage() {
  const [files, setFiles] = useState<ProcessedFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [organizeStep, setOrganizeStep] = useState(false);
  const [fileMetadata, setFileMetadata] = useState<{
    [key: string]: ProcessedFile["metadata"];
  }>({});
  const hiddenFileInput = useRef<HTMLInputElement>(null);

  const completed = useMemo(
    () => files.filter((f) => f.processingStage === "completed").length,
    [files]
  );
  const total = files.length;
  const hasErrors = useMemo(
    () => files.some((f) => f.processingStage === "error"),
    [files]
  );

  const bulkRef = useRef<{ handleFiles: (fl: FileList | File[]) => void }>(
    null
  );

  const onFileProcessed = useCallback((file: ProcessedFile) => {
    setFiles((prev) => {
      const existingIndex = prev.findIndex((f) => f.id === file.id);
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = file;
        return updated;
      }
      return [...prev, file];
    });
  }, []);

  const onFileProgress = useCallback(
    (
      fileId: string,
      progress: number,
      stage: ProcessedFile["processingStage"]
    ) => {
      setFiles((prev) => {
        const existingIndex = prev.findIndex((f) => f.id === fileId);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = {
            ...updated[existingIndex],
            progress,
            processingStage: stage,
          };
          return updated;
        }
        // Create placeholder file if it doesn't exist
        const placeholderFile: ProcessedFile = {
          id: fileId,
          name: fileId.split("-")[0] || "Unknown File",
          type: "application/octet-stream",
          size: 0,
          uploadedAt: new Date(),
          processingStage: stage,
          progress,
        };
        return [...prev, placeholderFile];
      });
    },
    []
  );

  const handleAddFiles = (fileArr: FileList | File[]) => {
    console.log("handleAddFiles called", fileArr);
    bulkRef.current?.handleFiles(fileArr);
    // Reset file input after processing to allow re-uploading same file
    if (hiddenFileInput.current) {
      hiddenFileInput.current.value = "";
    }
  };

  const removeFile = (fileId: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
    setFileMetadata((prev) => {
      const updated = { ...prev };
      delete updated[fileId];
      return updated;
    });
  };

  const dragEvents: React.HTMLAttributes<any> = {
    onDragEnter: (e) => {
      e.preventDefault();
      setDragActive(true);
    },
    onDragLeave: (e) => {
      e.preventDefault();
      setDragActive(false);
    },
    onDragOver: (e) => {
      e.preventDefault();
    },
    onDrop: (e) => {
      e.preventDefault();
      setDragActive(false);
      if (e.dataTransfer.files.length) handleAddFiles(e.dataTransfer.files);
    },
  };

  const quickImport = (type: "drive" | "url" | "local") => {
    if (type === "local") {
      // Reset the input value first to ensure change event fires even for same file
      if (hiddenFileInput.current) {
        hiddenFileInput.current.value = "";
      }
      hiddenFileInput.current?.click();
    }
    if (type === "drive") alert("Google Drive picker coming soon");
    if (type === "url") alert("URL import coming soon");
  };

  const startOrganize = () => {
    // Initialize metadata for all completed files
    const initialMetadata: { [key: string]: ProcessedFile["metadata"] } = {};
    files
      .filter((f) => f.processingStage === "completed")
      .forEach((file) => {
        initialMetadata[file.id] = file.metadata || {};
      });
    setFileMetadata(initialMetadata);
    setOrganizeStep(true);
  };

  const updateFileMetadata = (fileId: string, field: string, value: string) => {
    setFileMetadata((prev) => ({
      ...prev,
      [fileId]: {
        ...prev[fileId],
        [field]: value,
      },
    }));
  };

  const saveMetadata = () => {
    // Update files with metadata
    setFiles((prev) =>
      prev.map((file) => ({
        ...file,
        metadata: {
          ...file.metadata,
          ...fileMetadata[file.id],
        },
      }))
    );

    alert("✅ Files organized and metadata saved successfully!");
    setOrganizeStep(false);
    setFiles([]);
    setFileMetadata({});
  };

  const clearAll = () => {
    setFiles([]);
    setFileMetadata({});
    setOrganizeStep(false);
  };

  const getStatusIcon = (file: ProcessedFile) => {
    switch (file.processingStage) {
      case "completed":
        return <Check className="w-5 h-5 text-emerald-600" />;
      case "error":
        return <X className="w-5 h-5 text-red-600" />;
      case "processing":
      case "uploading":
        return <Loader className="w-5 h-5 text-primary-600 animate-spin" />;
      default:
        return <FileUp className="w-5 h-5 text-neutral-500" />;
    }
  };

  const getStatusText = (file: ProcessedFile) => {
    switch (file.processingStage) {
      case "completed":
        return "Completed";
      case "error":
        return "Failed";
      case "processing":
        return "Processing";
      case "uploading":
        return "Uploading";
      default:
        return "Pending";
    }
  };

  const getProgressColor = (file: ProcessedFile) => {
    switch (file.processingStage) {
      case "completed":
        return "bg-emerald-500";
      case "error":
        return "bg-red-500";
      case "processing":
        return "bg-purple-500";
      case "uploading":
        return "bg-purple-500";
      default:
        return "bg-gray-300";
    }
  };

  return (
    <div className="w-full min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <header className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold text-gray-900">
                Upload & Import
              </h1>
              {organizeStep && (
                <motion.button
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  onClick={() => setOrganizeStep(false)}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to upload
                </motion.button>
              )}
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors ${
                  !organizeStep
                    ? "bg-purple-600 text-white"
                    : "bg-purple-100 text-purple-600"
                }`}
              >
                1
              </div>
              <span
                className={`font-medium transition-colors ${
                  !organizeStep ? "text-gray-900" : "text-gray-500"
                }`}
              >
                Add sources
              </span>
            </div>

            <div
              className={`w-8 h-0.5 transition-colors ${
                organizeStep ? "bg-purple-600" : "bg-gray-300"
              }`}
            />

            <div className="flex items-center gap-2">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors ${
                  organizeStep
                    ? "bg-purple-600 text-white"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                2
              </div>
              <span
                className={`font-medium transition-colors ${
                  organizeStep ? "text-gray-900" : "text-gray-500"
                }`}
              >
                Organize & tag
              </span>
            </div>
          </div>

          <p className="text-gray-600 max-w-2xl">
            Add learning materials to your repository with intelligent
            processing and organization.
          </p>
        </header>

        {/* Main Content */}
        <AnimatePresence mode="wait">
          {!organizeStep ? (
            <motion.div
              key="upload"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="space-y-6"
            >
              {!total ? (
                /* Upload Area */
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
                  <div className="p-8">
                    <div
                      {...dragEvents}
                      onClick={() => {
                        if (hiddenFileInput.current) {
                          hiddenFileInput.current.value = "";
                        }
                        hiddenFileInput.current?.click();
                      }}
                      className={`border-2 border-dashed rounded-xl p-12 text-center cursor-pointer transition-all duration-200 ${
                        dragActive
                          ? "border-blue-400 bg-blue-50"
                          : "border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"
                      }`}
                    >
                      <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-6">
                        <Upload className="w-8 h-8 text-primary-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-neutral-900 mb-2">
                        Upload your files
                      </h3>
                      <p className="text-neutral-600 mb-4">
                        Drag and drop files here, or click to browse
                      </p>
                      <p className="text-sm text-neutral-500">
                        Supported: PDF, Word (.docx, .doc), PowerPoint (.pptx,
                        .ppt), Images, Text files (.txt, .json, .csv, .md,
                        .rtf), Code files (up to 10MB each)
                      </p>
                      <input
                        ref={hiddenFileInput}
                        type="file"
                        multiple
                        className="hidden"
                        accept=".pdf,.docx,.doc,.pptx,.ppt,.jpg,.jpeg,.png,.gif,.txt,.json,.csv,.md,.markdown,.rtf,.log,.xml,.html,.css,.js,.ts,.py,.java,.cpp,.c"
                        onChange={(e) => {
                          if (e.target.files && e.target.files.length > 0) {
                            handleAddFiles(e.target.files);
                          }
                        }}
                      />
                    </div>

                    {/* Quick Import Options */}
                    <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4">
                      <button
                        onClick={() => quickImport("local")}
                        className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors group"
                      >
                        <div className="p-2 bg-primary-50 rounded-lg group-hover:bg-primary-100 transition-colors">
                          <FileUp className="w-5 h-5 text-primary-600" />
                        </div>
                        <div className="ml-3 text-left">
                          <p className="font-medium text-neutral-900">
                            Local Files
                          </p>
                          <p className="text-sm text-neutral-500">
                            Browse and upload
                          </p>
                        </div>
                      </button>

                      <button
                        onClick={() => quickImport("drive")}
                        className="flex items-center p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 hover:border-neutral-300 transition-colors group"
                      >
                        <div className="p-2 bg-green-50 rounded-lg group-hover:bg-green-100 transition-colors">
                          <SiGoogledrive className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="ml-3 text-left">
                          <p className="font-medium text-neutral-900">
                            Google Drive
                          </p>
                          <p className="text-sm text-neutral-500">
                            Import from Drive
                          </p>
                        </div>
                      </button>

                      <button
                        onClick={() => quickImport("url")}
                        className="flex items-center p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 hover:border-neutral-300 transition-colors group"
                      >
                        <div className="p-2 bg-primary-50 rounded-lg group-hover:bg-primary-100 transition-colors">
                          <Link className="w-5 h-5 text-primary-600" />
                        </div>
                        <div className="ml-3 text-left">
                          <p className="font-medium text-gray-900">
                            Website URL
                          </p>
                          <p className="text-sm text-gray-500">
                            Import from web
                          </p>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                /* File Processing */
                <div className="space-y-6">
                  <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
                    <div className="p-6 border-b border-gray-200">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">
                            Processing Files
                          </h3>
                          <p className="text-sm text-gray-600">
                            {completed} of {total} files completed
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <button
                            onClick={() => {
                              if (hiddenFileInput.current) {
                                hiddenFileInput.current.value = "";
                              }
                              hiddenFileInput.current?.click();
                            }}
                            className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-purple-600 hover:text-purple-700 transition-colors"
                          >
                            <Upload className="w-4 h-4" />
                            Add more
                          </button>
                          <button
                            onClick={clearAll}
                            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                          >
                            Clear all
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 space-y-4">
                      {files.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200"
                        >
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
                              {getStatusIcon(file)}
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <p className="font-medium text-gray-900 truncate">
                                {file.name}
                              </p>
                              <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                                {(file.size / (1024 * 1024)).toFixed(1)} MB
                              </span>
                            </div>

                            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                              <div
                                className={`h-full transition-all duration-300 ${getProgressColor(file)}`}
                                style={{ width: `${file.progress}%` }}
                              />
                            </div>

                            <div className="flex items-center justify-between mt-2">
                              <span
                                className={`text-sm font-medium ${
                                  file.processingStage === "completed"
                                    ? "text-emerald-600"
                                    : file.processingStage === "error"
                                      ? "text-red-600"
                                      : "text-purple-600"
                                }`}
                              >
                                {getStatusText(file)}
                              </span>

                              {file.processingStage === "processing" &&
                                file.metadata?.parsingMethod && (
                                  <p className="text-xs text-gray-500">
                                    {file.metadata.parsingMethod}...
                                  </p>
                                )}
                            </div>
                          </div>

                          <button
                            onClick={() => removeFile(file.id)}
                            className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded-lg transition-colors"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Ready to organize */}
                  {completed === total && completed > 0 && !hasErrors && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-emerald-50 border border-emerald-200 rounded-xl p-6"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center">
                            <Check className="w-6 h-6 text-emerald-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-emerald-900 mb-1">
                              All files processed successfully!
                            </h3>
                            <p className="text-sm text-emerald-700">
                              Ready to organize and add metadata to your {total}{" "}
                              {total === 1 ? "file" : "files"}.
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={startOrganize}
                          className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
                        >
                          Organize files →
                        </button>
                      </div>
                    </motion.div>
                  )}

                  {hasErrors && (
                    <div className="bg-red-50 border border-red-200 rounded-xl p-6">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <X className="w-4 h-4 text-red-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-red-900 mb-1">
                            Some files failed to process
                          </h3>
                          <p className="text-sm text-red-700">
                            You can remove failed files and continue, or try
                            uploading them again.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="organize"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="space-y-6"
            >
              {/* Organize Header */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Organize Your Files
                  </h3>
                  <p className="text-gray-600">
                    Add metadata and tags to make your files easier to find and
                    organize.
                  </p>
                </div>
              </div>

              {/* File Organization */}
              {files
                .filter((f) => f.processingStage === "completed")
                .map((file, index) => (
                  <motion.div
                    key={file.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden"
                  >
                    {/* File Header */}
                    <div className="p-6 border-b border-gray-200 bg-gray-50">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                          <FileUp className="w-6 h-6 text-primary-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-neutral-900 mb-1">
                            {file.name}
                          </h3>
                          <p className="text-sm text-neutral-500">
                            {(file.size / (1024 * 1024)).toFixed(1)} MB •{" "}
                            {file.metadata?.parsingMethod}
                          </p>
                        </div>
                        <div className="text-right">
                          <span className="inline-block px-3 py-1 bg-emerald-100 text-emerald-800 text-sm font-medium rounded-full">
                            ✓ Processed
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Metadata Form */}
                    <div className="p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Subject
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                            value={fileMetadata[file.id]?.subject || ""}
                            onChange={(e) =>
                              updateFileMetadata(
                                file.id,
                                "subject",
                                e.target.value
                              )
                            }
                          >
                            <option value="">Select subject</option>
                            <option value="mathematics">Mathematics</option>
                            <option value="science">Science</option>
                            <option value="english">
                              English Language Arts
                            </option>
                            <option value="history">History</option>
                            <option value="foreign-language">
                              Foreign Language
                            </option>
                            <option value="art">Art</option>
                            <option value="music">Music</option>
                            <option value="physical-education">
                              Physical Education
                            </option>
                            <option value="computer-science">
                              Computer Science
                            </option>
                            <option value="other">Other</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Grade Level
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                            value={fileMetadata[file.id]?.gradeLevel || ""}
                            onChange={(e) =>
                              updateFileMetadata(
                                file.id,
                                "gradeLevel",
                                e.target.value
                              )
                            }
                          >
                            <option value="">Select grade</option>
                            <option value="k">Kindergarten</option>
                            <option value="1">Grade 1</option>
                            <option value="2">Grade 2</option>
                            <option value="3">Grade 3</option>
                            <option value="4">Grade 4</option>
                            <option value="5">Grade 5</option>
                            <option value="6">Grade 6</option>
                            <option value="7">Grade 7</option>
                            <option value="8">Grade 8</option>
                            <option value="9">Grade 9</option>
                            <option value="10">Grade 10</option>
                            <option value="11">Grade 11</option>
                            <option value="12">Grade 12</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            State/Region
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                            value={fileMetadata[file.id]?.state || ""}
                            onChange={(e) =>
                              updateFileMetadata(
                                file.id,
                                "state",
                                e.target.value
                              )
                            }
                          >
                            <option value="">Select state/region</option>
                            <option value="california">California</option>
                            <option value="texas">Texas</option>
                            <option value="florida">Florida</option>
                            <option value="new-york">New York</option>
                            <option value="pennsylvania">Pennsylvania</option>
                            <option value="illinois">Illinois</option>
                            <option value="ohio">Ohio</option>
                            <option value="georgia">Georgia</option>
                            <option value="north-carolina">
                              North Carolina
                            </option>
                            <option value="michigan">Michigan</option>
                            <option value="other">Other</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Country
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                            value={fileMetadata[file.id]?.country || ""}
                            onChange={(e) =>
                              updateFileMetadata(
                                file.id,
                                "country",
                                e.target.value
                              )
                            }
                          >
                            <option value="">Select country</option>
                            <option value="united-states">United States</option>
                            <option value="canada">Canada</option>
                            <option value="united-kingdom">
                              United Kingdom
                            </option>
                            <option value="australia">Australia</option>
                            <option value="germany">Germany</option>
                            <option value="france">France</option>
                            <option value="other">Other</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            School/Institution
                          </label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                            placeholder="Enter school name"
                            value={fileMetadata[file.id]?.school || ""}
                            onChange={(e) =>
                              updateFileMetadata(
                                file.id,
                                "school",
                                e.target.value
                              )
                            }
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Author/Creator
                          </label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                            placeholder="Enter author name"
                            value={fileMetadata[file.id]?.author || ""}
                            onChange={(e) =>
                              updateFileMetadata(
                                file.id,
                                "author",
                                e.target.value
                              )
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}

              {/* Save Actions */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">
                      Ready to save?
                    </h3>
                    <p className="text-sm text-gray-600">
                      Your files will be saved with the metadata you've
                      provided.
                    </p>
                  </div>
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => setOrganizeStep(false)}
                      className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={saveMetadata}
                      className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
                    >
                      Save Files
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bottom Actions */}
        <div className="mt-8 flex justify-between items-center py-4 border-t border-gray-200">
          <div className="flex items-center gap-4">
            <button className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors">
              <SlidersHorizontal className="w-5 h-5" />
              <span className="text-sm font-medium">Processing options</span>
            </button>
          </div>
          <button className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors">
            <Settings className="w-5 h-5" />
            <span className="text-sm font-medium">Settings</span>
          </button>
        </div>
      </div>

      {/* Hidden Components */}
      <BulkImportComponent
        ref={bulkRef}
        onFileProcessed={onFileProcessed}
        onFileProgress={onFileProgress}
      />
    </div>
  );
}
