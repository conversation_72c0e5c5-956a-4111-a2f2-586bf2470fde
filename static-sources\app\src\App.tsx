import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { default as RootRoute } from "./root/Route";
import AlertsProvider from "@/util/components/Alerts/Provider.tsx";
import ConfigurationProvider from "@/util/configuration/ConfigurationProvider.tsx";
import { AuthProvider } from "@/util/auth/AuthProvider.tsx";

const router = createBrowserRouter([RootRoute], {});

export default function App() {
  return (
    <ConfigurationProvider>
      <AuthProvider>
        <AlertsProvider>
          <RouterProvider router={router} />
        </AlertsProvider>
      </AuthProvider>
    </ConfigurationProvider>
  );
}
