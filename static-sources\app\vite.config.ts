import path from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { execSync } from "node:child_process";
import { TanStackRouterVite } from "@tanstack/router-vite-plugin";
import tailwindcss from "tailwindcss";
import { viteStaticCopy } from "vite-plugin-static-copy";

// https://vitejs.dev/config/
export default defineConfig(() => {
  const gitVersionTag = execSync("git describe --always --tag --abbrev=0")
    .toString()
    .trimEnd();

  return {
    define: {
      global: {
        __APP_VERSION__: gitVersionTag,
      },
    },
    plugins: [
      react(),
      TanStackRouterVite({
        routesDirectory: "src/root",
        generatedRouteTree: "src/routeTree.gen.ts",
      }),
      viteStaticCopy({
        targets: [
          {
            src: "public/pdf.worker.min.js",
            dest: "",
          },
        ],
      }),
    ],
    css: {
      postcss: {
        plugins: [tailwindcss],
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@util": path.resolve(__dirname, "./src/util"),
        "@assets": path.resolve(__dirname, "./src/assets"),
        "@public": path.resolve(__dirname, "./public"),
      },
    },
    base: "/",
    envDir: "./environment",
    build: {
      outDir: "../../.static/",
    },
    server: {
      host: true,
    },
  };
});
