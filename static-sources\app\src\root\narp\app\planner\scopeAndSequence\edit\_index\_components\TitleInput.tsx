import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import {
  FocusEvent,
  FormEvent,
  KeyboardEvent,
  MouseEvent,
  useRef,
  useState,
} from "react";
import { FaCheck, FaPenToSquare } from "react-icons/fa6";

export default function TitleInput() {
  const {
    outline: { title, setTitle },
    cloud: { save },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState<string>(title);
  const [checkedTimeout, setCheckedTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const buttonRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleKeyDown(e: KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter") {
      e.preventDefault();
      confirmEdit();
    }
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget == buttonRef.current ||
      e.relatedTarget === inputRef.current
    )
      return;
    // Don't revert - let user keep their changes
  }

  function confirmEdit() {
    if (title === displayValue) return;
    setTitle(displayValue);

    if (checkedTimeout !== null) clearTimeout(checkedTimeout);
    setCheckedTimeout(
      setTimeout(() => {
        setCheckedTimeout(null);
        // Auto-save after confirm
        save();
      }, 1000)
    );
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    confirmEdit();
  }

  return (
    <label className="flex flex-col gap-1">
      <span className="font-bold">Title</span>
      <label className="flex flex-row gap-3 w-full max-w-screen-md">
        <input
          tabIndex={0}
          ref={inputRef}
          onBlur={handleBlur}
          value={displayValue}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          placeholder="Enter title (Press Enter to save)"
          className="input input-lg grow"
        />
        <div className="tooltip" data-tip="Confirm Edit (Enter)">
          <button
            tabIndex={0}
            onClick={handleClick}
            onBlur={handleBlur}
            ref={buttonRef}
            type="submit"
            disabled={title === displayValue}
            className={`btn btn-square btn-lg btn-primary ${title === displayValue ? "btn-disabled" : ""}`}
          >
            {checkedTimeout === null ? <FaPenToSquare /> : <FaCheck />}
          </button>
        </div>
      </label>
    </label>
  );
}
