import {
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { Dispatch, SetStateAction, useEffect, useRef } from "react";
import { FaXmark } from "react-icons/fa6";
import TitleField from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/TitleField.tsx";
import TypeField from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/TypeField.tsx";
import DescriptionField from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/DescriptionField.tsx";
import DurationField from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/DurationField.tsx";
import ResourcesField from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/ResourcesField.tsx";
import DeliverablesField from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/DeliverablesField.tsx";

export default function ActivityEditModal({
  editID,
  setEditID,
}: {
  editID: EntityID | null;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    activity: { get },
  } = useEditorContext();

  const dialogRef = useRef<HTMLDialogElement>(null);
  const activity = editID !== null ? get(editID) : null;

  useEffect(() => {
    if (activity) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [activity]);

  function closeDialog() {
    dialogRef.current?.close();
  }

  function handleClose() {
    setEditID(null);
  }

  if (!activity) return <></>;
  return (
    <dialog onClose={handleClose} ref={dialogRef}>
      <button
        tabIndex={0}
        type="button"
        onClick={closeDialog}
        className="-z-10 fixed w-screen h-screen"
      />

      <div
        className="fixed w-full max-w-2xl top-1/2 left-1/2 -translate-1/2
         p-5"
      >
        <div className="bg-white border border-neutral-300 rounded-lg p-5 flex flex-col gap-4 max-h-[80vh] overflow-y-auto">
          <div className="flex flex-row">
            <div className="grow basis-0">
              <div className="tooltip" data-tip="Close">
                <button
                  tabIndex={0}
                  type="button"
                  className="btn btn-sm btn-square btn-error btn-outline"
                  data-tip="what"
                  onClick={closeDialog}
                >
                  <FaXmark />
                </button>
              </div>
            </div>
            <div className="basis-0">
              <div className="text-xl font-bold text-nowrap">Edit Activity</div>
            </div>
            <div className="grow basis-0"></div>
          </div>

          <TitleField activity={activity} />
          <TypeField activity={activity} />
          <DescriptionField activity={activity} />
          <DurationField activity={activity} />
          <ResourcesField activity={activity} />
          <DeliverablesField activity={activity} />
        </div>
      </div>
    </dialog>
  );
}
