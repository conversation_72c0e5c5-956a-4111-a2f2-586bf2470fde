import { LexicalCategory } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export function getLexicalColor(category: LexicalCategory) {
  switch (category) {
    case LexicalCategory.NOUN:
      return "oklch(93.6% 0.032 17.717)";
    case LexicalCategory.VERB:
      return "oklch(96.2% 0.059 95.617)";
    case LexicalCategory.ADJECTIVE:
      return "oklch(96.7% 0.067 122.328)";
    case LexicalCategory.ADVERB:
      return "oklch(95% 0.052 163.051)";
    case LexicalCategory.PRONOUN:
      return "oklch(95.6% 0.045 203.388)";
    case LexicalCategory.PREPOSITION:
      return "oklch(93.2% 0.032 255.585)";
    case LexicalCategory.CONJUNCTION:
      return "oklch(94.3% 0.029 294.588)";
    case LexicalCategory.INTERJECTION:
      return "oklch(94.6% 0.033 307.174)";
  }
}
