import {
  Topic,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import {
  FocusEvent,
  FormEvent,
  MouseEvent,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  DragData,
  DropData,
} from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/DndHandler.tsx";
import {
  Active,
  DragStartEvent,
  useDndMonitor,
  useDraggable,
  useDroppable,
} from "@dnd-kit/core";
import {
  FaCheck,
  FaChevronDown,
  FaGripLinesVertical,
  FaTrash,
  FaWandMagicSparkles,
} from "react-icons/fa6";
import { FaSave } from "react-icons/fa";
import usePOST from "@/util/api/usePOST.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import { APIEnvelope } from "@/util/api/types.ts";
import { TermsSection } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/TermsSection.tsx";
import GoalsSection from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/GoalsSection.tsx";
import { NotesSection } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/NotesSection.tsx";
import { ActivitiesSection } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/ActivitiesSection.tsx";
import { EssentialQuestionsSection } from "@/root/narp/app/planner/scopeAndSequence/edit/topics/_components/EssentialQuestionsSection.tsx";

export default function TopicEntry({
  topic,
  index,
  toggleCollapsed,
  collapsed,
}: {
  topic: Topic;
  index: number;
  collapsed: boolean;
  toggleCollapsed: () => void;
}) {
  const dropData: DropData = {
    type: "TOPIC",
    index: index,
  };
  const { isOver, setNodeRef: setDropRef } = useDroppable({
    id: topic.id + ":topic:drop",
    data: dropData,
  });

  const dragData: DragData = {
    type: "TOPIC",
    topic: topic,
    index: index,
  };

  const {
    isDragging,
    attributes,
    listeners,
    setNodeRef: setDragRef,
  } = useDraggable({
    id: topic.id + ":topic:drag",
    data: dragData,
  });

  const [active, setActive] = useState<Active | null>(null);

  useDndMonitor({
    onDragStart(event: DragStartEvent) {
      if (event.active.data.current?.type === "TOPIC") setActive(event.active);
    },
    onDragEnd() {
      setActive(null);
    },
  });

  return (
    <>
      {active && (
        <div
          style={{
            height: isOver ? active.rect.current.initial!.height + "px" : 0,
          }}
          className="transition-all"
        />
      )}
      <div ref={setDragRef}>
        <div
          ref={setDropRef}
          className={`border-2 border-primary-200 rounded-lg ${isDragging && "invisible"} mb-5`}
        >
          <div
            className={`flex flex-row items-center p-5 bg-primary-050 gap-3 overflow-auto ${collapsed ? "rounded-lg" : "rounded-t-lg"}`}
          >
            <div
              {...attributes}
              {...listeners}
              className="w-4 flex items-center justify-center text-xl cursor-grab"
            >
              <FaGripLinesVertical className="shrink-0" />
            </div>

            <NameField topic={topic} index={index} />

            <div className="tooltip" data-tip="Collapse">
              <button
                type="button"
                onClick={() => toggleCollapsed()}
                className="btn btn-lg btn-square"
              >
                <FaChevronDown
                  className={`transition-transform ${collapsed ? "rotate-180" : "rotate-0"}`}
                />
              </button>
            </div>

            <DeleteButton topic={topic} />
          </div>

          {!collapsed && !active && !isDragging && (
            <div className="flex flex-col gap-5 p-5">
              <GoalsSection topic={topic} />
              <TermsSection topic={topic} />
              <ActivitiesSection topic={topic} />
              <NotesSection topic={topic} />
              <EssentialQuestionsSection topic={topic} />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

function DeleteButton({ topic }: { topic: Topic }) {
  const {
    topic: { remove },
  } = useEditorContext();

  const [confirmTimeout, setConfirmTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Clear confirmation when clicking elsewhere
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        if (confirmTimeout) {
          clearTimeout(confirmTimeout);
          setConfirmTimeout(null);
        }
      }
    }

    if (confirmTimeout) {
      // Add click listener when in confirmation mode
      const nativeHandler = (event: Event) =>
        handleClickOutside(event as unknown as MouseEvent<Element>);
      document.addEventListener("click", nativeHandler);
      return () => {
        document.removeEventListener("click", nativeHandler);
      };
    }
  }, [confirmTimeout]);

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    e.stopPropagation();

    if (confirmTimeout) {
      // Confirmed delete
      remove(topic.id);
      clearTimeout(confirmTimeout);
      setConfirmTimeout(null);
    } else {
      // Start confirmation
      setConfirmTimeout(
        setTimeout(() => {
          setConfirmTimeout(null);
        }, 3000) // 3 second timeout
      );
    }
  }

  return (
    <div className="tooltip">
      <div className="tooltip-content">
        {confirmTimeout ? (
          <span>
            Click to Confirm Delete
            <br />
            <small>(or click elsewhere to cancel)</small>
          </span>
        ) : (
          <span>Delete Topic</span>
        )}
      </div>
      <button
        ref={buttonRef}
        onClick={handleClick}
        type="button"
        className={`btn btn-lg btn-square btn-outline transition-colors ${
          confirmTimeout
            ? "btn-error animate-pulse border-red-500"
            : "btn-error hover:btn-error"
        }`}
      >
        <FaTrash />
      </button>
    </div>
  );
}

function NameField({ topic, index }: { topic: Topic; index: number }) {
  const {
    topic: { setName, getGoals },
  } = useEditorContext();
  const topRealm = useTopRealm();
  const POST = usePOST();
  const pushError = usePushError();

  const [generating, setGenerating] = useState<boolean>(false);
  const [displayValue, setDisplayValue] = useState<string>(topic.name);
  const [confirmTimeout, setConfirmTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const inputRef = useRef<HTMLInputElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    setDisplayValue(topic.name);
  }, [topic.name]);

  async function handleGenerate(e: MouseEvent) {
    e.preventDefault();
    setGenerating(true);

    const { response, error, data } = await POST(
      `/v1/ai/curricula/weekly-topic/generate`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
        body: JSON.stringify({
          realmEID: topRealm.externalID,
          weeklyObjectives: {
            weekNumber: index,
            primaryTopic: topic.name,
            objectives: getGoals(topic.id).map((entry) => entry.description),
          },
        }),
      }
    );
    setGenerating(false);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());
    if (!data) return pushError("missing data");

    const envelope: APIEnvelope<{
      weekNumber: number;
      primaryTopic: string;
      objectives: string[];
    }> = JSON.parse(await data.text());

    setName(topic.id, envelope.data.primaryTopic);
  }

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    if (confirmTimeout) {
      clearTimeout(confirmTimeout);
      setConfirmTimeout(null);
    }

    setName(topic.id, displayValue);
    setConfirmTimeout(
      setTimeout(() => {
        setConfirmTimeout(null);
      }, 1000)
    );
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget === inputRef.current ||
      e.relatedTarget === buttonRef.current
    )
      return;
    setDisplayValue(topic.name);
  }

  return (
    <div className="grow flex flex-row gap-3">
      <button
        disabled={generating}
        type="button"
        onClick={handleGenerate}
        className="btn btn-lg btn-square btn-primary"
      >
        {generating ? (
          <div className="loading loading-bars loading-xl" />
        ) : (
          <FaWandMagicSparkles />
        )}
      </button>

      <input
        disabled={generating}
        ref={inputRef}
        onInput={handleInput}
        onBlur={handleBlur}
        className="input input-lg grow font-bold w-full min-w-[200px]"
        value={displayValue}
      />
      <div className="tooltip" data-tip={"Save Edit"}>
        <button
          type="button"
          onClick={handleClick}
          onBlur={handleBlur}
          ref={buttonRef}
          disabled={generating || topic.name === displayValue}
          className={`btn btn-lg btn-square btn-primary`}
        >
          {confirmTimeout !== null ? <FaCheck /> : <FaSave />}
        </button>
      </div>
    </div>
  );
}
