import { NavLink } from "react-router-dom";
import { FaShield, FaSitemap, FaUsers } from "react-icons/fa6";
import BreadCrumbs from "@/util/components/BreadCrumbs.tsx";

export function Component() {
  return (
    <div className="size-full overflow-y-auto">
      <div className="flex flex-col gap-5 p-5">
        {" "}
        <BreadCrumbs
          crumbs={[
            {
              icon: <FaShield className="size-4" />,
              label: "Admin",
              to: "/narp/app/admin",
            },
          ]}
        />
        <div>
          <h1 className="text-3xl font-bold mb-2">Administration</h1>
          <p className="text-gray-600 mb-6">
            Manage realms, roles, and privileges for your organization
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <NavLink
            to="/narp/app/admin/realm"
            className="block p-6 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            {" "}
            <div className="flex items-center mb-3">
              <FaSitemap className="size-8 text-primary-600 mr-3" />
              <h3 className="text-xl font-semibold">Realm Management</h3>
            </div>
            <p className="text-gray-600">
              View and manage the realm hierarchy, create new realms, and
              configure realm settings.
            </p>
          </NavLink>

          <NavLink
            to="/narp/app/admin/role"
            className="block p-6 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center mb-3">
              <FaUsers className="size-8 text-primary-600 mr-3" />
              <h3 className="text-xl font-semibold">Role Management</h3>
            </div>
            <p className="text-gray-600">
              Create and manage user roles, assign users to roles, and configure
              role permissions.
            </p>
          </NavLink>

          <NavLink
            to="/narp/app/admin/privilege-matrix"
            className="block p-6 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center mb-3">
              <FaShield className="size-8 text-primary-600 mr-3" />
              <h3 className="text-xl font-semibold">Privilege Matrix</h3>
            </div>
            <p className="text-gray-600">
              View and configure privilege assignments across realms and roles
              using the privilege matrix interface.
            </p>
          </NavLink>
        </div>
      </div>
    </div>
  );
}
