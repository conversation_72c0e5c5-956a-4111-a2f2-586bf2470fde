import { BaseOperation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/base.ts";
import {
  EntityID,
  Topic,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export interface TopicAddOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "ADD";
  data: {
    id: EntityID;
  };
}

export interface TopicRemoveOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "REMOVE";
  data: {
    index: number;
    value: Topic;
  };
}

export interface TopicMoveOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "MOVE";
  data: {
    from: number;
    to: number;
  };
}

export interface TopicSetNameOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_NAME";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TopicSetNotesOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_NOTES";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TopicSetEssentialQuestionsOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_ESSENTIAL_QUESTIONS";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TopicSetResourcesOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_RESOURCES";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TopicSetEvidenceOfLearningOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_EVIDENCE_OF_LEARNING";
  data: {
    id: EntityID;
    previous: string;
  };
}

export interface TopicSetDurationOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_DURATION";
  data: {
    id: EntityID;
    previous: number;
  };
}

export interface TopicSetStartOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "SET_START";
  data: {
    id: EntityID;
    previous: number;
  };
}

export interface TopicAddDependencyOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "ADD_DEPENDENCY";
  data: {
    id: EntityID;
    value: EntityID;
  };
}

export interface TopicRemoveDependencyOperation extends BaseOperation {
  namespace: "TOPIC";
  action: "REMOVE_DEPENDENCY";
  data: {
    id: EntityID;
    value: EntityID;
  };
}

export type TopicOperation =
  | TopicAddOperation
  | TopicRemoveOperation
  | TopicMoveOperation
  | TopicSetNameOperation
  | TopicSetNotesOperation
  | TopicSetEssentialQuestionsOperation
  | TopicSetResourcesOperation
  | TopicSetEvidenceOfLearningOperation
  | TopicSetDurationOperation
  | TopicSetStartOperation
  | TopicAddDependencyOperation
  | TopicRemoveDependencyOperation;
