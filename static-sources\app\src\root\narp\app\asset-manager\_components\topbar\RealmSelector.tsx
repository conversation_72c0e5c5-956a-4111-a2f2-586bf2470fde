import { useNavigate, useParams } from "react-router-dom";
import { FormEvent, useEffect, useState } from "react";
import {
  RealmDetails,
  useTopRealm,
} from "@/root/narp/app/_components/Context";
import { HttpMethod, useAPIRequest } from "@/util/API.tsx";
import { usePushError } from "@/util/components/Alerts/util.tsx";

interface ResultEntry {
  descendants: { realms: ResultEntry[] } | undefined;
  displayName: string;
  eidURN: string;
  externalID: string;
  parentPath: string;
  realmPath: string;
  urn: string;
}

interface Result {
  realms: ResultEntry[];
}

function transform(entry: ResultEntry): RealmDetails {
  return {
    realmPath: entry.realmPath,
    displayName: entry.displayName,
    externalID: entry.externalID,
    urn: entry.urn,
    eidURN: entry.eidURN,
    children: entry.descendants ? entry.descendants.realms.map(transform) : [],
  };
}

function flatten(
  collection: RealmDetails[],
  details: RealmDetails,
): RealmDetails[] {
  collection.push(details);
  for (const detail of details.children) {
    flatten(collection, detail);
  }
  return collection;
}

export default function RealmSelector() {
  const request = useAPIRequest();
  const topRealm = useTopRealm();
  const pushError = usePushError();
  const navigate = useNavigate();
  const { realmEIDURN, zone } = useParams();

  const [realms, setRealms] = useState<RealmDetails[] | null>();

  async function updateRealms() {
    // reset states
    setRealms(null);

    // fetch descendant realms
    const [response, error] = await request(
      `/v1/realms/${topRealm.externalID}`,
      HttpMethod.GET,
      { assertedRealm: topRealm },
    );
    if (!response) return pushError("Missing auth token");
    if (error) return pushError(error.clientErrorDetail.userMessage);

    // transform and flatten returned result
    const raw: Result = await response.json();
    const transformed = transform(raw.realms[0]);
    const list: RealmDetails[] = flatten([], transformed);
    setRealms(list);
  }

  function handleSelectRealm(e: FormEvent<HTMLSelectElement>) {
    e.preventDefault();
    navigate(
      `/narp/app/asset-manager/${e.currentTarget.value}/${zone || "~"}/`,
    );
  }

  useEffect(() => {
    updateRealms();
  }, []);

  return (
    <label className="floating-label w-2xs">
      <span className="text-nowrap">Target Realm</span>
      {!realms ? (
        <div className="select select-sm flex items-center justify-center">
          <div className="loading loading-spinner loading-xs" />
        </div>
      ) : (
        <select
          value={realmEIDURN}
          onInput={handleSelectRealm}
          className="select select-sm w-full"
        >
          <option hidden defaultChecked />
          {realms.map((entry, i) => (
            <option value={entry.eidURN} key={i}>
              {entry.realmPath}
            </option>
          ))}
        </select>
      )}
    </label>
  );
}
