import { createContext, Dispatch, SetStateAction, useContext } from "react";
import { Goal } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { SerializedDocument } from "@/root/narp/app/planner/_util/plannerDocuments/ver2.ts";

export interface GenerationOutline {
  title: string;
  abstract: string;
  keywords: string[];
}

export interface GenerationGoal extends Goal {
  topicIndex: number;
}

export interface Region {
  category: string;
  code: string;
  eid: string;
  eidURN: string;
  name: string;
  urn: string;
}

export interface Subregion {
  category: string;
  code: string;
  eid: string;
  eidURN: string;
  name: string;
  parentEID: string;
  parentEIDurn: string;
  parentURN: string;
  urn: string;
}

export interface Authority {
  code: string;
  eidURN: string;
  name: string;
}

export interface Standard {
  formattedSectionNumber: string;
  levelTitle: string;
  sectionName: string;
  sectionNumber: number;
  urn: string;
  text?: {
    en_US: string;
  };
  canonicalPath?: string;
  parentCanonicalPath?: string;
  children?: Standard[];
}

interface Context {
  region: Region | null;
  setRegion: Dispatch<SetStateAction<Region | null>>;

  subregion: Subregion | null;
  setSubregion: Dispatch<SetStateAction<Subregion | null>>;

  authority: Authority | null;
  setAuthority: Dispatch<SetStateAction<Authority | null>>;

  standard: Standard | null;
  setStandard: Dispatch<SetStateAction<Standard | null>>;

  doc: SerializedDocument | null;
  setDoc: Dispatch<SetStateAction<SerializedDocument | null>>;

  blockCount: number | null;
  setBlockCount: Dispatch<SetStateAction<number | null>>;
}

export const Context = createContext<Context>({} as Context);

export function useCreateScopeSequenceContext() {
  return useContext(Context);
}
