"use client";

import { useState, useCallback, useMemo } from "react";
import { createWorker } from "tesseract.js";

interface ProcessedImage {
  id: string;
  name: string;
  size: number;
  type: string;
  preview: string;
  extractedText: string;
  confidence: number;
  uploadedAt: Date;
  saved?: boolean;
  cloudUrl?: string;
}

interface ImageParserComponentProps {
  onImageProcessed?: (image: ProcessedImage) => void;
}

export default function ImageParserComponent({
  onImageProcessed,
}: ImageParserComponentProps) {
  const [processedImages, setProcessedImages] = useState<ProcessedImage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [progress, setProgress] = useState<{ [key: string]: number }>({});
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [saveLocation, setSaveLocation] = useState<"local" | "cloud">("local");

  const supportedImageTypes = useMemo(
    () => ["image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"],
    []
  );

  const saveToLocal = useCallback(async (image: ProcessedImage) => {
    try {
      // Create a downloadable file with extracted text
      const fileName = `${image.name.split(".")[0]}_extracted_text.txt`;

      // For demo purposes, we'll just mark it as saved
      // In a real app, you might use the File System Access API or trigger a download
      image.saved = true;

      console.log(
        `Text extracted from ${image.name} would be saved as ${fileName}`
      );
    } catch (error) {
      console.error("Error saving to local:", error);
    }
  }, []);

  const saveToCloud = useCallback(async (image: ProcessedImage) => {
    try {
      // Simulate cloud upload
      // In a real app, you would upload to your cloud storage service
      await new Promise((resolve) => setTimeout(resolve, 1000));

      image.saved = true;
      image.cloudUrl = `https://cloud-storage.example.com/images/${image.id}`;

      console.log(`Image ${image.name} and extracted text saved to cloud`);
    } catch (error) {
      console.error("Error saving to cloud:", error);
    }
  }, []);

  const processImages = useCallback(
    async (files: File[]) => {
      setIsProcessing(true);

      for (const file of files) {
        const imageId = `img_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        try {
          // Create preview
          const preview = URL.createObjectURL(file);

          // Initialize Tesseract worker
          const worker = await createWorker("eng");

          // Track progress
          worker.setParameters({
            tessedit_char_whitelist:
              "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?;:'\"()[]{}@#$%^&*-+=<>/\\|`~_\n\t ",
          });

          // Set initial progress
          setProgress((prev) => ({
            ...prev,
            [imageId]: 0,
          }));

          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setProgress((prev) => {
              const currentProgress = prev[imageId] || 0;
              if (currentProgress < 90) {
                return {
                  ...prev,
                  [imageId]: currentProgress + Math.random() * 20,
                };
              }
              return prev;
            });
          }, 500);

          // Process the image
          const {
            data: { text, confidence },
          } = await worker.recognize(file);

          // Clear progress interval
          clearInterval(progressInterval);

          // Set final progress
          setProgress((prev) => ({
            ...prev,
            [imageId]: 100,
          }));

          await worker.terminate();

          const processedImage: ProcessedImage = {
            id: imageId,
            name: file.name,
            size: file.size,
            type: file.type,
            preview,
            extractedText: text.trim(),
            confidence: Math.round(confidence),
            uploadedAt: new Date(),
          };

          // Save based on selected location
          if (saveLocation === "local") {
            await saveToLocal(processedImage);
          } else {
            await saveToCloud(processedImage);
          }

          setProcessedImages((prev) => [...prev, processedImage]);
          onImageProcessed?.(processedImage);

          // Clear progress for this image
          setProgress((prev) => {
            const newProgress = { ...prev };
            delete newProgress[imageId];
            return newProgress;
          });
        } catch (error) {
          console.error("Error processing image:", error);

          // Clear progress for this image on error
          setProgress((prev) => {
            const newProgress = { ...prev };
            delete newProgress[imageId];
            return newProgress;
          });
        }
      }

      setIsProcessing(false);
    },
    [saveLocation, onImageProcessed, saveToLocal, saveToCloud]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      const imageFiles = files.filter((file) =>
        supportedImageTypes.includes(file.type)
      );

      if (imageFiles.length > 0) {
        processImages(imageFiles);
      }
    },
    [supportedImageTypes, processImages]
  );

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      const imageFiles = files.filter((file) =>
        supportedImageTypes.includes(file.type)
      );

      if (imageFiles.length > 0) {
        processImages(imageFiles);
      }
    },
    [supportedImageTypes, processImages]
  );

  const downloadExtractedText = useCallback((image: ProcessedImage) => {
    const textBlob = new Blob([image.extractedText], { type: "text/plain" });
    const url = URL.createObjectURL(textBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${image.name.split(".")[0]}_extracted_text.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  const downloadAllExtractedText = useCallback(() => {
    const allText = processedImages
      .filter(
        (img) => selectedImages.length === 0 || selectedImages.includes(img.id)
      )
      .map((img) => `=== ${img.name} ===\n${img.extractedText}\n\n`)
      .join("");

    const textBlob = new Blob([allText], { type: "text/plain" });
    const url = URL.createObjectURL(textBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "all_extracted_text.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [processedImages, selectedImages]);

  const toggleImageSelection = useCallback((imageId: string) => {
    setSelectedImages((prev) =>
      prev.includes(imageId)
        ? prev.filter((id) => id !== imageId)
        : [...prev, imageId]
    );
  }, []);

  const selectAllImages = useCallback(() => {
    setSelectedImages(processedImages.map((img) => img.id));
  }, [processedImages]);

  const clearSelection = useCallback(() => {
    setSelectedImages([]);
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 dark:text-white mb-2">
          🖼️ Image Text Parser
        </h2>
        <p className="text-slate-600 dark:text-slate-400">
          Extract text from images using OCR technology. Upload images and get
          searchable text content.
        </p>
      </div>

      {/* Settings */}
      <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
        <h3 className="font-semibold mb-3 text-slate-800 dark:text-white">
          Save Settings
        </h3>
        <div className="flex gap-4">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="saveLocation"
              value="local"
              checked={saveLocation === "local"}
              onChange={(e) =>
                setSaveLocation(e.target.value as "local" | "cloud")
              }
              className="text-blue-500"
            />
            <span className="text-slate-700 dark:text-slate-300">
              💾 Save Locally
            </span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="saveLocation"
              value="cloud"
              checked={saveLocation === "cloud"}
              onChange={(e) =>
                setSaveLocation(e.target.value as "local" | "cloud")
              }
              className="text-blue-500"
            />
            <span className="text-slate-700 dark:text-slate-300">
              ☁️ Save to Cloud
            </span>
          </label>
        </div>
      </div>

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
          isDragging
            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : "border-slate-300 dark:border-slate-600 hover:border-slate-400"
        } ${isProcessing ? "opacity-50 pointer-events-none" : ""}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="text-6xl mb-4">🖼️</div>
        <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
          Drop Images Here or Click to Upload
        </h3>
        <p className="text-slate-500 dark:text-slate-400 mb-4">
          Supports JPEG, PNG, GIF, BMP, and WebP formats
        </p>
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
          id="image-upload"
          disabled={isProcessing}
        />
        <label
          htmlFor="image-upload"
          className="inline-flex items-center gap-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors cursor-pointer"
        >
          📁 Select Images
        </label>
      </div>

      {/* Processing Progress */}
      {Object.keys(progress).length > 0 && (
        <div className="space-y-2">
          <h3 className="font-semibold text-slate-800 dark:text-white">
            Processing Images...
          </h3>
          {Object.entries(progress).map(([imageId, progressValue]) => (
            <div
              key={imageId}
              className="bg-slate-200 dark:bg-slate-700 rounded-lg p-3"
            >
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  Processing image...
                </span>
                <span className="text-sm font-semibold text-slate-800 dark:text-white">
                  {progressValue}%
                </span>
              </div>
              <div className="w-full bg-slate-300 dark:bg-slate-600 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progressValue}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Processed Images */}
      {processedImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-slate-800 dark:text-white">
              Processed Images ({processedImages.length})
            </h3>
            <div className="flex gap-2">
              <button
                onClick={selectAllImages}
                className="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
              >
                Select All
              </button>
              <button
                onClick={clearSelection}
                className="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors"
              >
                Clear Selection
              </button>
              <button
                onClick={downloadAllExtractedText}
                className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                disabled={processedImages.length === 0}
              >
                📥 Download All Text
              </button>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {processedImages.map((image) => (
              <div
                key={image.id}
                className={`bg-white dark:bg-slate-700 rounded-lg shadow-md overflow-hidden border-2 transition-all ${
                  selectedImages.includes(image.id)
                    ? "border-blue-500 shadow-lg"
                    : "border-transparent"
                }`}
              >
                <div className="relative">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={image.preview}
                    alt={image.name}
                    className="w-full h-48 object-cover"
                  />
                  <button
                    onClick={() => toggleImageSelection(image.id)}
                    className={`absolute top-2 right-2 w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      selectedImages.includes(image.id)
                        ? "bg-blue-500 border-blue-500 text-white"
                        : "bg-white border-slate-300 text-slate-600"
                    }`}
                  >
                    {selectedImages.includes(image.id) && "✓"}
                  </button>
                  {image.saved && (
                    <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
                      {saveLocation === "cloud" ? "☁️ Saved" : "💾 Saved"}
                    </div>
                  )}
                </div>

                <div className="p-4">
                  <h4 className="font-semibold text-slate-800 dark:text-white truncate mb-2">
                    {image.name}
                  </h4>

                  <div className="text-xs text-slate-500 dark:text-slate-400 mb-2">
                    Size: {(image.size / 1024 / 1024).toFixed(2)} MB •
                    Confidence: {image.confidence}%
                  </div>

                  <div className="mb-3">
                    <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                      Extracted Text:
                    </div>
                    <div className="text-xs text-slate-600 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 p-2 rounded max-h-20 overflow-y-auto">
                      {image.extractedText || "No text detected"}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => downloadExtractedText(image)}
                      className="flex-1 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                      disabled={!image.extractedText}
                    >
                      📥 Download Text
                    </button>
                    {image.cloudUrl && (
                      <button
                        onClick={() => window.open(image.cloudUrl, "_blank")}
                        className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                      >
                        🔗 View Cloud
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Summary Stats */}
      {processedImages.length > 0 && (
        <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-4">
          <h3 className="font-semibold mb-3 text-slate-800 dark:text-white">
            Summary
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-500">
                {processedImages.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Images Processed
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-500">
                {processedImages.filter((img) => img.extractedText).length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Text Extracted
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-500">
                {Math.round(
                  processedImages.reduce(
                    (acc, img) => acc + img.confidence,
                    0
                  ) / processedImages.length
                ) || 0}
                %
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Avg. Confidence
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-500">
                {processedImages.filter((img) => img.saved).length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Files Saved
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
