import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Target,
  Zap,
  Filter,
  Download,
  X,
  Clock,
  Hash,
  Type,
  BookOpen,
} from "lucide-react";
import { useState } from "react";

interface AnalysisData {
  id: string;
  fileName: string;
  wordCount: number;
  readabilityScore: number;
  sentimentScore: number;
  complexityScore: number;
  keyWords: string[];
  toneAnalysis: {
    formal: number;
    conversational: number;
    technical: number;
    educational: number;
  };
  structureAnalysis: {
    headings: number;
    paragraphs: number;
    sentences: number;
    avgSentenceLength: number;
  };
}

const mockAnalysisData: AnalysisData[] = [
  {
    id: "1",
    fileName: "Foreign Language Resource Collection - Grade 8 (65).jpg",
    wordCount: 1250,
    readabilityScore: 8.2,
    sentimentScore: 0.7,
    complexityScore: 6.5,
    keyWords: [
      "language",
      "vocabulary",
      "grammar",
      "communication",
      "cultural",
    ],
    toneAnalysis: {
      formal: 70,
      conversational: 20,
      technical: 30,
      educational: 85,
    },
    structureAnalysis: {
      headings: 5,
      paragraphs: 12,
      sentences: 67,
      avgSentenceLength: 18.6,
    },
  },
  {
    id: "2",
    fileName: "Mathematics Problem Set - Grade 7",
    wordCount: 890,
    readabilityScore: 7.8,
    sentimentScore: 0.5,
    complexityScore: 8.2,
    keyWords: [
      "algebra",
      "equations",
      "problem-solving",
      "mathematics",
      "variables",
    ],
    toneAnalysis: {
      formal: 85,
      conversational: 10,
      technical: 90,
      educational: 95,
    },
    structureAnalysis: {
      headings: 8,
      paragraphs: 15,
      sentences: 45,
      avgSentenceLength: 19.8,
    },
  },
  {
    id: "3",
    fileName: "Science Lab Report Template",
    wordCount: 650,
    readabilityScore: 9.1,
    sentimentScore: 0.6,
    complexityScore: 7.3,
    keyWords: [
      "experiment",
      "hypothesis",
      "methodology",
      "results",
      "conclusion",
    ],
    toneAnalysis: {
      formal: 95,
      conversational: 5,
      technical: 80,
      educational: 75,
    },
    structureAnalysis: {
      headings: 6,
      paragraphs: 8,
      sentences: 32,
      avgSentenceLength: 20.3,
    },
  },
];

export function Component() {
  const [analysisMode, setAnalysisMode] = useState<"overall" | "per-file">(
    "overall"
  );
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [showDetailedView, setShowDetailedView] = useState(false);
  const [selectedFileForDetails, setSelectedFileForDetails] =
    useState<AnalysisData | null>(null);

  const overallStats = {
    totalFiles: mockAnalysisData.length,
    totalWords: mockAnalysisData.reduce((sum, item) => sum + item.wordCount, 0),
    avgReadability:
      mockAnalysisData.reduce((sum, item) => sum + item.readabilityScore, 0) /
      mockAnalysisData.length,
    avgSentiment:
      mockAnalysisData.reduce((sum, item) => sum + item.sentimentScore, 0) /
      mockAnalysisData.length,
    avgComplexity:
      mockAnalysisData.reduce((sum, item) => sum + item.complexityScore, 0) /
      mockAnalysisData.length,
    mostCommonWords: [
      "educational",
      "learning",
      "students",
      "knowledge",
      "understanding",
    ],
  };

  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles((prev) =>
      prev.includes(fileId)
        ? prev.filter((id) => id !== fileId)
        : [...prev, fileId]
    );
  };

  const getScoreColor = (
    score: number,
    type: "readability" | "sentiment" | "complexity"
  ) => {
    if (type === "readability" || type === "sentiment") {
      if (score >= 8) return "text-green-600 bg-green-50";
      if (score >= 6) return "text-yellow-600 bg-yellow-50";
      return "text-red-600 bg-red-50";
    } else {
      if (score >= 8) return "text-red-600 bg-red-50";
      if (score >= 6) return "text-yellow-600 bg-yellow-50";
      return "text-green-600 bg-green-50";
    }
  };

  const getToneBarColor = (value: number) => {
    if (value >= 80) return "bg-green-500";
    if (value >= 60) return "bg-yellow-500";
    if (value >= 40) return "bg-orange-500";
    return "bg-red-500";
  };

  const openFileDetails = (file: AnalysisData) => {
    setSelectedFileForDetails(file);
    setShowDetailedView(true);
  };

  const closeFileDetails = () => {
    setShowDetailedView(false);
    setSelectedFileForDetails(null);
  };

  const exportOverallReport = () => {
    const reportData = {
      reportType: "Overall Content Analysis Report",
      generatedAt: new Date().toISOString(),
      summary: {
        totalFiles: overallStats.totalFiles,
        totalWords: overallStats.totalWords,
        averageReadability: Number(overallStats.avgReadability.toFixed(1)),
        averageSentiment: Number((overallStats.avgSentiment * 100).toFixed(1)),
        averageComplexity: Number(overallStats.avgComplexity.toFixed(1)),
        mostCommonWords: overallStats.mostCommonWords,
      },
      overallToneDistribution: {
        educational: 85,
        formal: 75,
        technical: 67,
        conversational: 12,
      },
      fileAnalyses: mockAnalysisData.map((file) => ({
        fileName: file.fileName,
        wordCount: file.wordCount,
        readabilityScore: file.readabilityScore,
        sentimentScore: file.sentimentScore,
        complexityScore: file.complexityScore,
        keyWords: file.keyWords,
        toneAnalysis: file.toneAnalysis,
        structureAnalysis: file.structureAnalysis,
      })),
      recommendations: [
        "Consider simplifying complex mathematical terminology for better readability",
        "Maintain the positive educational tone across all content",
        "Add more conversational elements to engage students",
        "Ensure consistent heading structure across documents",
      ],
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `content-analysis-report-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportFileAnalysis = (file: AnalysisData) => {
    const analysisData = {
      reportType: "Individual File Analysis Report",
      generatedAt: new Date().toISOString(),
      fileDetails: {
        fileName: file.fileName,
        wordCount: file.wordCount,
        readabilityScore: file.readabilityScore,
        sentimentScore: file.sentimentScore,
        complexityScore: file.complexityScore,
      },
      keyWords: file.keyWords,
      toneAnalysis: file.toneAnalysis,
      structureAnalysis: {
        ...file.structureAnalysis,
        wordsPerParagraph: Math.round(
          file.wordCount / file.structureAnalysis.paragraphs
        ),
        estimatedReadingTime: Math.ceil(file.wordCount / 200),
      },
      insights: {
        readabilityAssessment:
          file.readabilityScore >= 8
            ? "Excellent readability"
            : file.readabilityScore >= 6
              ? "Good readability"
              : "Needs improvement",
        sentimentAssessment:
          file.sentimentScore >= 0.6
            ? "Positive tone"
            : file.sentimentScore >= 0.4
              ? "Neutral tone"
              : "Negative tone",
        complexityAssessment:
          file.complexityScore >= 8
            ? "High complexity"
            : file.complexityScore >= 6
              ? "Moderate complexity"
              : "Low complexity",
      },
      recommendations: generateFileRecommendations(file),
    };

    const blob = new Blob([JSON.stringify(analysisData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${file.fileName.replace(/[^a-z0-9]/gi, "_").toLowerCase()}-analysis-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const generateFileRecommendations = (file: AnalysisData): string[] => {
    const recommendations = [];

    if (file.readabilityScore < 6) {
      recommendations.push(
        "Consider simplifying sentence structure and vocabulary for better readability"
      );
    }

    if (file.complexityScore > 8) {
      recommendations.push(
        "Break down complex concepts into smaller, more digestible sections"
      );
    }

    if (file.sentimentScore < 0.4) {
      recommendations.push("Add more positive language and encouraging tone");
    }

    if (file.toneAnalysis.conversational < 20) {
      recommendations.push(
        "Include more conversational elements to engage readers"
      );
    }

    if (file.structureAnalysis.avgSentenceLength > 20) {
      recommendations.push(
        "Consider shortening average sentence length for better comprehension"
      );
    }

    if (file.structureAnalysis.headings < 3) {
      recommendations.push(
        "Add more headings to improve content structure and navigation"
      );
    }

    if (recommendations.length === 0) {
      recommendations.push(
        "Content analysis shows good overall quality - maintain current standards"
      );
    }

    return recommendations;
  };

  return (
    <div className="w-full h-full flex flex-col bg-neutral-50">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200 px-6 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-neutral-900">
              Context Analytics
            </h1>
            <p className="text-sm text-neutral-600 mt-1">
              Analyze word usage, tone, style, and structure of your uploaded
              content.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center border border-neutral-300 rounded-md overflow-hidden">
              <button
                onClick={() => setAnalysisMode("overall")}
                className={`px-4 py-2 text-sm transition-colors ${
                  analysisMode === "overall"
                    ? "bg-primary-600 text-white"
                    : "bg-white text-neutral-600 hover:bg-neutral-50"
                }`}
              >
                Overall Analysis
              </button>
              <button
                onClick={() => setAnalysisMode("per-file")}
                className={`px-4 py-2 text-sm transition-colors ${
                  analysisMode === "per-file"
                    ? "bg-primary-600 text-white"
                    : "bg-white text-neutral-600 hover:bg-neutral-50"
                }`}
              >
                Per-File Analysis
              </button>
            </div>
            <button
              onClick={exportOverallReport}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 transition-colors shadow-sm"
            >
              <Download className="w-4 h-4" />
              Export Report
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-auto">
        {analysisMode === "overall" ? (
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg border border-neutral-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">
                      Total Files
                    </p>
                    <p className="text-2xl font-bold text-neutral-900">
                      {overallStats.totalFiles}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-neutral-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">
                      Total Words
                    </p>
                    <p className="text-2xl font-bold text-neutral-900">
                      {overallStats.totalWords.toLocaleString()}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-neutral-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">
                      Avg Readability
                    </p>
                    <p className="text-2xl font-bold text-neutral-900">
                      {overallStats.avgReadability.toFixed(1)}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <Brain className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-neutral-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600">
                      Avg Sentiment
                    </p>
                    <p className="text-2xl font-bold text-neutral-900">
                      {(overallStats.avgSentiment * 100).toFixed(0)}%
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Target className="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Analysis Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Word Usage Analysis */}
              <div className="bg-white rounded-lg border border-neutral-200">
                <div className="px-6 py-4 border-b border-neutral-200">
                  <h3 className="text-lg font-semibold text-neutral-900">
                    Most Common Words
                  </h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {overallStats.mostCommonWords.map((word, index) => (
                      <div
                        key={word}
                        className="flex items-center justify-between"
                      >
                        <span className="text-neutral-700 font-medium">
                          {word}
                        </span>
                        <div className="flex items-center gap-3">
                          <div className="w-32 bg-neutral-200 rounded-full h-2">
                            <div
                              className="bg-primary-600 h-2 rounded-full"
                              style={{ width: `${100 - index * 15}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-neutral-500 w-12">
                            {100 - index * 15}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Tone Distribution */}
              <div className="bg-white rounded-lg border border-neutral-200">
                <div className="px-6 py-4 border-b border-neutral-200">
                  <h3 className="text-lg font-semibold text-neutral-900">
                    Overall Tone Distribution
                  </h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {Object.entries({
                      Educational: 85,
                      Formal: 75,
                      Technical: 67,
                      Conversational: 12,
                    }).map(([tone, value]) => (
                      <div
                        key={tone}
                        className="flex items-center justify-between"
                      >
                        <span className="text-neutral-700 font-medium">
                          {tone}
                        </span>
                        <div className="flex items-center gap-3">
                          <div className="w-32 bg-neutral-200 rounded-full h-2">
                            <div
                              className={`${getToneBarColor(value)} h-2 rounded-full`}
                              style={{ width: `${value}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-neutral-500 w-12">
                            {value}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Per-File Analysis */
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-neutral-200">
              <div className="px-6 py-4 border-b border-neutral-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-neutral-900">
                    File Analysis
                  </h3>
                  <div className="flex items-center gap-2">
                    <Filter className="w-4 h-4 text-neutral-500" />
                    <span className="text-sm text-neutral-600">
                      {mockAnalysisData.length} files analyzed
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {mockAnalysisData.map((file) => (
                    <div
                      key={file.id}
                      className="border border-neutral-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={selectedFiles.includes(file.id)}
                            onChange={() => toggleFileSelection(file.id)}
                            className="rounded border-neutral-300 text-purple-600 focus:ring-purple-500"
                          />
                          <div>
                            <h4 className="font-medium text-neutral-900">
                              {file.fileName}
                            </h4>
                            <p className="text-sm text-neutral-600">
                              {file.wordCount.toLocaleString()} words
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => openFileDetails(file)}
                          className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                        >
                          View Details
                        </button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div
                          className={`px-3 py-2 rounded-lg ${getScoreColor(file.readabilityScore, "readability")}`}
                        >
                          <div className="text-sm font-medium">Readability</div>
                          <div className="text-lg font-bold">
                            {file.readabilityScore.toFixed(1)}/10
                          </div>
                        </div>
                        <div
                          className={`px-3 py-2 rounded-lg ${getScoreColor(file.sentimentScore, "sentiment")}`}
                        >
                          <div className="text-sm font-medium">Sentiment</div>
                          <div className="text-lg font-bold">
                            {(file.sentimentScore * 100).toFixed(0)}%
                          </div>
                        </div>
                        <div
                          className={`px-3 py-2 rounded-lg ${getScoreColor(file.complexityScore, "complexity")}`}
                        >
                          <div className="text-sm font-medium">Complexity</div>
                          <div className="text-lg font-bold">
                            {file.complexityScore.toFixed(1)}/10
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        <span className="text-xs text-neutral-600 font-medium">
                          Key Words:
                        </span>
                        {file.keyWords.slice(0, 4).map((word) => (
                          <span
                            key={word}
                            className="px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs"
                          >
                            {word}
                          </span>
                        ))}
                        {file.keyWords.length > 4 && (
                          <span className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded text-xs">
                            +{file.keyWords.length - 4} more
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Detailed View Modal */}
      {showDetailedView && selectedFileForDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-neutral-200 flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-neutral-900">
                  Detailed Analysis
                </h2>
                <p className="text-sm text-neutral-600 mt-1">
                  {selectedFileForDetails.fileName}
                </p>
              </div>
              <button
                onClick={closeFileDetails}
                className="p-2 hover:bg-neutral-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-neutral-500" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Overview Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div
                    className={`p-4 rounded-lg ${getScoreColor(selectedFileForDetails.readabilityScore, "readability")}`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <Brain className="w-5 h-5" />
                      <h3 className="font-semibold">Readability Score</h3>
                    </div>
                    <p className="text-2xl font-bold">
                      {selectedFileForDetails.readabilityScore.toFixed(1)}/10
                    </p>
                    <p className="text-sm mt-1">
                      {selectedFileForDetails.readabilityScore >= 8
                        ? "Excellent readability"
                        : selectedFileForDetails.readabilityScore >= 6
                          ? "Good readability"
                          : "Needs improvement"}
                    </p>
                  </div>

                  <div
                    className={`p-4 rounded-lg ${getScoreColor(selectedFileForDetails.sentimentScore, "sentiment")}`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <Target className="w-5 h-5" />
                      <h3 className="font-semibold">Sentiment Analysis</h3>
                    </div>
                    <p className="text-2xl font-bold">
                      {(selectedFileForDetails.sentimentScore * 100).toFixed(0)}
                      %
                    </p>
                    <p className="text-sm mt-1">
                      {selectedFileForDetails.sentimentScore >= 0.6
                        ? "Positive tone"
                        : selectedFileForDetails.sentimentScore >= 0.4
                          ? "Neutral tone"
                          : "Negative tone"}
                    </p>
                  </div>

                  <div
                    className={`p-4 rounded-lg ${getScoreColor(selectedFileForDetails.complexityScore, "complexity")}`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <Zap className="w-5 h-5" />
                      <h3 className="font-semibold">Complexity Score</h3>
                    </div>
                    <p className="text-2xl font-bold">
                      {selectedFileForDetails.complexityScore.toFixed(1)}/10
                    </p>
                    <p className="text-sm mt-1">
                      {selectedFileForDetails.complexityScore >= 8
                        ? "High complexity"
                        : selectedFileForDetails.complexityScore >= 6
                          ? "Moderate complexity"
                          : "Low complexity"}
                    </p>
                  </div>
                </div>

                {/* Tone Analysis */}
                <div className="bg-neutral-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4 flex items-center gap-2">
                    <Type className="w-5 h-5" />
                    Tone Distribution
                  </h3>
                  <div className="space-y-4">
                    {Object.entries(selectedFileForDetails.toneAnalysis).map(
                      ([tone, value]) => (
                        <div
                          key={tone}
                          className="flex items-center justify-between"
                        >
                          <span className="text-neutral-700 font-medium capitalize">
                            {tone}
                          </span>
                          <div className="flex items-center gap-3">
                            <div className="w-40 bg-neutral-200 rounded-full h-3">
                              <div
                                className={`${getToneBarColor(value)} h-3 rounded-full transition-all duration-300`}
                                style={{ width: `${value}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-neutral-600 w-12 font-medium">
                              {value}%
                            </span>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>

                {/* Structure Analysis */}
                <div className="bg-neutral-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    Content Structure
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-white rounded-lg p-4 border border-neutral-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Hash className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-neutral-700">
                          Headings
                        </span>
                      </div>
                      <p className="text-xl font-bold text-neutral-900">
                        {selectedFileForDetails.structureAnalysis.headings}
                      </p>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-neutral-200">
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-neutral-700">
                          Paragraphs
                        </span>
                      </div>
                      <p className="text-xl font-bold text-neutral-900">
                        {selectedFileForDetails.structureAnalysis.paragraphs}
                      </p>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-neutral-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Type className="w-4 h-4 text-purple-600" />
                        <span className="text-sm font-medium text-neutral-700">
                          Sentences
                        </span>
                      </div>
                      <p className="text-xl font-bold text-neutral-900">
                        {selectedFileForDetails.structureAnalysis.sentences}
                      </p>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-neutral-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="w-4 h-4 text-orange-600" />
                        <span className="text-sm font-medium text-neutral-700">
                          Avg Length
                        </span>
                      </div>
                      <p className="text-xl font-bold text-neutral-900">
                        {
                          selectedFileForDetails.structureAnalysis
                            .avgSentenceLength
                        }
                      </p>
                      <p className="text-xs text-neutral-500">words/sentence</p>
                    </div>
                  </div>
                </div>

                {/* Key Words Analysis */}
                <div className="bg-neutral-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4">
                    Key Words & Themes
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {selectedFileForDetails.keyWords.map((word, index) => (
                      <span
                        key={word}
                        className={`px-4 py-2 rounded-full text-sm font-medium ${
                          index < 2
                            ? "bg-primary-500 text-white"
                            : index < 4
                              ? "bg-primary-100 text-primary-700"
                              : "bg-neutral-200 text-neutral-700"
                        }`}
                      >
                        {word}
                      </span>
                    ))}
                  </div>
                  <div className="mt-4 p-4 bg-white rounded-lg border border-neutral-200">
                    <h4 className="font-medium text-neutral-900 mb-2">
                      Content Statistics
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Total Words:</span>
                        <span className="font-medium">
                          {selectedFileForDetails.wordCount.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">
                          Unique Keywords:
                        </span>
                        <span className="font-medium">
                          {selectedFileForDetails.keyWords.length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">
                          Words per Paragraph:
                        </span>
                        <span className="font-medium">
                          {Math.round(
                            selectedFileForDetails.wordCount /
                              selectedFileForDetails.structureAnalysis
                                .paragraphs
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Reading Time:</span>
                        <span className="font-medium">
                          {Math.ceil(selectedFileForDetails.wordCount / 200)}{" "}
                          min
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-end gap-3 pt-4 border-t border-neutral-200">
                  <button
                    onClick={closeFileDetails}
                    className="px-4 py-2 text-neutral-600 hover:text-neutral-800 transition-colors"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => exportFileAnalysis(selectedFileForDetails)}
                    className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    Export Analysis
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
