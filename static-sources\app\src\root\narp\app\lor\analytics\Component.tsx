import { BarChart } from "lucide-react";

export function Component() {
  return (
    <div className="w-full h-full flex flex-col bg-neutral-50">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200 px-6 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-neutral-900">
              Analytics
            </h1>
            <p className="text-sm text-neutral-600 mt-1">
              Insights and statistics for your Learning Object Repository
              content.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="bg-white rounded-lg border border-neutral-200">
          <div className="p-12">
            <div className="text-center">
              <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-6">
                <BarChart className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-900 mb-2">
                Analytics feature coming soon
              </h3>
              <p className="text-neutral-600">
                This feature is currently under development
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
