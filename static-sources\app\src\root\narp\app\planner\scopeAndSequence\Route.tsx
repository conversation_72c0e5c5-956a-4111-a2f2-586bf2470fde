import { RouteObject } from "react-router-dom";
import { default as EditRoute } from "@/root/narp/app/planner/scopeAndSequence/edit/Route";
import { default as CreateRoute } from "@/root/narp/app/planner/scopeAndSequence/create/Route.tsx";
import { default as IndexRoute } from "./_index/Route";

const Route: RouteObject = {
  path: "scopeAndSequence",
  children: [IndexRoute, EditRoute, CreateRoute],
};

export default Route;
