import { ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import {
  Activity,
  ActivityType,
  Context,
  EntityID,
  Goal,
  GoalPriority,
  LexicalCategory,
  SaveState,
  Term,
  Topic,
} from "./Context";
import { GradeLevel } from "@/util/standards.ts";
import { SerializedDocument } from "@/root/narp/app/planner/_util/plannerDocuments/ver2.ts";
import { Operation } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/operation.ts";
import { usePushError } from "@/util/components/Alerts/util.tsx";
import {
  GoalAddOperation,
  GoalOperation,
  GoalRemoveOperation,
  GoalSetDescriptionOperation,
  GoalSetParentOperation,
  GoalSetPriorityOperation,
  GoalSetTypeOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/goal.ts";
import {
  OutlineAddKeywordOperation,
  OutlineOperation,
  OutlineRemoveKeywordOperation,
  OutlineSetAbstractOperation,
  OutlineSetGradeLevelOperation,
  OutlineSetTitleOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/outline.ts";
import {
  TopicAddDependencyOperation,
  TopicAddOperation,
  TopicMoveOperation,
  TopicOperation,
  TopicRemoveDependencyOperation,
  TopicRemoveOperation,
  TopicSetDurationOperation,
  TopicSetEssentialQuestionsOperation,
  TopicSetEvidenceOfLearningOperation,
  TopicSetNameOperation,
  TopicSetNotesOperation,
  TopicSetResourcesOperation,
  TopicSetStartOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/topic.ts";
import {
  TermAddOperation,
  TermOperation,
  TermRemoveOperation,
  TermSetCategoryOperation,
  TermSetDefinitionOperation,
  TermSetParentOperation,
  TermSetWordOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/term.ts";
import {
  ActivityAddOperation,
  ActivityOperation,
  ActivityRemoveOperation,
  ActivitySetDeliverablesOperation,
  ActivitySetDescriptionOperation,
  ActivitySetDurationOperation,
  ActivitySetParentOperation,
  ActivitySetResourcesOperation,
  ActivitySetTitleOperation,
  ActivitySetTypeOperation,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/operations/activity.ts";
import { getNonCollidingID } from "@/util/getNonCollidingID.ts";
import { AlertType, usePushAlert } from "@/util/components/Alerts/Context.tsx";
import usePOST from "@/util/api/usePOST.tsx";
import { useTopRealm } from "@/root/narp/app/_components/Context";
import { useParams } from "react-router-dom";

// region Utility Functions
function arrayMove<T>(array: T[], from: number, to: number): T[] {
  const newArray = array.slice();
  newArray.splice(
    to < 0 ? newArray.length + to : to,
    0,
    newArray.splice(from, 1)[0]
  );
  return newArray;
}

// endregion

const _operationLimit = 100;

export function Provider({
  children,
  data,
}: {
  children: ReactNode;
  data: SerializedDocument;
}) {
  const pushError = usePushError();

  // region States
  const [undoStack, setUndoStack] = useState<Operation[]>([]);
  const [redoStack, setRedoStack] = useState<Operation[]>([]);

  const [title, setTitle] = useState<string>(data.title);
  const [abstract, setAbstract] = useState<string>(data.abstract);
  const [gradeLevel, setGradeLevel] = useState<GradeLevel>(data.gradeLevel);
  const [keywords, setKeywords] = useState<string[]>(data.keywords);

  const [goals, setGoals] = useState<Goal[]>(data.goals);
  const goalMap = useMemo<Map<EntityID, Goal>>(() => {
    return new Map(goals.map((entry) => [entry.id, entry]));
  }, [goals]);
  const goalIndexMap = useMemo<Map<EntityID, number>>(() => {
    return new Map(goals.map((entry, i) => [entry.id, i]));
  }, [goals]);

  const [terms, setTerms] = useState<Term[]>(data.terms as Term[]);
  const termMap = useMemo<Map<EntityID, Term>>(() => {
    return new Map(terms.map((entry) => [entry.id, entry]));
  }, [terms]);
  const termIndexMap = useMemo<Map<EntityID, number>>(() => {
    return new Map(terms.map((entry, i) => [entry.id, i]));
  }, [terms]);

  const [topics, setTopics] = useState<Topic[]>(data.topics);
  const topicMap = useMemo<Map<EntityID, Topic>>(() => {
    return new Map(topics.map((entry) => [entry.id, entry]));
  }, [topics]);
  const topicIndexMap = useMemo<Map<EntityID, number>>(() => {
    return new Map(topics.map((entry, i) => [entry.id, i]));
  }, [topics]);

  const [activities, setActivities] = useState<Activity[]>(
    data.activities as Activity[]
  );
  const activityMap = useMemo<Map<EntityID, Activity>>(() => {
    return new Map(activities.map((entry) => [entry.id, entry]));
  }, [activities]);
  const activityIndexMap = useMemo<Map<EntityID, number>>(() => {
    return new Map(activities.map((entry, i) => [entry.id, i]));
  }, [activities]);

  // endregion

  // region Outline

  const setOutlineTitle = useCallback(
    (value: string): OutlineSetTitleOperation => {
      setTitle(value);
      return {
        action: "SET_TITLE",
        data: { previous: title },
        namespace: "OUTLINE",
      };
    },
    [title]
  );

  const setOutlineAbstract = useCallback(
    (value: string): OutlineSetAbstractOperation => {
      setAbstract(value);
      return {
        action: "SET_ABSTRACT",
        data: { previous: abstract },
        namespace: "OUTLINE",
      };
    },
    [abstract]
  );

  const setOutlineGradeLevel = useCallback(
    (value: GradeLevel): OutlineSetGradeLevelOperation => {
      setGradeLevel(value);
      return {
        action: "SET_GRADE_LEVEL",
        data: { previous: gradeLevel },
        namespace: "OUTLINE",
      };
    },
    [gradeLevel]
  );

  const addOutlineKeyword = useCallback(
    (word: string, index?: number): OutlineAddKeywordOperation => {
      let actionIndex: number;
      if (index === undefined) {
        actionIndex = keywords.length;
        keywords.push(word);
      } else {
        actionIndex = index;
        keywords.splice(index, 0, word);
      }

      setKeywords([...keywords]);

      return {
        action: "ADD_KEYWORD",
        namespace: "OUTLINE",
        data: { index: actionIndex },
      };
    },
    [keywords]
  );

  const removeOutlineKeyword = useCallback(
    (index: number): OutlineRemoveKeywordOperation | null => {
      if (keywords.at(index) === undefined) return null;
      const removed = keywords.splice(index, 1)[0]!;

      setKeywords([...keywords]);

      return {
        action: "REMOVE_KEYWORD",
        data: { index: index, value: removed },
        namespace: "OUTLINE",
      };
    },
    [keywords]
  );

  // endregion

  // region Terms

  const getTerm = useCallback(
    (id: EntityID): Term | null => {
      return termMap.get(id) || null;
    },
    [termMap]
  );

  const addTerm = useCallback(
    (value?: Term, index?: number): TermAddOperation => {
      const newTerm: Term = value || {
        id: getNonCollidingID(terms.map((entry) => entry.id)),
        parentTopic: null,
        word: "New Word",
        category: LexicalCategory.NOUN,
        definition: "New Word's definition",
      };

      if (index === undefined) {
        terms.push(newTerm);
      } else {
        terms.splice(index, 0, newTerm);
      }

      setTerms((prev) => [...prev]);

      return {
        action: "ADD",
        data: { id: newTerm.id },
        namespace: "TERM",
      };
    },
    [terms]
  );

  const removeTerm = useCallback(
    (id: EntityID): TermRemoveOperation | null => {
      const targetIndex = termIndexMap.get(id);
      if (targetIndex === undefined) return null;
      const target = terms.splice(targetIndex, 1)[0]!;

      setTerms([...terms]);

      return {
        action: "REMOVE",
        data: { index: targetIndex, value: target },
        namespace: "TERM",
      };
    },
    [terms, termIndexMap]
  );

  const setTermWord = useCallback(
    (id: EntityID, value: string): TermSetWordOperation | null => {
      const target = termMap.get(id);
      if (target === undefined) return null;
      const previousValue = target.word;
      target.word = value;
      setTerms([...terms]);

      return {
        action: "SET_WORD",
        data: { id: id, previous: previousValue },
        namespace: "TERM",
      };
    },
    [termMap, terms]
  );

  const setTermCategory = useCallback(
    (id: EntityID, value: LexicalCategory): TermSetCategoryOperation | null => {
      const target = termMap.get(id);
      if (target === undefined) return null;
      const previousValue = target.category;
      target.category = value;
      setTerms([...terms]);

      return {
        action: "SET_CATEGORY",
        data: { previous: previousValue, id: id },
        namespace: "TERM",
      };
    },
    [termMap, terms]
  );

  const setTermDefinition = useCallback(
    (id: EntityID, value: string): TermSetDefinitionOperation | null => {
      const target = termMap.get(id);
      if (target === undefined) return null;
      const previousValue = target.definition;
      target.definition = value;
      setTerms([...terms]);
      return {
        action: "SET_DEFINITION",
        data: { id: id, previous: previousValue },
        namespace: "TERM",
      };
    },
    [termMap, terms]
  );

  const setTermParent = useCallback(
    (id: EntityID, topicID: EntityID | null): TermSetParentOperation | null => {
      const term = termMap.get(id);
      if (!term) return null;

      let previousValue;

      if (topicID === null) {
        previousValue = term.parentTopic;
        term.parentTopic = null;
      } else {
        const topic = topicMap.get(topicID);
        if (!topic) return null;

        previousValue = term.parentTopic;
        term.parentTopic = topic.id;
      }

      setTerms([...terms]);

      return {
        action: "SET_PARENT",
        data: { id: id, previous: previousValue },
        namespace: "TERM",
      };
    },
    [topicMap, termMap, terms]
  );

  // endregion

  // region Goals

  const getGoal = useCallback(
    (id: EntityID): Goal | null => {
      return goalMap.get(id) || null;
    },
    [goalMap]
  );

  const addGoal = useCallback(
    (value?: Goal, index?: number): GoalAddOperation => {
      const newGoal = value || {
        id: getNonCollidingID(goals.map((entry) => entry.id)),
        parentTopic: null,
        type: null,
        description: "New Goal",
        priority: GoalPriority.OPTIONAL,
      };

      if (index === undefined) {
        goals.push(newGoal);
      } else {
        goals.splice(index, 0, newGoal);
      }
      setGoals([...goals]);

      return {
        action: "ADD",
        data: { id: newGoal.id },
        namespace: "GOAL",
      };
    },
    [goals]
  );

  const removeGoal = useCallback(
    (id: EntityID): GoalRemoveOperation | null => {
      const targetIndex = goalIndexMap.get(id);
      if (targetIndex === undefined || !goals.at(targetIndex)) return null;
      const targetGoal = goals.splice(targetIndex, 1)[0]!;
      setGoals([...goals]);
      return {
        action: "REMOVE",
        data: { index: targetIndex, value: targetGoal },
        namespace: "GOAL",
      };
    },
    [goals, goalIndexMap]
  );

  const setGoalType = useCallback(
    (id: EntityID, value: string | null): GoalSetTypeOperation | null => {
      const target = goalMap.get(id);
      if (!target) return null;
      const previousValue = target.type;
      target.type = value;
      setGoals([...goals]);
      return {
        namespace: "GOAL",
        action: "SET_TYPE",
        data: {
          id: id,
          previous: previousValue,
        },
      };
    },
    [goalMap, goals]
  );

  const setGoalDescription = useCallback(
    (id: EntityID, value: string): GoalSetDescriptionOperation | null => {
      const target = goalMap.get(id);
      if (!target) return null;
      const previousValue = target.description;
      target.description = value;
      setGoals([...goals]);
      return {
        action: "SET_DESCRIPTION",
        data: { id: id, previous: previousValue },
        namespace: "GOAL",
      };
    },
    [goalMap, goals]
  );

  const setGoalPriority = useCallback(
    (id: EntityID, value: GoalPriority): GoalSetPriorityOperation | null => {
      const target = goalMap.get(id);
      if (!target) return null;
      const previousValue = value;
      target.priority = value;
      setGoals([...goals]);
      return {
        action: "SET_PRIORITY",
        data: { id: id, previous: previousValue },
        namespace: "GOAL",
      };
    },
    [goalMap, goals]
  );

  const setGoalParent = useCallback(
    (id: EntityID, topicID: EntityID | null): GoalSetParentOperation | null => {
      let previousValue;
      const goal = goalMap.get(id);
      if (!goal) return null;

      if (topicID === null) {
        previousValue = goal.parentTopic;
        goal.parentTopic = null;
      } else {
        const topic = topicMap.get(topicID);
        if (!topic) return null;
        previousValue = goal.parentTopic;
        goal.parentTopic = topic.id;
      }

      setGoals([...goals]);
      return {
        action: "SET_PARENT",
        data: { id: id, previous: previousValue },
        namespace: "GOAL",
      };
    },
    [topicMap, goalMap, goals]
  );

  // endregion

  // region Topics

  const getTopic = useCallback(
    (id: EntityID): Topic | null => {
      return topicMap.get(id) || null;
    },
    [topicMap]
  );

  const getTopicDependants = useCallback(
    (id: EntityID): Topic[] => {
      const dependants: Topic[] = [];

      for (const topic of topics) {
        if (topic.id === id) continue;
        if (topic.dependencies.find((entry) => entry === id) !== undefined)
          dependants.push(topic);
      }

      return dependants;
    },
    [topics]
  );

  const getTopicGoals = useCallback(
    (id: EntityID): Goal[] => {
      return goals.filter((entry) => entry.parentTopic === id);
    },
    [goals]
  );

  const getTopicTerms = useCallback(
    (id: EntityID): Term[] => {
      return terms.filter((entry) => entry.parentTopic === id);
    },
    [terms]
  );

  const addTopic = useCallback(
    (value?: Topic, index?: number): TopicAddOperation | null => {
      const newTopic: Topic = value || {
        id: getNonCollidingID(topics.map((entry) => entry.id)),
        name: "New Topic",
        notes: "",
        essentialQuestions: "",
        resources: "",
        evidenceOfLearning: "",

        duration: 1,
        start: 0,
        dependencies: [],
      };

      if (index === undefined) {
        topics.push(newTopic);
      } else {
        topics.splice(index, 0, newTopic);
      }
      setTopics([...topics]);

      return {
        action: "ADD",
        data: { id: newTopic.id },
        namespace: "TOPIC",
      };
    },
    [topics]
  );

  const removeTopic = useCallback(
    (id: EntityID): TopicRemoveOperation | null => {
      const targetIndex = topicIndexMap.get(id);
      if (targetIndex === undefined || !topics.at(targetIndex)) return null;
      const targetTopic = topics.splice(targetIndex, 1)[0]!;
      setTopics([...topics]);
      return {
        action: "REMOVE",
        data: { index: targetIndex, value: targetTopic },
        namespace: "TOPIC",
      };
    },
    [topics, topicIndexMap]
  );

  const moveTopic = useCallback(
    (fromIndex: number, toIndex: number): TopicMoveOperation => {
      const moved = arrayMove(topics, fromIndex, toIndex);
      setTopics(moved);
      // setTopicSequence([...topicSequence]);
      return {
        action: "MOVE",
        data: { from: fromIndex, to: toIndex },
        namespace: "TOPIC",
      };
    },
    [topics]
  );

  const setTopicName = useCallback(
    (id: EntityID, value: string): TopicSetNameOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;
      const previousValue = target.name;
      target.name = value;
      setTopics([...topics]);
      return {
        action: "SET_NAME",
        data: { id: id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap]
  );

  const setTopicNotes = useCallback(
    (id: EntityID, value: string): TopicSetNotesOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;
      const previousValue = target.notes;
      target.notes = value;
      setTopics([...topics]);
      return {
        action: "SET_NOTES",
        data: { id: id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap]
  );

  const setTopicEssentialQuestions = useCallback(
    (
      id: EntityID,
      value: string
    ): TopicSetEssentialQuestionsOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;
      const previousValue = target.essentialQuestions;
      target.essentialQuestions = value;
      setTopics([...topics]);
      return {
        action: "SET_ESSENTIAL_QUESTIONS",
        data: { id: id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap]
  );

  const setTopicResources = useCallback(
    (id: EntityID, value: string): TopicSetResourcesOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;
      const previousValue = target.resources;
      target.resources = value;
      setTopics([...topics]);
      return {
        action: "SET_RESOURCES",
        data: { id: id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap]
  );

  const setTopicEvidenceOfLearning = useCallback(
    (
      id: EntityID,
      value: string
    ): TopicSetEvidenceOfLearningOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;
      const previousValue = target.evidenceOfLearning;
      target.evidenceOfLearning = value;
      setTopics([...topics]);
      return {
        action: "SET_EVIDENCE_OF_LEARNING",
        data: { id: id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap]
  );

  const updateTopicDependants = useCallback(
    (topic: Topic, updated: Set<EntityID>) => {
      const dependants = getTopicDependants(topic.id);
      const end = topic.start + topic.duration;
      for (const dependant of dependants) {
        if (updated.has(dependant.id)) continue;
        if (dependant.start < end) dependant.start = end;
        updated.add(dependant.id);
        updateTopicDependants(dependant, updated);
      }
    },
    [getTopicDependants]
  );

  const hasTopicDependant = useCallback(
    (topic: Topic, target: EntityID) => {
      const dependants = getTopicDependants(topic.id);
      for (const dependant of dependants) {
        if (dependant.id === target) return true;
        if (hasTopicDependant(dependant, target)) return true;
      }
      return false;
    },
    [getTopicDependants]
  );

  const setTopicStart = useCallback(
    (id: EntityID, value: number): TopicSetStartOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;

      let minimumStart: number = 0;
      for (const dependencyID of target.dependencies) {
        if (dependencyID === target.id) continue;
        const dependency = topicMap.get(dependencyID);
        if (!dependency) continue;
        if (minimumStart < dependency.start + dependency.duration)
          minimumStart = dependency.start + dependency.duration;
      }

      const previousValue = target.start;
      target.start = Math.max(minimumStart, value);

      updateTopicDependants(target, new Set([target.id]));
      setTopics([...topics]);

      return {
        action: "SET_START",
        data: { id: target.id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap, updateTopicDependants]
  );

  const setTopicDuration = useCallback(
    (id: EntityID, value: number): TopicSetDurationOperation | null => {
      const target = topicMap.get(id);
      if (!target) return null;

      const previousValue = target.duration;
      target.duration = Math.max(1, value);

      updateTopicDependants(target, new Set([target.id]));
      setTopics([...topics]);

      return {
        action: "SET_DURATION",
        data: { id: target.id, previous: previousValue },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap, updateTopicDependants]
  );

  const pushAlert = usePushAlert();
  const addTopicDependency = useCallback(
    (id: EntityID, value: EntityID): TopicAddDependencyOperation | null => {
      const target = topicMap.get(id);
      const reference = topicMap.get(value);
      if (!target || !reference) return null;
      if (hasTopicDependant(target, value)) {
        pushAlert(
          "Dependency loop detected, failed to add dependency",
          AlertType.WARNING
        );
        return null;
      }

      const dependenciesSet = new Set(target.dependencies);
      target.dependencies = Array.from(dependenciesSet.add(value));

      updateTopicDependants(reference, new Set([reference.id]));

      setTopics([...topics]);

      return {
        action: "ADD_DEPENDENCY",
        data: { id: target.id, value: value },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap, updateTopicDependants, hasTopicDependant, pushAlert]
  );

  const removeTopicDependency = useCallback(
    (id: EntityID, value: EntityID): TopicRemoveDependencyOperation | null => {
      const target = topicMap.get(id);

      if (!target) return null;
      const dependencies = new Set(target.dependencies);
      if (!dependencies.delete(value)) return null;
      target.dependencies = Array.from(dependencies);

      setTopics([...topics]);

      return {
        action: "REMOVE_DEPENDENCY",
        data: { id: target.id, value: value },
        namespace: "TOPIC",
      };
    },
    [topics, topicMap]
  );

  // endregion

  // region Activities

  const getActivity = useCallback(
    (id: EntityID): Activity | null => {
      return activityMap.get(id) ?? null;
    },
    [activityMap]
  );

  const addActivity = useCallback(
    (activity?: Activity, index?: number): ActivityAddOperation | null => {
      const id = getNonCollidingID(activities.map((entry) => entry.id));

      const newActivity: Activity = activity ?? {
        id: id,
        parentTopic: null,
        title: "New Activity",
        type: ActivityType.CREATION,
        description: "",
        duration: "30 min",
        resources: [],
        deliverables: [],
      };

      if (index === undefined) {
        activities.push(newActivity);
      } else {
        activities.splice(index, 0, newActivity);
      }

      setActivities([...activities]);

      return {
        action: "ADD",
        data: { id: newActivity.id },
        namespace: "ACTIVITY",
      };
    },
    [activities]
  );

  const removeActivity = useCallback(
    (id: EntityID): ActivityRemoveOperation | null => {
      const index = activityIndexMap.get(id);
      const activity = activityMap.get(id);

      if (index === undefined || !activity) return null;

      activities.splice(index, 1);
      setActivities([...activities]);

      return {
        action: "REMOVE",
        data: { index: index, value: activity },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityIndexMap, activityMap]
  );

  const setActivityTitle = useCallback(
    (id: EntityID, value: string): ActivitySetTitleOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = activity.title;
      activity.title = value;
      setActivities([...activities]);

      return {
        action: "SET_TITLE",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const setActivityType = useCallback(
    (id: EntityID, value: ActivityType): ActivitySetTypeOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = activity.type;
      activity.type = value;
      setActivities([...activities]);

      return {
        action: "SET_TYPE",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const setActivityDescription = useCallback(
    (id: EntityID, value: string): ActivitySetDescriptionOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = activity.description;
      activity.description = value;
      setActivities([...activities]);

      return {
        action: "SET_DESCRIPTION",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const setActivityDuration = useCallback(
    (id: EntityID, value: string): ActivitySetDurationOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = activity.duration;
      activity.duration = value;
      setActivities([...activities]);

      return {
        action: "SET_DURATION",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const setActivityResources = useCallback(
    (id: EntityID, value: string[]): ActivitySetResourcesOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = [...activity.resources];
      activity.resources = [...value];
      setActivities([...activities]);

      return {
        action: "SET_RESOURCES",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const setActivityDeliverables = useCallback(
    (
      id: EntityID,
      value: string[]
    ): ActivitySetDeliverablesOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = [...activity.deliverables];
      activity.deliverables = [...value];
      setActivities([...activities]);

      return {
        action: "SET_DELIVERABLES",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const setActivityParent = useCallback(
    (
      id: EntityID,
      parentTopic: EntityID | null
    ): ActivitySetParentOperation | null => {
      const activity = activityMap.get(id);
      if (!activity) return null;

      const previous = activity.parentTopic;
      activity.parentTopic = parentTopic;
      setActivities([...activities]);

      return {
        action: "SET_PARENT",
        data: { id: id, previous: previous },
        namespace: "ACTIVITY",
      };
    },
    [activities, activityMap]
  );

  const getTopicActivities = useCallback(
    (topicID: EntityID) => {
      return activities.filter((activity) => activity.parentTopic === topicID);
    },
    [activities]
  );

  // endregion

  // region Miscellaneous

  const serialize = useCallback((): SerializedDocument => {
    return {
      manifest: "ver2",
      title: title,
      abstract: abstract,
      keywords: keywords,
      gradeLevel: gradeLevel,

      goals: goals,
      terms: terms,
      topics: topics,
      activities: activities,
    } as unknown as SerializedDocument;
  }, [title, abstract, keywords, gradeLevel, goals, terms, topics, activities]);

  const { id } = useParams();
  const [saveState, setSaveState] = useState<SaveState>(SaveState.NOT_SAVED);
  useEffect(() => {
    setSaveState(SaveState.NOT_SAVED);
  }, [title, abstract, keywords, gradeLevel, goals, terms, topics, activities]);

  useEffect(() => {
    setSaveState(SaveState.SAVED);
  }, []);

  const POST = usePOST();
  const topRealm = useTopRealm();
  const save = useCallback(async () => {
    setSaveState(SaveState.SAVING);
    const { response, error } = await POST(
      `/v1/assets/${topRealm.externalID}/~/scopeAndSequence/${id}.json`,
      {
        assertedRealmEidUrn: topRealm.eidURN,
        body: JSON.stringify(serialize()),
        headers: {
          ["Content-Type"]: "application/octet-stream",
        },
      }
    );
    setSaveState(SaveState.NOT_SAVED);

    if (error) return pushError(error.clientErrorDetail.userMessage);
    if (!response.ok) return pushError(await response.text());

    setSaveState(SaveState.SAVED);
  }, [POST, id, pushError, topRealm, serialize]);

  const undoGoalOperation = useCallback(
    (operation: GoalOperation): Operation | null => {
      switch (operation.action) {
        case "SET_PARENT":
          return setGoalParent(operation.data.id, operation.data.previous);

        case "ADD":
          return removeGoal(operation.data.id);

        case "REMOVE":
          return addGoal(operation.data.value, operation.data.index);

        case "SET_TYPE":
          return setGoalType(operation.data.id, operation.data.previous);

        case "SET_DESCRIPTION":
          return setGoalDescription(operation.data.id, operation.data.previous);

        case "SET_PRIORITY":
          return setGoalPriority(operation.data.id, operation.data.previous);
      }
    },
    [
      removeGoal,
      addGoal,
      setGoalType,
      setGoalDescription,
      setGoalPriority,
      setGoalParent,
    ]
  );

  const undoOutlineOperation = useCallback(
    (operation: OutlineOperation): Operation | null => {
      switch (operation.action) {
        case "SET_TITLE":
          return setOutlineTitle(operation.data.previous);

        case "SET_ABSTRACT":
          return setOutlineAbstract(operation.data.previous);

        case "SET_GRADE_LEVEL":
          return setOutlineGradeLevel(operation.data.previous);

        case "ADD_KEYWORD":
          return removeOutlineKeyword(operation.data.index);

        case "REMOVE_KEYWORD":
          return addOutlineKeyword(operation.data.value, operation.data.index);
      }
    },
    [
      setOutlineTitle,
      setOutlineAbstract,
      setOutlineGradeLevel,
      removeOutlineKeyword,
      addOutlineKeyword,
    ]
  );

  const undoTermOperation = useCallback(
    (operation: TermOperation): Operation | null => {
      switch (operation.action) {
        case "SET_PARENT":
          return setTermParent(operation.data.id, operation.data.previous);

        case "ADD":
          return removeTerm(operation.data.id);

        case "REMOVE":
          return addTerm(operation.data.value, operation.data.index);

        case "SET_WORD":
          return setTermWord(operation.data.id, operation.data.previous);

        case "SET_CATEGORY":
          return setTermCategory(operation.data.id, operation.data.previous);

        case "SET_DEFINITION":
          return setTermDefinition(operation.data.id, operation.data.previous);
      }
    },
    [
      removeTerm,
      addTerm,
      setTermWord,
      setTermCategory,
      setTermDefinition,
      setTermParent,
    ]
  );

  const undoTopicOperation = useCallback(
    (operation: TopicOperation): Operation | null => {
      switch (operation.action) {
        case "SET_DURATION":
          return setTopicDuration(operation.data.id, operation.data.previous);
        case "SET_START":
          return setTopicStart(operation.data.id, operation.data.previous);
        case "ADD_DEPENDENCY":
          return removeTopicDependency(operation.data.id, operation.data.value);
        case "REMOVE_DEPENDENCY":
          return addTopicDependency(operation.data.id, operation.data.value);

        case "ADD":
          return removeTopic(operation.data.id);

        case "REMOVE":
          return addTopic(operation.data.value, operation.data.index);

        case "MOVE":
          return moveTopic(operation.data.to, operation.data.from);

        case "SET_NAME":
          return setTopicName(operation.data.id, operation.data.previous);

        case "SET_NOTES":
          return setTopicNotes(operation.data.id, operation.data.previous);

        case "SET_ESSENTIAL_QUESTIONS":
          return setTopicEssentialQuestions(
            operation.data.id,
            operation.data.previous
          );

        case "SET_RESOURCES":
          return setTopicResources(operation.data.id, operation.data.previous);

        case "SET_EVIDENCE_OF_LEARNING":
          return setTopicEvidenceOfLearning(
            operation.data.id,
            operation.data.previous
          );
      }
    },
    [
      removeTopic,
      addTopic,
      moveTopic,
      setTopicName,
      setTopicNotes,
      setTopicEssentialQuestions,
      setTopicResources,
      setTopicEvidenceOfLearning,
      addTopicDependency,
      removeTopicDependency,
      setTopicStart,
      setTopicDuration,
    ]
  );

  const undoActivityOperation = useCallback(
    (operation: ActivityOperation): Operation | null => {
      switch (operation.action) {
        case "SET_PARENT":
          return setActivityParent(operation.data.id, operation.data.previous);

        case "ADD":
          return removeActivity(operation.data.id);

        case "REMOVE":
          return addActivity(operation.data.value, operation.data.index);

        case "SET_TITLE":
          return setActivityTitle(operation.data.id, operation.data.previous);

        case "SET_TYPE":
          return setActivityType(operation.data.id, operation.data.previous);

        case "SET_DESCRIPTION":
          return setActivityDescription(
            operation.data.id,
            operation.data.previous
          );

        case "SET_DURATION":
          return setActivityDuration(
            operation.data.id,
            operation.data.previous
          );

        case "SET_RESOURCES":
          return setActivityResources(
            operation.data.id,
            operation.data.previous
          );

        case "SET_DELIVERABLES":
          return setActivityDeliverables(
            operation.data.id,
            operation.data.previous
          );
      }
    },
    [
      removeActivity,
      addActivity,
      setActivityTitle,
      setActivityType,
      setActivityDescription,
      setActivityDuration,
      setActivityResources,
      setActivityDeliverables,
      setActivityParent,
    ]
  );

  const undoOperation = useCallback(
    (operation: Operation): Operation | null => {
      switch (operation.namespace) {
        case "GOAL":
          return undoGoalOperation(operation as GoalOperation);
        case "OUTLINE":
          return undoOutlineOperation(operation as OutlineOperation);
        case "TERM":
          return undoTermOperation(operation as TermOperation);
        case "TOPIC":
          return undoTopicOperation(operation as TopicOperation);
        case "ACTIVITY":
          return undoActivityOperation(operation as ActivityOperation);
        default:
          pushError("unknown operation namespace");
          return null;
      }
    },
    [
      undoGoalOperation,
      undoOutlineOperation,
      undoTermOperation,
      undoTopicOperation,
      undoActivityOperation,
      pushError,
    ]
  );

  const pushOperation = useCallback(
    (operation: Operation) => {
      setRedoStack([]);

      undoStack.push(operation);
      if (undoStack.length > _operationLimit)
        undoStack.splice(0, undoStack.length - _operationLimit);

      setUndoStack([...undoStack]);
    },
    [undoStack]
  );

  const undo = useCallback(() => {
    if (undoStack.length <= 0) return;
    const operation = undoStack.pop()!;
    const antiOperation = undoOperation(operation);
    if (antiOperation) {
      redoStack.push(antiOperation);
      setRedoStack([...redoStack]);
    }
    setUndoStack([...undoStack]);
  }, [undoStack, redoStack, undoOperation]);

  const redo = useCallback(() => {
    if (redoStack.length <= 0) return;
    const operation = redoStack.pop()!;
    const antiOperation = undoOperation(operation);
    if (antiOperation) {
      undoStack.push(antiOperation);
      setUndoStack([...undoStack]);
    }
    setRedoStack([...redoStack]);
  }, [undoStack, redoStack, undoOperation]);

  // endregion

  return (
    <Context.Provider
      value={{
        serialize: serialize,

        pushOperation: pushOperation,
        _operationLimit: _operationLimit,
        undo: undo,
        redo: redo,

        cloud: {
          state: saveState,
          save: save,
        },

        outline: {
          title: title,
          keywords: keywords,
          abstract: abstract,
          gradeLevel: gradeLevel,

          setTitle: setOutlineTitle,
          setAbstract: setOutlineAbstract,
          setGradeLevel: setOutlineGradeLevel,
          addKeyword: addOutlineKeyword,
          removeKeyword: removeOutlineKeyword,
        },

        term: {
          entries: terms,
          get: getTerm,

          add: addTerm,
          remove: removeTerm,
          setDefinition: setTermDefinition,
          setWord: setTermWord,
          setCategory: setTermCategory,
          setParentTopic: setTermParent,
        },

        goal: {
          entries: goals,
          get: getGoal,

          add: addGoal,
          remove: removeGoal,
          setType: setGoalType,
          setPriority: setGoalPriority,
          setDescription: setGoalDescription,
          setParentTopic: setGoalParent,
        },

        activity: {
          entries: activities,
          get: getActivity,

          add: addActivity,
          remove: removeActivity,
          setTitle: setActivityTitle,
          setType: setActivityType,
          setDescription: setActivityDescription,
          setDuration: setActivityDuration,
          setResources: setActivityResources,
          setDeliverables: setActivityDeliverables,
          setParentTopic: setActivityParent,
        },

        topic: {
          entries: topics,
          get: getTopic,
          getGoals: getTopicGoals,
          getTerms: getTopicTerms,
          getDependants: getTopicDependants,
          getActivities: getTopicActivities,

          add: addTopic,
          remove: removeTopic,
          move: moveTopic,

          setName: setTopicName,
          setNotes: setTopicNotes,
          setEssentialQuestions: setTopicEssentialQuestions,
          setResources: setTopicResources,
          setEvidenceOfLearning: setTopicEvidenceOfLearning,

          setDuration: setTopicDuration,
          setStart: setTopicStart,
          addDependency: addTopicDependency,
          removeDependency: removeTopicDependency,
        },
      }}
    >
      {children}
    </Context.Provider>
  );
}
