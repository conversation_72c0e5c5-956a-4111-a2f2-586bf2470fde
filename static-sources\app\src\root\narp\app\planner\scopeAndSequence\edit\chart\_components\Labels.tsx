import { useChartContext } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";
import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { useMemo } from "react";

export default function Labels() {
  const {
    topic: { entries },
  } = useEditorContext();
  const { scale, timestampToPixels } = useChartContext();

  const fullDuration = useMemo(() => {
    let value = 0;
    for (const entry of entries) {
      if (entry.start + entry.duration > value)
        value = entry.start + entry.duration;
    }
    return value;
  }, [entries]);

  const stepOffset = 1 / scale;

  let label: string;
  if (stepOffset === 1) {
    label = "Day";
  } else if (stepOffset === 1 * 7) {
    label = "Week";
  } else if (stepOffset === 1 * 7 * 4) {
    label = "Month";
  }

  return (
    <div className="-z-20 size-full flex flex-row border-s-2 border-s-neutral-200">
      {stepOffset > 0 &&
        new Array(Math.floor(fullDuration / stepOffset) + 1)
          .fill(null)
          .map((_, index) => (
            <div
              key={index}
              style={{ width: timestampToPixels(stepOffset) + "px" }}
              className="relative border-e-2 border-e-neutral-200 shrink-0 p-1"
            >
              {label} {index + 1}
            </div>
          ))}
    </div>
  );
}
