#!/bin/bash
######################################################################################################
# © 2024 ĀYŌDÈ: Organization for the Advancement of Youth through Opportunities in Digital Education.
# All rights reserved.
#
# This software is confidential and proprietary information of ĀYŌDÈ.
# It is intended solely for internal use by authorized personnel within ĀYŌDÈ.
# Any unauthorized use, distribution, or reproduction of this software is strictly prohibited.
#
# https://ayode.org
#
######################################################################################################

####################################################################################################
# Source common header
####################################################################################################

# Ensure file is NOT being SOURCED
if [[ "${BASH_SOURCE[0]}" != "$0" ]]
then
    echo "Do not source this file; execute instead."
    return 1
fi

# Ensure COMMON_BASH_HEADER_CODE_PATHNAME is set
if [ -z "${COMMON_BASH_HEADER_CODE_PATHNAME-}" ]
then
    echo "common_bash_header_code_pathname is not set. source 'prepare-environment.sh' in root directory."
    exit 1
fi

# Load common bash header
source "$COMMON_BASH_HEADER_CODE_PATHNAME"

####################################################################################################
# Build
####################################################################################################

# Increase allocated memory to prevent wasm out of memory error
print "increasing allocated memory (wasm memory error prevention)\n"
ulimit -n 8192
ulimit -u 256
ulimit -v 67108864
ulimit -t 1200

# Remove node_modules and package-lock.json
print "removing existing packages\n"
rm -fr node_modules package-lock.json

# Install all necessary packages
print "installing component packages and dependencies\n"
npm install

# Compile React components to static files
print "compiling components\n"

# compile typescript and build to static/
npm run build

# Finalization
print "component build step complete\n"