import { useState, useRef, useEffect } from "react";
import {
  ChevronDown,
  Check,
  User,
  Users,
  Building,
  MapPin,
  Flag,
  Globe,
  Loader2,
} from "lucide-react";
import { useLORContext, LORLevel } from "./LORContext";
import LORInfoButton from "./LORInfoButton";

interface LORSelectorProps {
  className?: string;
}

export default function LORSelector({ className = "" }: LORSelectorProps) {
  const { currentLORLevel, availableLORLevels, isSwitching, switchLORLevel } =
    useLORContext();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getLORIcon = (type: LORLevel["type"]) => {
    switch (type) {
      case "personal":
        return <User className="w-4 h-4" />;
      case "classroom":
        return <Users className="w-4 h-4" />;
      case "school":
        return <Building className="w-4 h-4" />;
      case "district":
        return <MapPin className="w-4 h-4" />;
      case "state":
        return <Flag className="w-4 h-4" />;
      case "national":
        return <Globe className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getLevelColor = (type: LORLevel["type"]) => {
    switch (type) {
      case "personal":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "classroom":
        return "text-green-600 bg-green-50 border-green-200";
      case "school":
        return "text-purple-600 bg-purple-50 border-purple-200";
      case "district":
        return "text-orange-600 bg-orange-50 border-orange-200";
      case "state":
        return "text-red-600 bg-red-50 border-red-200";
      case "national":
        return "text-indigo-600 bg-indigo-50 border-indigo-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getLevelBorderColor = (type: LORLevel["type"]) => {
    switch (type) {
      case "personal":
        return "border-blue-200";
      case "classroom":
        return "border-green-200";
      case "school":
        return "border-purple-200";
      case "district":
        return "border-orange-200";
      case "state":
        return "border-red-200";
      case "national":
        return "border-indigo-200";
      default:
        return "border-gray-200";
    }
  };

  const getLevelTextColor = (type: LORLevel["type"]) => {
    switch (type) {
      case "personal":
        return "text-blue-600";
      case "classroom":
        return "text-green-600";
      case "school":
        return "text-purple-600";
      case "district":
        return "text-orange-600";
      case "state":
        return "text-red-600";
      case "national":
        return "text-indigo-600";
      default:
        return "text-gray-600";
    }
  };

  const handleLevelSelect = async (level: LORLevel) => {
    setIsOpen(false);
    await switchLORLevel(level);
  };

  if (!currentLORLevel) {
    return (
      <div
        className={`flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-md ${className}`}
      >
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="text-sm text-gray-600">Loading LOR levels...</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="relative" ref={dropdownRef}>
        {/* Current LOR Button */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          disabled={isSwitching}
          className={`flex items-center gap-2 px-3 py-2 bg-white border rounded-md hover:bg-gray-50 transition-all focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
            isSwitching
              ? "opacity-70 cursor-not-allowed bg-gray-50 border-gray-300"
              : `cursor-pointer ${getLevelBorderColor(currentLORLevel.type)}`
          }`}
        >
          <div className="flex items-center gap-2">
            <span
              className={`text-sm transition-colors ${isSwitching ? "text-gray-400" : "text-gray-700"}`}
            >
              Current LOR:
            </span>
            <div className="flex items-center gap-1">
              {isSwitching ? (
                <div className="relative">
                  <Loader2 className="w-4 h-4 animate-spin text-primary-600" />
                  <div className="absolute inset-0 w-4 h-4 bg-gray-200 rounded-full opacity-20"></div>
                </div>
              ) : (
                <div
                  className={`transition-all ${getLevelTextColor(currentLORLevel.type)}`}
                >
                  {getLORIcon(currentLORLevel.type)}
                </div>
              )}
              <span
                className={`text-sm font-medium transition-colors ${
                  isSwitching
                    ? "text-gray-500"
                    : getLevelTextColor(currentLORLevel.type)
                }`}
              >
                {currentLORLevel.name}
              </span>
            </div>
          </div>
          <ChevronDown
            className={`w-4 h-4 transition-all ${isOpen ? "rotate-180" : ""} ${
              isSwitching ? "text-gray-300" : "text-gray-400"
            }`}
          />
        </button>

        {/* Dropdown Modal */}
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40 bg-black bg-opacity-10"
              onClick={() => setIsOpen(false)}
            />

            {/* Dropdown Content */}
            <div className="absolute top-full left-0 mt-2 w-96 bg-white rounded-lg border border-gray-200 shadow-lg z-50">
              {/* Header */}
              <div className="px-4 py-3 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Switch LOR Level
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Choose your working context and access level (6 available)
                </p>
              </div>

              {/* LOR Levels List */}
              <div className="max-h-96 overflow-y-auto">
                {availableLORLevels.map((level) => {
                  const isSelected = currentLORLevel.id === level.id;
                  const isDisabled = isSwitching;

                  return (
                    <button
                      key={level.id}
                      onClick={() => handleLevelSelect(level)}
                      disabled={isDisabled}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 disabled:opacity-50 disabled:cursor-not-allowed ${
                        isSelected ? "bg-blue-50" : ""
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {/* Icon with level color */}
                          <div
                            className={`p-2 rounded-lg border ${getLevelColor(level.type)}`}
                          >
                            {getLORIcon(level.type)}
                          </div>

                          {/* Level Info */}
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-gray-900">
                                {level.name}
                              </h4>
                              {isSelected && (
                                <Check className="w-4 h-4 text-blue-600" />
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {level.description}
                            </p>

                            {/* Level Badge */}
                            <div className="mt-2">
                              <span
                                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(level.type)}`}
                              >
                                {level.type.charAt(0).toUpperCase() +
                                  level.type.slice(1)}{" "}
                                level
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>

              {/* Footer Info */}
              <div className="px-4 py-3 bg-yellow-50 border-t border-yellow-200">
                <div className="flex items-start gap-2">
                  <div className="w-4 h-4 bg-yellow-400 rounded-full flex-shrink-0 mt-0.5">
                    <span className="block w-2 h-2 bg-white rounded-full mx-auto mt-1"></span>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-yellow-800">
                      What does LOR level mean?
                    </p>
                    <p className="text-yellow-700 mt-1">
                      Each LOR level provides different access permissions and
                      collaboration features.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Info Button */}
      <LORInfoButton currentLevel={currentLORLevel} />
    </div>
  );
}
