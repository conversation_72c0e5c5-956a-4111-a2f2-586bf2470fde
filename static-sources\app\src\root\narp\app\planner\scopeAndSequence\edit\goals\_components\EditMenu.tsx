import {
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { Dispatch, SetStateAction, useEffect, useRef } from "react";
import { FaXmark } from "react-icons/fa6";
import TypeField from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/TypeField.tsx";
import PriorityField from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/PriorityField.tsx";
import DescriptionField from "@/root/narp/app/planner/scopeAndSequence/edit/goals/_components/DescriptionField.tsx";

export default function EditMenu({
  editID,
  setEditID,
}: {
  editID: EntityID | null;
  setEditID: Dispatch<SetStateAction<EntityID | null>>;
}) {
  const {
    goal: { get },
  } = useEditorContext();

  const dialogRef = useRef<HTMLDialogElement>(null);
  const goal = editID !== null ? get(editID) : null;

  useEffect(() => {
    if (goal) {
      dialogRef.current?.showModal();
    } else {
      dialogRef.current?.close();
    }
  }, [goal]);

  function closeDialog() {
    dialogRef.current?.close();
  }

  function handleClose() {
    setEditID(null);
  }

  if (!goal) return <></>;
  return (
    <dialog onClose={handleClose} ref={dialogRef}>
      <button
        tabIndex={0}
        type="button"
        onClick={closeDialog}
        className="-z-10 fixed w-screen h-screen"
      />

      <div
        className="fixed w-full max-w-xl top-1/2 left-1/2 -translate-1/2
         p-5"
      >
        <div className="bg-white border border-neutral-300 rounded-lg p-5 flex flex-col gap-3">
          <div className="flex flex-row">
            <div className="grow basis-0">
              <div className="tooltip" data-tip="Close">
                <button
                  tabIndex={0}
                  type="button"
                  className="btn btn-sm btn-square btn-error btn-outline"
                  data-tip="what"
                  onClick={closeDialog}
                >
                  <FaXmark />
                </button>
              </div>
            </div>
            <div className="basis-0">
              <div className="text-xl font-bold text-nowrap">Edit Goal</div>
            </div>
            <div className="grow basis-0"></div>
          </div>

          <TypeField goal={goal} />
          <PriorityField goal={goal} />
          <DescriptionField goal={goal} />
        </div>
      </div>
    </dialog>
  );
}
