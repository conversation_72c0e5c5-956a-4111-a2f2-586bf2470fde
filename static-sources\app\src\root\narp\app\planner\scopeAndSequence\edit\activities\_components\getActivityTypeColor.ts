import { ActivityType } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";

export function getActivityTypeColor(type: ActivityType): string {
  switch (type) {
    case ActivityType.CREATION:
      return "#3b82f6"; // blue-500
    case ActivityType.ANIMATION:
      return "#8b5cf6"; // violet-500
    case ActivityType.RESEARCH:
      return "#10b981"; // emerald-500
    case ActivityType.PRESENTATION:
      return "#f59e0b"; // amber-500
    case ActivityType.ASSESSMENT:
      return "#ef4444"; // red-500
    case ActivityType.COLLABORATION:
      return "#06b6d4"; // cyan-500
    default:
      return "#6b7280"; // gray-500
  }
}