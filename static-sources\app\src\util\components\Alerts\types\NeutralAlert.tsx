import { useAlerts } from "@/util/components/Alerts/Context.tsx";
import { FaXmark } from "react-icons/fa6";
import { MouseEvent } from "react";
import { IconContext } from "react-icons";

export default function NeutralAlert() {
  const { activeAlert, popAlert } = useAlerts();

  function handleDismiss(e: MouseEvent) {
    e.preventDefault();
    popAlert();
  }

  if (!activeAlert) return <></>;
  return (
    <div
      role={"alert"}
      className={`text-neutral-050 rounded-xl p-4 w-full flex flex-row bg-neutral-900`}
    >
      <div className="col-span-3 p-2">
        <h1 className="font-black text-2xl">
          {activeAlert.message ? activeAlert.message : ""}
        </h1>{" "}
        {/* Title ^ */}
        <p className="overflow-y-scroll font-light min-w-auto max-h-13">
          {activeAlert.description ? activeAlert.description : ""}
        </p>{" "}
      </div>
      {/* Description ^ */}
      <div className="ml-auto font-mono flex flex-row space-x-10 col-span-2">
        <div className="p-2">
          {activeAlert.callToActionLink ? (
            <a
              className="ml-auto"
              href={activeAlert.callToActionLink}
              target="_blank"
            >
              {activeAlert.callToActionText
                ? activeAlert.callToActionText
                : activeAlert.callToActionLink}
            </a>
          ) : (
            ""
          )}
        </div>
        <button
          type={"button"}
          onClick={handleDismiss}
          className={"btn btn-ghost btn-square ml-auto mb-auto"}
        >
          <IconContext.Provider
            value={{
              className: "shared-class",
              size: "40",
              attr: { strokeWidth: "1" },
            }}
          >
            <FaXmark />
          </IconContext.Provider>
        </button>
      </div>
    </div>
  );
}
