import { useEditorContext } from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import {
  FocusEvent,
  FormEvent,
  KeyboardEvent,
  MouseEvent,
  useEffect,
  useRef,
  useState,
} from "react";
import { FaCheck, FaPenToSquare } from "react-icons/fa6";

export default function AbstractInput() {
  const {
    outline: { abstract, setAbstract },
    cloud: { save },
  } = useEditorContext();

  const [displayValue, setDisplayValue] = useState<string>(abstract);
  const [checkedTimeout, setCheckedTimeout] = useState<NodeJS.Timeout | null>(
    null
  );

  const inputRef = useRef<HTMLTextAreaElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  function updateSize() {
    if (!inputRef.current) return;
    inputRef.current.style.height = 0 + "px";
    inputRef.current.style.overflow = "hidden";
    inputRef.current.style.height = inputRef.current.scrollHeight + "px";
  }

  useEffect(() => {
    updateSize();
  }, [displayValue]);

  function handleInput(e: FormEvent<HTMLTextAreaElement>) {
    e.preventDefault();
    setDisplayValue(e.currentTarget.value);
  }

  function handleKeyDown(e: KeyboardEvent<HTMLTextAreaElement>) {
    // Ctrl/Cmd + Enter to save (since Enter is needed for line breaks)
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      confirmEdit();
    }
  }

  function handleBlur(e: FocusEvent) {
    if (
      e.relatedTarget == buttonRef.current ||
      e.relatedTarget === inputRef.current
    )
      return;
    // Don't revert - let user keep their changes
  }

  function confirmEdit() {
    if (abstract === displayValue) return;
    setAbstract(displayValue);

    if (checkedTimeout !== null) clearTimeout(checkedTimeout);
    setCheckedTimeout(
      setTimeout(() => {
        setCheckedTimeout(null);
        // Auto-save after confirm
        save();
      }, 1000)
    );
  }

  function handleClick(e: MouseEvent) {
    e.preventDefault();
    confirmEdit();
  }

  return (
    <label className="flex flex-col gap-1">
      <span className="font-bold">Abstract</span>
      <label className="flex flex-row gap-3 w-full max-w-screen-md">
        <textarea
          tabIndex={0}
          ref={inputRef}
          onBlur={handleBlur}
          value={displayValue}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          placeholder="Enter abstract (Ctrl+Enter to save)"
          className="textarea textarea-lg grow overflow-hidden resize-none text-wrap"
        />
        <div className="tooltip" data-tip="Confirm Edit (Ctrl+Enter)">
          <button
            tabIndex={0}
            onClick={handleClick}
            onBlur={handleBlur}
            ref={buttonRef}
            type="submit"
            disabled={abstract === displayValue}
            className={`btn btn-square btn-lg btn-primary ${abstract === displayValue ? "btn-disabled" : ""}`}
          >
            {checkedTimeout === null ? <FaPenToSquare /> : <FaCheck />}
          </button>
        </div>
      </label>
    </label>
  );
}
