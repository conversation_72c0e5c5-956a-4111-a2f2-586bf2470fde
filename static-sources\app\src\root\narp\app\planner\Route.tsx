import { RouteObject } from "react-router-dom";
import PageSkeleton from "@/util/components/PageSkeleton.tsx";
import { default as IndexRoute } from "./_index/Route.tsx";
import { default as ScopeAndSequenceRoute } from "./scopeAndSequence/Route.tsx";

const Route: RouteObject = {
  path: "planner",
  hydrateFallbackElement: <PageSkeleton />,
  children: [IndexRoute, ScopeAndSequenceRoute],
};
export default Route;
