import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import MazeAnimation from "@/assets/animations/codermerlin-maze.lottie.json";
import { memo } from "react";
import { FaXmark } from "react-icons/fa6";

const GeneratingActivitiesOverlay = memo(function ({ 
  isVisible, 
  onCancel 
}: { 
  isVisible: boolean;
  onCancel?: () => void;
}) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/20 backdrop-blur-sm" />
      
      <div className="relative z-10 flex flex-col items-center justify-center p-8">
        <div className="bg-white rounded-lg shadow-xl p-8 flex flex-col items-center relative">
          {onCancel && (
            <button
              onClick={onCancel}
              className="absolute top-4 right-4 btn btn-sm btn-circle btn-ghost text-gray-400 hover:text-gray-600"
              aria-label="Cancel generation"
            >
              <FaXmark className="w-4 h-4" />
            </button>
          )}
          <DotLottieReact
            className="w-64 h-64"
            loop={true}
            speed={1}
            data={MazeAnimation}
            autoplay={true}
            renderConfig={{ autoResize: true }}
          />
          <div className="mt-4 text-lg font-semibold text-gray-700">
            Generating Activities...
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Please wait while we create activities for your topic
          </div>
          {onCancel && (
            <button
              onClick={onCancel}
              className="mt-4 btn btn-outline btn-error btn-sm"
            >
              Cancel Generation
            </button>
          )}
        </div>
      </div>
    </div>
  );
});

export default GeneratingActivitiesOverlay;
