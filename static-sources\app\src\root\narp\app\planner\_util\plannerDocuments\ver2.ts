import { ScopeAndSequenceDocument as Version0 } from "./version0";
import { GoalType, ScopeAndSequenceDocument as Version1 } from "./version1";
import {
  Goal,
  GoalPriority,
  LexicalCategory,
  Term,
  Topic,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import { GradeLevel } from "@/util/standards.ts";

export interface SerializedDocument {
  manifest: "ver2";

  title: string;
  abstract: string;
  gradeLevel: GradeLevel;
  keywords: string[];

  topics: {
    id: number;
    name: string;

    notes: string;
    essentialQuestions: string;
    resources: string;
    evidenceOfLearning: string;

    duration: number;
    start: number;
    dependencies: number[];
  }[];

  goals: {
    id: number;
    parentTopic: number | null;
    type: string | null;
    description: string;
    priority: GoalPriority;
  }[];

  terms: {
    id: number;
    parentTopic: number | null;
    word: string;
    definition: string;
    category: string;
  }[];

  activities: {
    id: number;
    parentTopic: number | null;
    title: string;
    type: string;
    description: string;
    duration: string;
    resources: string[];
    deliverables: string[];
  }[];
}

export function fromVer0(data: Version0): SerializedDocument {
  let topicIdCounter = 0;
  let goalIdCounter = 0;

  const topics: Topic[] = [];
  const goals: Goal[] = [];
  const terms: Term[] = [];

  for (const topic of data.topics) {
    for (const goal of topic.goals) {
      goals.push({
        id: goalIdCounter,
        parentTopic: topicIdCounter,
        type:
          goal.type === GoalType.KnowledgeGoal
            ? "Knowledge Goal"
            : "Skill Goal",
        description: goal.description,
        priority:
          goal.priority === 0
            ? GoalPriority.OPTIONAL
            : goal.priority === 1
              ? GoalPriority.NICE_TO_HAVE
              : GoalPriority.MUST_HAVE,
      });
      goalIdCounter += 1;
    }

    topics.push({
      dependencies: topicIdCounter > 0 ? [topicIdCounter - 1] : [],
      duration: 7,
      essentialQuestions: "",
      evidenceOfLearning: "",
      id: topicIdCounter,
      name: topic.name,
      notes: "",
      resources: "",
      start: topicIdCounter * 7,
    });
    topicIdCounter += 1;
  }

  return {
    manifest: "ver2",
    abstract: data.abstract,
    title: data.title,
    gradeLevel: data.grade as unknown as GradeLevel,
    keywords: data.keywords,

    goals,
    terms,
    topics,
    activities: [],
  };
}

export function fromVer1(data: Version1): SerializedDocument {
  let topicIdCounter = 0;
  let goalIdCounter = 0;
  let termIdCounter = 0;

  const topics: Topic[] = [];
  const goals: Goal[] = [];
  const terms: Term[] = [];

  for (const topic of data.topics) {
    for (const goal of topic.goals) {
      goals.push({
        id: goalIdCounter,
        parentTopic: topicIdCounter,
        type:
          goal.type === GoalType.KnowledgeGoal
            ? "Knowledge Goal"
            : "Skill Goal",
        description: goal.description,
        priority:
          goal.priority === 0
            ? GoalPriority.OPTIONAL
            : goal.priority === 1
              ? GoalPriority.NICE_TO_HAVE
              : GoalPriority.MUST_HAVE,
      });
      goalIdCounter += 1;
    }

    for (const term of topic.terms) {
      terms.push({
        id: termIdCounter,
        parentTopic: topicIdCounter,
        word: term.word,
        category: term.lexicalCategory.toLowerCase() as LexicalCategory,
        definition: term.definition,
      });
      termIdCounter += 1;
    }

    topics.push({
      dependencies: topicIdCounter > 0 ? [topicIdCounter - 1] : [],
      duration: 7,
      essentialQuestions: topic.essentialQuestions,
      evidenceOfLearning: topic.evidenceOfLearning,
      id: topicIdCounter,
      name: topic.name,
      notes: topic.notes,
      resources: topic.resources,
      start: topicIdCounter * 7,
    });
    topicIdCounter += 1;
  }

  return {
    manifest: "ver2",
    abstract: data.outline.abstract,
    title: data.outline.title,
    gradeLevel: data.outline.grade as unknown as GradeLevel,
    keywords: data.outline.keywords,

    goals,
    terms,
    topics,
    activities: [],
  };
}
