import { NavLink, useNavigate } from "react-router-dom";
import Logo from "@/assets/brand/color-logo.svg";
import { FaKey } from "react-icons/fa6";
import { FaUser } from "react-icons/fa";
import { FormEvent, useState } from "react";
import { SignInStatus, useAuth } from "@/util/auth/AuthContext.tsx";

export function Component() {
  const { signIn } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const username = data.get("username")?.toString();
    const password = data.get("password")?.toString();

    if (username == undefined || password == undefined)
      return setError(new Error("Missing Form Values"));

    setLoading(true);
    const [status, error] = await signIn(username, password);
    setLoading(false);

    if (error || !status) return setError(error);

    switch (status) {
      case SignInStatus.Success:
        return navigate("/narp/app");
      case SignInStatus.Error:
        return { success: false, status, error };
      case SignInStatus.NewPassword:
        return navigate(`./reset/${username}`);
      case SignInStatus.ConfirmRequired:
        return navigate(`./verify/${username}`);
      case SignInStatus.MfaSms:
        return navigate("./mfa/sms");
      case SignInStatus.MfaTotp:
        return navigate("./mfa/totp");
      case SignInStatus.MfaTotpSetup:
        return navigate("./mfa/setup");
      case SignInStatus.MfaSelect:
        return navigate("./mfa/select");
      default:
        return {
          success: false,
          status: SignInStatus.Error,
          error: new Error("Invalid sign in state"),
        };
    }
  }

  return (
    <div className="w-screen h-full min-h-screen flex flex-col items-center justify-center bg-base-100 p-5 gap-10">
      <img src={Logo} className={"size-48 mg-10 text-primary"} alt={"Logo"} />

      <form
        onSubmit={handleSubmit}
        className="p-5 max-w-xs w-full flex flex-col gap-3"
      >
        <div className={"text-xl text-center font-bold my-3"}>Sign In</div>

        <fieldset className={"fieldset gap-0"}>
          <label className={"input floating-label validator"}>
            <span>Username / Email</span>
            <FaUser className={"text-base-content/20"} />
            <input
              type={"text"}
              required
              name="username"
              autoComplete="username"
              placeholder="Username / Email"
            />
          </label>
          <p className={"validator-hint hidden"}>Required</p>
        </fieldset>

        <fieldset className="fieldset gap-0">
          <label className={"input floating-label validator"}>
            <span>Password</span>
            <FaKey className={"text-base-content/20"} />
            <input
              type={"password"}
              required
              name={"password"}
              autoComplete={"current-password"}
              placeholder="Password"
            />
          </label>
          <NavLink className={"fieldset-label link link-info"} to={"./forgot"}>
            Forgot Password
          </NavLink>
          <p className={"validator-hint hidden"}>Required</p>
        </fieldset>

        <button
          disabled={loading}
          type="submit"
          className={`btn ${error ? "btn-error" : "btn-primary"}`}
        >
          {loading ? <div className="loading" /> : <>Sign In</>}
        </button>

        {error ? (
          <div className="text-error text-center">{error?.message}</div>
        ) : (
          <></>
        )}

        <div className="divider m-0">OR</div>

        <NavLink
          className="btn btn-secondary no-animation"
          to="/narp/auth/signup"
        >
          Register
        </NavLink>
        <NavLink className="btn btn-accent no-animation" to="/narp/auth/verify">
          Verify Account
        </NavLink>
      </form>
    </div>
  );
}

Component.displayName = "SignInPage";
