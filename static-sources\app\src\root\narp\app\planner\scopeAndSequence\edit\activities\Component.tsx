import { useLocation, useParams } from "react-router-dom";
import {
  Activity,
  EntityID,
  useEditorContext,
} from "@/root/narp/app/planner/scopeAndSequence/edit/_components/Context.tsx";
import BreadCrumbs from "@/util/components/bread-crumbs/BreadCrumbs.tsx";
import Crumb from "@/util/components/bread-crumbs/Crumb.tsx";
import { LuCalendarCheck } from "react-icons/lu";
import NavBar from "@/root/narp/app/planner/scopeAndSequence/edit/_components/NavBar.tsx";
import { FaSearch } from "react-icons/fa";
import { FormEvent, useMemo, useState } from "react";
import { FaBorderAll, FaListUl } from "react-icons/fa6";
import ListView from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/ListView.tsx";
import GridView from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/GridView.tsx";
import Fuse from "fuse.js";
import EditMenu from "@/root/narp/app/planner/scopeAndSequence/edit/activities/_components/EditMenu.tsx";
import useSessionState from "@/util/hooks/useSessionState.tsx";

export function Component() {
  const { id } = useParams();
  const location = useLocation();
  const {
    topic: { get },
    activity: { entries: allEntries },
    outline: { title },
  } = useEditorContext();

  const [displayMode, setDisplayMode] = useSessionState<"GRID" | "LIST">(
    location.pathname + ":displayMode",
    "GRID"
  );

  // Ensure displayMode is always valid
  const validDisplayMode =
    displayMode === "GRID" || displayMode === "LIST" ? displayMode : "GRID";

  const [searchString, setSearchString] = useState<string>("");
  const [editID, setEditID] = useState<EntityID | null>(null);

  const entries: Activity[] = useMemo(() => {
    if (searchString == "" || !searchString) return allEntries;
    const fuse = new Fuse<{ activity: Activity; parentName: string }>(
      allEntries.map((entry) => {
        return {
          activity: entry,
          parentName:
            entry.parentTopic !== null
              ? get(entry.parentTopic)?.name || "No Parent"
              : "No Parent",
        };
      }),
      {
        keys: [
          "activity.title",
          "activity.description",
          "activity.type",
          "activity.duration",
          "parentName",
        ],
      }
    );
    const results = fuse.search(searchString);
    return results.map((result) => result.item.activity);
  }, [searchString, allEntries]);

  function handleInput(e: FormEvent<HTMLInputElement>) {
    e.preventDefault();
    setSearchString(e.currentTarget.value);
  }

  return (
    <div className="size-full overflow-y-auto overflow-x-hidden flex flex-col py-5 gap-5">
      <EditMenu editID={editID} setEditID={setEditID} />

      <div className="flex flex-col gap-3 px-5">
        <BreadCrumbs>
          <Crumb
            icon={<LuCalendarCheck className="size-4" />}
            label="Planner"
            to="/narp/app/planner"
          />
          <Crumb
            label={title}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}`}
          />
          <Crumb
            label="Activities"
            base
            active={false}
            to={`/narp/app/planner/scopeAndSequence/edit/${id}/activities`}
          />
        </BreadCrumbs>
        <NavBar />
      </div>

      <div className="flex flex-row gap-3 sticky top-5 px-5 z-7">
        <div className="join shadow">
          <button
            onClick={() => setDisplayMode("LIST")}
            type="button"
            className={`btn btn-lg btn-square join-item transition-colors ${validDisplayMode === "LIST" && "btn-primary"}`}
          >
            <FaListUl />
          </button>

          <button
            onClick={() => setDisplayMode("GRID")}
            type="button"
            className={`btn btn-lg btn-square join-item transition-colors ${validDisplayMode === "GRID" && "btn-primary"}`}
          >
            <FaBorderAll />
          </button>
        </div>
        <label className="input input-lg grow shadow">
          <FaSearch className="text-neutral-300" />
          <input
            type="text"
            autoComplete="off"
            autoCapitalize="off"
            placeholder="Search activities"
            onInput={handleInput}
          />
        </label>
      </div>

      {validDisplayMode === "LIST" ? (
        <ListView entries={entries} setEditID={setEditID} />
      ) : (
        <GridView entries={entries} setEditID={setEditID} />
      )}
    </div>
  );
}
