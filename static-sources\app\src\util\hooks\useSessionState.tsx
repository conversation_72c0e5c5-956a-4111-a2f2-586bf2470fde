import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";

// Unicode to Base64
function utob(str: string) {
  return btoa(
    encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (_, p1) {
      return String.fromCharCode(parseInt(p1, 16));
    })
  );
}

// Base64 to Unicode
function btou(str: string) {
  return decodeURIComponent(
    Array.prototype.map
      .call(atob(str), function (c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      })
      .join("")
  );
}

const _STORE_KEY = "SessionStore";

function store(key: string, value: unknown) {
  let cur: { [id: string]: unknown } = {};
  const raw = sessionStorage.getItem(_STORE_KEY);
  if (raw) cur = JSON.parse(btou(raw));
  cur = { ...cur, [key]: value };
  sessionStorage.setItem(_STORE_KEY, utob(JSON.stringify(cur)));
}

function get(key: string): unknown | null {
  const raw = sessionStorage.getItem(_STORE_KEY);
  if (!raw) return null;
  const parsed: { [id: string]: unknown } = JSON.parse(btou(raw));
  return parsed[key];
}

// Simple deep-ish compare good enough for serializable session data.
// Replace with a custom deepEqual if needed for perf or non-JSON-safe types.
function isEqual(a: unknown, b: unknown) {
  return JSON.stringify(a) === JSON.stringify(b);
}

export default function useSessionState<T>(
  key: string,
  initValue: T
): [T, Dispatch<SetStateAction<T>>] {
  const [value, setValue] = useState<T>(
    () => (get(key) as T | null) || initValue
  );

  const valueRef = useRef(value);
  valueRef.current = value;

  // When the key changes, pull from storage; don't push (setter handles that).
  useEffect(() => {
    const stored = get(key) as T | null;
    if (stored !== null && !isEqual(stored, valueRef.current)) {
      setValue(stored as T);
    }
  }, [key]);

  const setState: Dispatch<SetStateAction<T>> = (newValue) => {
    const resolvedValue =
      newValue instanceof Function ? newValue(valueRef.current) : newValue;

    if (isEqual(valueRef.current, resolvedValue)) return;

    store(key, resolvedValue);
    setValue(resolvedValue);
  };

  return [value, setState];
}
