import { FaCalendarDay, FaCalendarDays, FaCalendarWeek } from "react-icons/fa6";
import { useChartContext } from "@/root/narp/app/planner/scopeAndSequence/edit/chart/_components/Context.tsx";

export function ActionBar() {
  const { setScale, scale } = useChartContext();

  return (
    <div className="flex flex-row items-center gap-3 px-5">
      <div className="join join-horizontal shadow">
        <div className="tooltip" data-tip="Day">
          <button
            type="button"
            onClick={() => setScale(1)}
            className={`join-item btn btn-lg btn-square ${scale === 1 && "btn-primary"}`}
          >
            <FaCalendarDay />
          </button>
        </div>

        <div className="tooltip" data-tip="Week">
          <button
            type="button"
            onClick={() => setScale(1 / 7)}
            className={`join-item btn btn-lg btn-square ${scale === 1 / 7 && "btn-primary"}`}
          >
            <FaCalendarWeek />
          </button>
        </div>

        <div className="tooltip" data-tip="Month">
          <button
            type="button"
            onClick={() => setScale(1 / 7 / 4)}
            className={`join-item btn btn-lg btn-square ${scale === 1 / 7 / 4 && "btn-primary"}`}
          >
            <FaCalendarDays />
          </button>
        </div>
      </div>
    </div>
  );
}
